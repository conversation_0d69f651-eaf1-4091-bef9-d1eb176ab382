# External API Endpoints

This document describes the external API endpoints that have been implemented for system integration.

## Authentication

All external API endpoints require an API key for authentication. The API key should be provided in the request headers as `x-api-key`.

Example:
```
x-api-key: your-api-key-here
```

## API Endpoints

### 1. Get Dish Foods

**Endpoint:** `GET /api/external/dish-foods/:dishId`

**Description:** Retrieves all foods associated with a specific dish.

**Parameters:**
- `dishId` (path parameter): The ID of the dish

**Query Parameters:** None

**Response:**
```json
{
  "success": true,
  "data": [...],
  "message": "Successfully retrieved dish foods"
}
```

### 2. Get Dishes for Select

**Endpoint:** `GET /api/external/dishes-for-select`

**Description:** Retrieves a list of dishes for selection purposes.

**Parameters:** None

**Query Parameters:**
- `search` (optional): Search term to filter dishes by name

**Response:**
```json
{
  "success": true,
  "data": [...],
  "message": "Successfully retrieved dishes"
}
```

### 3. Get Food Names

**Endpoint:** `GET /api/external/food-name`

**Description:** Retrieves food names with optional filtering.

**Parameters:** None

**Query Parameters:**
- `search` (optional): Search term to filter foods by name
- `food_type` (optional): Filter foods by type
- `food_year` (optional): Filter foods by year

**Response:**
```json
{
  "success": true,
  "data": [...],
  "message": "Successfully retrieved food names"
}
```

## Implementation

The API endpoints are implemented in the `dishController.js` file with the following methods:
- `getDishFoodsExternal`: Handles requests to `/api/external/dish-foods/:dishId`
- `getDishesForSelectExternal`: Handles requests to `/api/external/dishes-for-select`
- `getFoodNameExternal`: Handles requests to `/api/external/food-name`

The routes in `routes/index.js` now point to these controller methods.

## Setup

1. Set the `API_KEY` environment variable in your `.env` file
2. Use the API key in the `x-api-key` header for all requests to these endpoints

## Error Responses

- `401 Unauthorized`: Missing or invalid API key
- `400 Bad Request`: Missing required parameters
- `500 Internal Server Error`: Server-side errors

## Curl Examples for Postman Import

### 1. Get Dish Foods
```bash
curl -X GET "http://localhost:3000/api/external/dish-foods/1" \
  -H "x-api-key: your-api-key-here"
```

### 2. Get Dishes for Select (without search)
```bash
curl -X GET "http://localhost:3000/api/external/dishes-for-select" \
  -H "x-api-key: your-api-key-here"
```

### 3. Get Dishes for Select (with search)
```bash
curl -X GET "http://localhost:3000/api/external/dishes-for-select?search=pasta" \
  -H "x-api-key: your-api-key-here"
```

### 4. Get Food Names (without filters)
```bash
curl -X GET "http://localhost:3000/api/external/food-name" \
  -H "x-api-key: your-api-key-here"
```

### 5. Get Food Names (with search)
```bash
curl -X GET "http://localhost:3000/api/external/food-name?search=apple" \
  -H "x-api-key: your-api-key-here"
```

### 6. Get Food Names (with all filters)
```bash
curl -X GET "http://localhost:3000/api/external/food-name?search=apple&food_type=fruit&food_year=2023" \
  -H "x-api-key: your-api-key-here"
```
