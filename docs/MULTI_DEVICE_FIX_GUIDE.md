# 🔧 Hướng dẫn khắc phục vấn đề Multi-Device Login

## 🐛 Vấn đề đã gặp phải

**<PERSON><PERSON> tả:** <PERSON><PERSON> bật "cho phép nhiều thiết bị", đăng nhập vào thiết bị thứ 2 vẫn bị logout thiết bị thứ nhất.

**Nguyên nhân:** Logic vẫn đang hoạt động theo chế độ single device, ghi đè token cũ thay vì tạo session mới.

## ✅ Các thay đổi đã thực hiện

### 1. **Cập nhật `jwtService.js`**

**File:** `services/jwtService.js`
**Thay đổi:** Hàm `saveTokenToDatabase`

```javascript
// Trước: Luôn ghi đè token cũ
const updateResult = await commonService.updateRecordTable(
    { jwt_token_id: tokenId, ... },
    { id: userId },
    'user'
);

// Sau: Kiểm tra cài đặt multi-device
const userSettings = await multiDeviceService.getUserSessionSettings(userId);
const allowMultipleDevices = userSettings ? userSettings.allow_multiple_devices : 1;

if (allowMultipleDevices) {
    // Chế độ multi-device: chỉ tạo session mới, không ghi đè token cũ
    const sessionResult = await multiDeviceService.createSession(userId, tokenId, deviceInfo, ipAddress);
    // Chỉ cập nhật last_login, không ghi đè jwt_token_id
} else {
    // Chế độ single device: xóa session cũ và ghi đè token
    // Giữ nguyên logic cũ
}
```

### 2. **Cập nhật `multiDeviceService.js`**

**File:** `services/multiDeviceService.js`
**Thay đổi:** Hàm `createSession`

```javascript
// Trước: Luôn xóa session cũ
if (currentSessions.length >= userSettings.max_sessions) {
    // Xóa session cũ nhất
}

// Sau: Kiểm tra chế độ multi-device
if (allowMultipleDevices) {
    // Chế độ multi-device: chỉ xóa khi vượt quá giới hạn
    if (currentSessions.length >= maxSessions) {
        // Xóa session cũ nhất
    }
} else {
    // Chế độ single device: logout tất cả session cũ
    for (const session of currentSessions) {
        await multiDeviceService.logoutSession(session.jwt_token_id);
    }
}
```

### 3. **Cập nhật middleware xác thực**

**Files:** `app.js`, `services/commonService.js`
**Thay đổi:** Chỉ kiểm tra bảng `user_sessions`

```javascript
// Trước: Kiểm tra trường jwt_token_id trong bảng user
const user = await mainService.getAllDataTable('user', { 
    id: req.user.id,
    jwt_token_id: req.user.tokenId 
});

// Sau: Chỉ kiểm tra bảng user_sessions
const sessionResult = await mainService.getAllDataTable('user_sessions', { 
    user_id: req.user.id,
    jwt_token_id: req.user.tokenId,
    is_active: 1
});
```

## 🧪 Cách test

### 1. **Chạy script database**
```bash
# Chạy file database/multi_device_schema.sql
```

### 2. **Khởi động lại ứng dụng**
```bash
npm start
```

### 3. **Test thủ công**
```bash
# Chạy test tự động
node test-multi-device-fixed.js
```

### 4. **Test thủ công**
1. Đăng nhập trên browser thứ nhất
2. Mở browser thứ hai (hoặc incognito)
3. Đăng nhập cùng tài khoản
4. Kiểm tra cả hai browser vẫn hoạt động
5. Truy cập `/devices-page` để xem danh sách thiết bị

## 🔍 Kiểm tra database

### 1. **Kiểm tra bảng user_sessions**
```sql
SELECT * FROM user_sessions 
WHERE user_id = [YOUR_USER_ID] 
AND is_active = 1 
ORDER BY login_at DESC;
```

### 2. **Kiểm tra cài đặt user**
```sql
SELECT * FROM user_session_settings 
WHERE user_id = [YOUR_USER_ID];
```

### 3. **Kiểm tra bảng user**
```sql
SELECT id, email, jwt_token_id, last_login 
FROM user 
WHERE id = [YOUR_USER_ID];
```

## 📊 Kết quả mong đợi

### **Khi cho phép nhiều thiết bị (`allow_multiple_devices = 1`):**
- ✅ Đăng nhập thiết bị 2 không logout thiết bị 1
- ✅ Cả hai thiết bị đều hoạt động bình thường
- ✅ Bảng `user_sessions` có nhiều bản ghi `is_active = 1`
- ✅ Trường `jwt_token_id` trong bảng `user` không bị ghi đè

### **Khi không cho phép nhiều thiết bị (`allow_multiple_devices = 0`):**
- ✅ Đăng nhập thiết bị 2 sẽ logout thiết bị 1
- ✅ Chỉ có 1 session active tại một thời điểm
- ✅ Trường `jwt_token_id` trong bảng `user` được cập nhật

## 🚨 Lưu ý quan trọng

### 1. **Backward Compatibility**
- Hệ thống vẫn tương thích với single device mode
- Trường `jwt_token_id` trong bảng `user` vẫn được sử dụng cho single device

### 2. **Performance**
- Kiểm tra session trong bảng `user_sessions` có thể chậm hơn một chút
- Đã thêm index để tối ưu hiệu suất

### 3. **Security**
- Mỗi session có token ID duy nhất
- Session timeout được kiểm tra tự động
- Có thể logout từng thiết bị riêng lẻ

## 🔧 Troubleshooting

### **Vấn đề 1: Vẫn bị logout thiết bị cũ**
**Nguyên nhân:** Cài đặt `allow_multiple_devices` chưa được bật
**Giải pháp:** Kiểm tra và cập nhật cài đặt trong `/devices-page`

### **Vấn đề 2: Không thể đăng nhập**
**Nguyên nhân:** Database chưa được cập nhật
**Giải pháp:** Chạy lại script `multi_device_schema.sql`

### **Vấn đề 3: Session bị mất**
**Nguyên nhân:** Session timeout quá ngắn
**Giải pháp:** Tăng thời gian timeout trong cài đặt

## 📞 Support

Nếu vẫn gặp vấn đề:
1. Kiểm tra log lỗi trong console
2. Chạy test tự động: `node test-multi-device-fixed.js`
3. Kiểm tra database theo hướng dẫn trên
4. Liên hệ team development

---

**Lưu ý:** Sau khi áp dụng các thay đổi, hãy test kỹ lưỡng trước khi deploy production. 