var passport        = require('passport'),
    JwtStrategy     = require('passport-jwt').Strategy,
    ExtractJwt      = require('passport-jwt').ExtractJwt,
    userService     = require('./../admin/models/userModel'),
    webService      = require('./../web/models/webModel');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

module.exports = function(passport) {
    // JWT Strategy
    const jwtOptions = {
        jwtFromRequest: ExtractJwt.fromExtractors([
            ExtractJwt.fromAuthHeaderAsBearerToken(),
            (req) => {
                if (req.cookies && req.cookies.jwt_token) {
                    return req.cookies.jwt_token;
                }
                return null;
            }
        ]),
        secretOrKey: JWT_SECRET
    };

    passport.use(new JwtStrategy(jwtOptions, async function(jwt_payload, done) {
        try {
            // Ki<PERSON><PERSON> tra token c<PERSON> hợ<PERSON> lệ trong database không
            const jwtService = require('../services/jwtService');
            const isValidInDB = await jwtService.validateTokenInDatabase(jwt_payload.userId, jwt_payload.tokenId);
            
            if (!isValidInDB) {
                return done(null, false);
            }

            // Lấy thông tin user từ database
            userService.getUserById(jwt_payload.userId, function (err, resUser, fields) {
                if (err) {
                    return webService.addToLogService(err, 'passport JWT getUserById').then(log_id =>{
                        done(err, false);
                    });
                }
                if (resUser !== undefined && resUser[0] !== undefined) {
                    const user = resUser[0];
                    user.role_id = jwt_payload.role_id || [];
                    user.isAdmin = jwt_payload.isAdmin || false;
                    return done(null, user);
                } else {
                    return done(null, false);
                }
            });
        } catch (error) {
            console.error('JWT Strategy error:', error);
            return done(error, false);
        }
    }));
}
