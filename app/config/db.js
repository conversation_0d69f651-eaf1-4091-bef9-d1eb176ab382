const dotenv = require('dotenv');
const mysql = require('mysql2');

// Load environment variables
dotenv.config();

const {
    DATABASE_USER,
    DATABASE_PASSWORD,
    DATABASE_URL,
    DATABASE_HOST = 'localhost',
    DATABASE_CONNECTION_LIMIT = 10,
} = process.env;

// Validate required environment variables
if (!DATABASE_USER || !DATABASE_URL) {
    throw new Error(
        'Missing database configuration in .env file: DATABASE_USER, DATABASE_PASSWORD, and DATABASE_URL are required.'
    );
}

const state = {
    pool: null,
    mode: null,
};

const db = {
    /**
     * Connects to the MySQL database using a connection pool.
     * @param {string} mode - The mode of operation (e.g., 'production', 'development').
     */
    connect(mode) {
        if (state.pool) {
            console.log('✅ Database connection pool already exists.');
            return;
        }

        try {
            state.pool = mysql.createPool({
                host: <PERSON>AT<PERSON>ASE_HOST,
                user: DATABASE_USER,
                password: DATABASE_PASSWORD,
                database: DATABASE_URL,
                waitForConnections: true,
                connectionLimit: parseInt(DATABASE_CONNECTION_LIMIT, 10) || 10,
                queueLimit: 0,
            });

            // Test connection
            state.pool.getConnection((err, connection) => {
                if (err) {
                    console.error('❌ Error getting connection from pool:', err.message);
                } else {
                    console.log('✅ Database connection pool created successfully.');
                    connection.release();
                }
            });

            state.mode = mode;
        } catch (err) {
            console.error('❌ Error creating database connection pool:', err);
            throw new Error(`Failed to connect to the database: ${err.message}`);
        }
    },

    /**
     * Retrieves the database connection pool.
     * @returns {mysql.Pool} - The MySQL connection pool.
     */
    get() {
        if (!state.pool) {
            throw new Error('❌ Database connection pool has not been created. Call connect() first.');
        }
        return state.pool;
    },
};

module.exports = db;

