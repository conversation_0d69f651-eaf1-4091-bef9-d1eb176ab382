const express = require('express');
const router = express.Router();
const jwtAuth = require('../../middleware/jwtAuth');
const multiDeviceService = require('../service/multiDeviceService');

// L<PERSON>y danh sách thiết bị đang đăng nhập
router.get('/', jwtAuth.isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const devices = await multiDeviceService.getActiveSessions(userId, req);
        res.json({ success: true, data: { devices } });
    } catch (error) {
        res.json({ success: false, message: 'Lỗi khi lấy danh sách thiết bị' });
    }
});

// Lấy cài đặt session
router.get('/settings', jwtAuth.isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const settings = await multiDeviceService.getUserSessionSettings(userId);
        res.json({ success: true, data: settings });
    } catch (error) {
        res.json({ success: false, message: 'Lỗi khi lấy cài đặt session' });
    }
});

// Cập nhật cài đặt session
router.post('/settings', jwtAuth.isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const { max_sessions, session_timeout_hours, allow_multiple_devices } = req.body;
        await multiDeviceService.updateUserSessionSettings(userId, {
            max_sessions,
            session_timeout_hours,
            allow_multiple_devices
        });
        res.json({ success: true });
    } catch (error) {
        res.json({ success: false, message: 'Lỗi khi cập nhật cài đặt session' });
    }
});

// Logout một thiết bị cụ thể
router.post('/logout', jwtAuth.isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const { tokenId } = req.body;
        const result = await multiDeviceService.logoutSession(userId, tokenId);
        res.json({ success: result });
    } catch (error) {
        res.json({ success: false, message: 'Lỗi khi logout thiết bị' });
    }
});

// Logout tất cả thiết bị khác
router.post('/logout-all-others', jwtAuth.isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;
        const currentTokenId = req.user.tokenId;
        const result = await multiDeviceService.logoutAllOtherSessions(userId, currentTokenId);
        res.json({ success: result });
    } catch (error) {
        res.json({ success: false, message: 'Lỗi khi logout các thiết bị khác' });
    }
});

// Trang giao diện quản lý thiết bị
router.get('/page', jwtAuth.isAuthenticated, (req, res) => {
    // Tạo filter object để tương thích với view
    const filter = {
        search: {
            keyword: '',
            skip: 0,
            take: 10,
            page: 1,
            hospital_ids: []
        },
        requestUri: '',
        error: [],
        hospitals: []
    };
    
    res.render('devices/devices', { 
        user: req.user, 
        errors: [],
        filter: filter,
        link:'devices',
        title: 'Quản lý thiết bị đăng nhập'
    });
});

module.exports = router; 