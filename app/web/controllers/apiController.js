var express         = require('express'),
    router          = express.Router(),
    axios           = require('axios'),
    webService      = require('../models/webModel'),
    logService      = require('../../admin/models/logModel');

// C<PERSON>u hình URL của project benh-nhan
const BENH_NHAN_API_BASE = process.env.BENH_NHAN_API_URL || 'http://localhost:3001';

// Middleware để kiểm tra authentication
function requireAuth(req, res, next) {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: "<PERSON>ui lòng đăng nhập để thực hiện chức năng này!"
        });
    }
    next();
}

// API lấy danh sách thực phẩm từ project benh-nhan
router.get('/foods', requireAuth, async function(req, res) {
    try {
        const search = req.query.search || '';
        const food_type = req.query.food_type || '';
        const food_year = req.query.food_year || '';

        // Gọi API của project benh-nhan
        const response = await axios.get(`${BENH_NHAN_API_BASE}/api/external/food-name`, {
            params: {
                search: search,
                food_type: food_type,
                food_year: food_year
            },
            headers: {
                'x-api-key': process.env.API_KEY
            },
            timeout: 10000
        });
        console.log('response.data foods',response.data);
        if (response.data && response.data.success) {
            res.json({
                success: true,
                message: "Thành công",
                data: response.data.data
            });
        } else {
            res.json({
                success: false,
                message: "Không thể lấy dữ liệu thực phẩm từ hệ thống benh-nhan",
                data: []
            });
        }
    } catch (error) {
        console.error('Error fetching foods from benh-nhan:', error.message);
        logService.create(req, `API foods error: ${error.message}`);

        res.json({
            success: false,
            message: "Lỗi kết nối đến hệ thống benh-nhan: " + error.message,
            data: []
        });
    }
});

// API lấy danh sách món ăn từ project benh-nhan
router.get('/dishes', requireAuth, async function(req, res) {
    try {
        const search = req.query.search || '';

        // Gọi API của project benh-nhan
        const response = await axios.get(`${BENH_NHAN_API_BASE}/api/external/dishes-for-select`, {
            params: {
                search: search
            },
            headers: {
                'x-api-key': process.env.API_KEY
            },
            timeout: 10000
        });
        console.log('response.data dishes',response.data);
        if (response.data && response.data.success) {
            res.json({
                success: true,
                message: "Thành công",
                data: response.data.data
            });
        } else {
            res.json({
                success: false,
                message: "Không thể lấy dữ liệu món ăn từ hệ thống benh-nhan",
                data: []
            });
        }
    } catch (error) {
        console.error('Error fetching dishes from benh-nhan:', error.message);
        logService.create(req, `API dishes error: ${error.message}`);

        res.json({
            success: false,
            message: "Lỗi kết nối đến hệ thống benh-nhan: " + error.message,
            data: []
        });
    }
});

// API lấy chi tiết thực phẩm của món ăn từ project benh-nhan
router.get('/dish-foods/:id', requireAuth, async function(req, res) {
    try {
        const dishId = req.params.id;
        
        // Gọi API của project benh-nhan
        const response = await axios.get(`${BENH_NHAN_API_BASE}/api/external/dish-foods/${dishId}`, {
            headers: {
                'x-api-key': process.env.API_KEY
            },
            timeout: 10000
        });
        console.log('response.data dish foods',response.data);
        if (response.data && response.data.success) {
            res.json({
                success: true,
                message: "Thành công",
                data: response.data.data
            });
        } else {
            res.json({
                success: false,
                message: "Không thể lấy dữ liệu chi tiết món ăn từ hệ thống benh-nhan",
                data: []
            });
        }
    } catch (error) {
        console.error('Error fetching dish foods from benh-nhan:', error.message);
        logService.create(req, `API dish-foods error: ${error.message}`);

        res.json({
            success: false,
            message: "Lỗi kết nối đến hệ thống benh-nhan: " + error.message,
            data: []
        });
    }
});
module.exports = router;