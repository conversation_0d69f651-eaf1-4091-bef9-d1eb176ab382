<div class="col">
    <div class="box">
        <div class="box-header">
            <h2 class="box-title"><PERSON>h s<PERSON>ch khám <small class="text-icon fw-6">(<%=paginator.totalItem%>)</small></h2>
            <div class="paginate fs-13px ms-auto d-none d-md-flex">
                <%- include('../layout/pagination') %>
            </div>
        </div>
        <div class="box-body p-2">
            <div class="row ms-0 me-0">
                <div class="col-3 col-md-2 fw-bold p-1"><PERSON><PERSON><PERSON> khám</div>
                <div class="col-5 col-md-5 fw-bold p-1">Address</div>
                <div class="col-4 col-md-5 fw-bold p-1">Chẩn đoán</div>
            </div>
            <% if (listExamine.length > 0){%>
                <% for (i = 0;i < listExamine.length;i++){%>
                    <div class="row ms-0 me-0">
                        <div class="col-3 col-md-2 fs-13px p-1 text-primary">
                            <a onclick="viewDetailExamine('<%=listExamine[i].id%>')" style="cursor: pointer;">
                                <%=moment(listExamine[i].created_at).format("HH:mm:ss")%>
                                <br>
                                <small class="text-body-2">
                                    <%=moment(listExamine[i].created_at).format("DD/MM/YYYY")%>
                                </small>
                            </a>
                        </div>
                        <div class="col-5 col-md-5 fs-13px p-1"><%=listExamine[i].cus_address%></div>
                        <div class="col-4 col-md-5 fs-13px p-1"><%=listExamine[i].diagnostic%></div>
                    </div>
                <%}%>
            <%} else {%>
                <div class="col-12 my-3" style="text-align: center;"><b>Không có dữ liệu!</b></div>
            <% } %>
            <div class="paginate fs-13px mx-auto">
                <%- include('../layout/pagination') %>
            </div>
        </div>
    </div>
</div>