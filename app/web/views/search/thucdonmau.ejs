<div>
    <% if(menuExample.length > 0){ %>
    <h3 class="box-title-break mb-3">Thự<PERSON> đơn mẫu</h3>
    <div class="row g-2 align-items-center mb-3">
        <div class="col-12 col-md-6 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_id">Chọn menu</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <select class="form-select" id="menu_id">
                        <% for(let item of menuExample){ %>
                            <option value="<%=item.id%>"><%=item.name%></option>
                        <% } %>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-0 mx-0 my-0" style="background-color: #f8fafc;border-bottom: 1px solid #e6ecf2;border-top: 1px solid #e6ecf2;font-size: 16px;">
        <div class="col-1 px-2 py-2 text-center" style="font-weight: 700;">Giờ</div>
        <div class="col-7 px-2 py-2 text-center" style="font-weight: 700;">Thực phẩm</div>
        <div class="col-4 px-2 py-2 text-center" style="font-weight: 700;">Weight(g)</div>
    </div>
    <div id="list_menu_example" style="font-size: 16px;" class="mb-3">

    </div>
    <% } %>
    <div class="row g-2 align-items-center mb-4">
        <div class="col-12 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_example_note">Ghi chú</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title" id="menu_example_note"><%= examine.cus_bc%></div>
                </div>
            </div>
        </div>
    </div>
    <h3 class="box-title-break mb-3">Thực phẩm thay thế tương đương</h3>
    <div class="row g-2 align-items-center">
        <% if(dataAlternativeFood.length > 0){ %>
            <% for(let item of dataAlternativeFood){ %>
                <div class="col-6 ps-3"><%= item.food_main %></div>
                <div class="col-6 ps-3"><%= item.food_replace %></div>
            <% } %>
        <% } %>
    </div>
</div>