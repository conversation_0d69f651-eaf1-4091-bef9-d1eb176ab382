<div class="form-horizontal container">
    <h3 class="box-title-break">Thông tin chung</h3>
    <div class="row g-2 align-items-center">
        <label class="col-form-label col-md-auto fw-bold" for="diagnostic"><PERSON><PERSON><PERSON></label>
        <div class="col-form-body col-md">
          <div class="form-control-has-addon">
                <div class="form-control"><%= examine.diagnostic%></div>
          </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_length">Cao (m)</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title"><%= examine.cus_length%></div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cntc">CNTC (kg)</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title"><%= examine.cus_cntc%></div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cnht">CNHT (kg)</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title"><%= examine.cus_cnht%></div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_bmi">BMI</label>
            <div class="col-form-body col-md w-100">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title"><%= examine.cus_bmi%></div>
                </div>
            </div>
        </div>
    </div>
    <h3 class="box-title-break">Khám lâm sàng</h3>
    <div class="row">
        <div class="col-form-body col-md">
            <div class="form-control-has-addon">
                <div class="form-control form-control-title"><%= examine.clinical_examination%></div>
            </div>
          </div>
    </div>
    <h3 class="box-title-break">Kết quả xét nghiệm</h3>
    <div class="row g-2 align-items-center">
        <%if(examine.erythrocytes){%>
            <div class="col-12 col-md-4 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="erythrocytes">Hồng cầu (T/L)</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.erythrocytes%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
        <%if(examine.cus_bc){%>
            <div class="col-12 col-md-4 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_bc">BC (G/L)</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_bc%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
        <%if(examine.cus_tc){%>
            <div class="col-12 col-md-4 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_tc">TC (T/L)</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_tc%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
    </div>
    <div class="row g-2 align-items-center">
        <%if(examine.cus_albumin){%>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="cus_albumin">Albumin (G/L)</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_albumin%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
        <%if(examine.cus_nakcl){%>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_nakcl">Na+/K+/Cl-</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_nakcl%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
    </div>
    <div class="row g-2 align-items-center">
        <%if(examine.cus_astaltggt){%>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="cus_astaltggt">AST/ALT/GGT (U/L)</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_astaltggt%></div>
                    </div>
                </div>
            </div>
        <%}%>
        
        <%if(examine.cus_urecreatinin){%>
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_urecreatinin">Ure/Creatinin</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_urecreatinin%></div>
                    </div>
                </div>
            </div>
        <%}%>
    </div>
    <%if(examine.cus_bilirubin){%>
        <div class="row g-2 align-items-center">
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="cus_bilirubin">Bilirubin TP/TT</label>
                <div class="col-form-body col-md w-100">
                    <div class="form-control-has-addon">
                        <div class="form-control form-control-title"><%= examine.cus_bilirubin%></div>
                    </div>
                </div>
            </div>
        </div>
    <%}%>

    <%if(examine.exa_note){%>
        <div class="row g-2 align-items-center">
            <label class="col-form-label col-md-auto fw-bold" for="diagnostic">Ghi chú</label>
            <div class="col-form-body col-md">
                <div class="form-control-has-addon">
                    <div class="form-control form-control-title"><%= examine.exa_note%></div>
                </div>
            </div>
        </div>
    <%}%>
    
    <h3 class="box-title-break">Lời khuyên dinh dưỡng</h3>
    <div class="row g-2 align-items-center">
        <div class="row align-items-center">
            <div class="col-6 col-md-2 fw-bold p-2">Nhóm TP</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP nên dùng</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP hạn chế dùng</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP không nên dùng</div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm glucid</div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.glucid_should_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.glucid_limited_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.glucid_should_not_use%></div>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm protein</div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.protein_should_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.protein_limited_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.protein_should_not_use%></div>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm lipid</div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.lipid_should_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.lipid_limited_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.lipid_should_not_use%></div>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm VTM & CK</div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.vitamin_ck_should_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.vitamin_ck_limited_use%></div>
            </div>
            <div class="col-12 col-md-3 p-2">
                <div class="form-control text-box single-line"><%=examine.vitamin_ck_should_not_use%></div>
            </div>
        </div>
    </div>
    <% if(examine.active_mode_of_living){ %>
    <h3 class="box-title-break">Chế độ vận động sinh hoạt</h3>
    <div class="row">
        <div class="col-form-body col-md">
            <div class="form-control-has-addon">
                <div class="form-control text-box single-line"><%=examine.active_mode_of_living%></div>
            </div>
        </div>
    </div>
    <% } %>
    <% if(prescriptionExamine.length > 0){ %>
        <h3 class="box-title-break">Bổ sung</h3>
        <div class="row">
            <div class="col-2">STT</div>
            <div class="col-6">Tên thuốc</div>
            <div class="col-2">Số lượng</div>
            <div class="col-2">Đơn vị tính</div>
        </div>
        <% for(let item of prescriptionExamine){ %>
            <div class="row">
                <div class="col-2"><%=item.stt%></div>
                <div class="col-6 fs-6">
                    <div><%=item.name%></div>
                    <div><%=item.note%></div>
                </div>
                <div class="col-2 fs-6 text-red"><%=item.total%></div>
                <div class="col-2 fs-13px text-primary"><%=item.unit%></div>
            </div>
        <% } %>
    <% } %>
</div>
