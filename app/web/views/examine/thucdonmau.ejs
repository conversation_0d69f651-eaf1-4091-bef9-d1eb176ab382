<form id="thucdonmau_form" class="form-horizontal">
    <h3 class="box-title-break">Th<PERSON><PERSON> đơn mẫu</h3>
    <input type="hidden" class="form-control form-control-title p-1" id="energy_food" placeholder="Energy" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="protein_food" placeholder="Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="animal_protein" placeholder="Animal Protein" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="lipid_food" placeholder="Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="unanimal_lipid" placeholder="Unanimal Lipid" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="carbohydrate" placeholder="Carbohydrate" readonly>
    <input type="hidden" class="form-control form-control-title p-1" id="status_examine" value="<%=examine.status%>">

    <!-- <PERSON> chọn thực đơn mẫu và thực đơn đã chọn -->
    <div class="row g-2">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-12 col-md-6">
                <div class="card shadow">
                    <div class="card-header d-flex flex-row align-items-center justify-content-between">
                        <h6 class="text-primary mb-0">Chọn thực đơn</h6>
                        <div>
                            <a onclick="addMenu()" class="btn btn-primary btn-circle" title="Tạo mới">
                                <svg class="iconsvg-edit">
                                    <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <div style="flex-grow:1;">
                                <select class="form-select" id="menuExample_id" data-plugin="select2" data-placeholder="Chọn thực đơn mẫu">
                                    <option></option>
                                    <% if(menuExample.length > 0){ %>
                                        <% for (let me of menuExample){ %>
                                            <option value="<%= me.id %>"><%= me.name_menu %></option>
                                        <%}%>
                                    <%}%>
                                </select>
                            </div>
                            <button title="Thêm thực đơn đã chọn" class="btn btn-danger" type="button" onclick="chooseMenuExample()">Thêm</button>
                        </div>
                    </div>
                </div>
            </div>
        <% } %>
        <div class="col-12 col-md-6">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="text-primary mb-0">Thực đơn đã chọn</h6>
                </div>
                <div class="card-body">
                    <div class="w-100">
                        <div class="form-control-has-addon">
                            <select class="form-select" id="menu_id" data-plugin="select2" data-placeholder="Chọn thực đơn"></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Card chính cho thực đơn -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="text-primary" id="name_menu_text">Tên thực đơn</h6>
        </div>
        <div class="card-body px-0">
            <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                <!-- Card thêm thực phẩm -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="text-primary mb-0">Thêm thực phẩm</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2 align-items-center justify-content-center mb-4">
                            <div class="col-12 col-md-6 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold" for="menuTime_id">Chọn giờ ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <select class="form-select" id="menuTime_id" data-plugin="select2" data-placeholder="Chọn giờ ăn">
                                            <option></option>
                                            <% if(menuTime.length > 0){ %>
                                                <% for (let mt of menuTime){ %>
                                                    <option value="<%= mt.id %>"><%= mt.name %></option>
                                                <%}%>
                                            <%}%>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- Filter cho thực phẩm -->
                            <div class="col-12 col-md-3 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold" for="food_type" title="Loại thực phẩm">Loại TP</label>
                                <div class="col-form-body col-md w-100">
                                    <select id="food_type" name="food_type" class="form-select">
                                        <option value="">Tất cả</option>
                                        <option value="raw">Sống</option>
                                        <option value="cooked">Chín ĐP</option>
                                        <option value="cooked_vdd">Chín VDD</option>
                                        <option value="milk">Sữa</option>
                                        <option value="ddd">Dịch DD</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-md-3 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold" for="food_year" title="Năm dữ liệu">Năm</label>
                                <div class="col-form-body col-md w-100">
                                    <select id="food_year" name="food_year" class="form-select">
                                        <option value="">Tất cả</option>
                                        <option value="2000">2000</option>
                                        <option value="2017">2017</option>
                                        <option value="2025">2025</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row g-2 align-items-center">
                            <div class="col-12 col-md-8 d-flex align-items-center">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="food_name">Thực phẩm</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <select class="form-select" id="food_name" data-plugin="select2" data-placeholder="Chọn thực phẩm"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-4">
                                <div class="input-group">
                                    <input type="number" class="form-control form-control-title p-1"
                                        id="weight_food" placeholder="Khối lượng"
                                        data-initial-value="0">
                                    <button class="btn btn-primary" type="button"
                                        onclick="addFoodToMenu()"
                                        title="Thêm thực phẩm vào thực đơn">Thêm</button>
                                </div>
                            </div>
                        </div>
                        <div class="row g-2 align-items-center d-none" id="food_note_container">
                            <div class="col-12 col-md-12 ps-4" id="food_note"></div>
                        </div>
                    </div>
                </div>

                <!-- Card chọn món ăn -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="text-primary mb-0">Thêm món ăn vào thực đơn</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2 align-items-center mb-4">
                            <div class="col-12 col-md-6 d-flex justify-content-center">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="dish_menuTime_id">Chọn giờ ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <select class="form-select" id="dish_menuTime_id" data-plugin="select2" data-placeholder="Chọn giờ ăn">
                                            <option></option>
                                            <% if(menuTime.length > 0){ %>
                                                <% for (let mt of menuTime){ %>
                                                    <option value="<%= mt.id %>"><%= mt.name %></option>
                                                <%}%>
                                            <%}%>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-2 align-items-center">
                            <div class="col-sm-8 col-md-10 d-flex justify-content-center">
                                <label class="col-form-label col-md-auto fw-bold mt-2" for="dish_name">Món ăn</label>
                                <div class="col-form-body col-md w-100">
                                    <div class="form-control-has-addon">
                                        <select class="form-select" id="dish_name" data-plugin="select2" data-placeholder="Chọn món ăn"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4 col-md-2">
                                <div class="input-group">
                                    <button class="btn btn-success" type="button"
                                        onclick="addDishToMenu()"
                                        title="Thêm món ăn vào thực đơn">Thêm món ăn</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } %>

            <!-- Bảng thực đơn chi tiết -->
            <div class="card shadow mb-4">
                <div class="card-header d-flex flex-row align-items-center justify-content-between">
                    <h6 class="text-primary mb-0">Thực đơn chi tiết</h6>
                    <button class="btn btn-info" type="button" onclick="showTableConfigModal()">
                        <i class="fas fa-cog"></i>
                        Cấu hình cột hiển thị
                    </button>
                </div>
                <div class="card-body px-0">
                    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_menu" style="width: 100%;display: none;">
                        <div class="table-responsive-inner px-3">
                            <table class="table-2 table table-pe-x-3 mb-0 align-middle table-ds-booking table-bordered table-hover">
                                <thead class="text-center">
                                    <!-- Header sẽ được tạo động bởi JavaScript -->
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row g-2 align-items-center">
                        <div class="col-12 d-flex">
                            <label class="col-form-label col-md-auto fw-bold mt-2" for="menu_example_note">Ghi chú</label>
                            <div class="col-form-body col-md w-100">
                                <div class="form-control-has-addon">
                                    <textarea class="form-control" id="menu_example_note" placeholder="Ghi chú" <%=(examine.status == 4 || (examine.status == 3 && isDetail == true)) ? 'readonly' : '' %>></textarea>
                                    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#menu_example_note')">
                                            <svg class="iconsvg-close">
                                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                                            </svg>
                                        </button>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-2 justify-content-center mt-0">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveExamine(2, 0, {})" title="Lưu phiếu khám">
                    <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#save"></use>
                    </svg>Lưu
                </button>
            </div>
        <% } %>
        <div class="col-6 col-md-auto">
            <button class="btn btn-action-edit box-btn w-100 text-uppercase" type="button" onclick="exportMenuExample()" title="Tải thực đơn mẫu">
                <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                    <use xlink:href="/public/content/images/sprite.svg#download"></use>
                </svg>Tải thực đơn
            </button>
        </div>
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <div class="col-6 col-md-auto">
            <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveMenuExampleFromMenu()" title="Lưu thành thực đơn mẫu">
                <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                    <use xlink:href="/public/content/images/sprite.svg#save"></use>
                </svg>Lưu mẫu
            </button>
        </div>
        <% } %>
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList()" title="Hủy thao tác sửa phiếu khám">
                    <svg class="iconsvg-close flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                    </svg>Huỷ
                </button>
            </div>
        <% } %>
    </div>
</form>


<!-- Modal cấu hình hiển thị bảng -->
<div class="modal fade" id="modal-table-config" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cấu hình cột hiển thị</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="tableConfigModalBody">
                <!-- Dynamic content will be injected here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="applyTableConfig()">Áp dụng</button>
            </div>
        </div>
    </div>
</div>

<div id="modal_confirm_box"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script>
    // Khởi tạo dữ liệu global cho thực đơn mẫu
    if (typeof dataExamine !== 'undefined') {
        dataExamine.menuExample = <%- JSON.stringify(menuExample) %>;
        dataExamine.listMenuTime = <%- JSON.stringify(menuTime) %>;

        // Khởi tạo header bảng với cấu hình mặc định khi trang load
        $(document).ready(function() {
            updateTableHeader();
        });
    }
</script>
