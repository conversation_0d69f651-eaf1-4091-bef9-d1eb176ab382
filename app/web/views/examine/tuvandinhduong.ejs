<form id="tuvandinhduong_form" class="form-horizontal">
    <h3 class="box-title-break">Khám dinh dưỡng</h3>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="cus_fat">Tỉ lệ mỡ(%)</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_fat" class="form-control form-control-title" id="cus_fat" placeholder="Tỉ lệ mỡ" value="<%= examine.cus_fat%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_fat')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_fat%></div>
                <% } %>
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_water">Tỉ lệ nước(%)</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_water" class="form-control form-control-title" id="cus_water" placeholder="Tỉ lệ nước" value="<%= examine.cus_water%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_water')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_water%></div>
                <% } %>
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_visceral_fat">Mỡ nội tạng</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_visceral_fat" class="form-control form-control-title" id="cus_visceral_fat" placeholder="Mỡ nội tạng" value="<%= examine.cus_visceral_fat%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_visceral_fat')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_visceral_fat%></div>
                <% } %>
            </div>
        </div>
    </div>
    <div class="row g-2 align-items-center">
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2 form-label-large" for="cus_chcb">CHCB (kcal)</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_chcb" class="form-control form-control-title" id="cus_chcb" placeholder="CHCB (kcal)" value="<%= examine.cus_chcb%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_chcb')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_chcb%></div>
                <% } %>
            </div>
        </div>
        <div class="col-12 col-md-4 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_bone_weight">Cân nặng xương(Kg)</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" data-type="number" name="cus_bone_weight" class="form-control form-control-title" id="cus_bone_weight" placeholder="Cân nặng xương" value="<%= examine.cus_bone_weight%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_bone_weight')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_bone_weight%></div>
                <% } %>
            </div>
        </div>
        <!-- <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_waist">Vòng eo</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" name="cus_waist" class="form-control form-control-title" id="cus_waist" placeholder="Vòng eo" value="<%= examine.cus_waist%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_waist')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_waist%></div>
                <% } %>
            </div>
        </div> -->
        <!-- <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_butt">Vòng mông</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" name="cus_butt" class="form-control form-control-title" id="cus_butt" placeholder="Vòng mông" value="<%= examine.cus_butt%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_butt')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_butt%></div>
                <% } %>
            </div>
        </div> -->
        <!-- <div class="col-12 col-md-3 d-flex">
            <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_cseomong">Cs eo/mông</label>
            <div class="col-form-body col-md">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <div class="form-control-has-addon">
                        <input type="text" name="cus_cseomong" class="form-control form-control-title" id="cus_cseomong" placeholder="Cs eo/mông" value="<%= examine.cus_cseomong%>">
                        <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#cus_cseomong')">
                            <svg class="iconsvg-close">
                                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                            </svg>
                        </button>
                    </div>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.cus_cseomong%></div>
                <% } %>
            </div>
        </div> -->
    </div>
    <h3 class="box-title-break">Lời khuyên dinh dưỡng</h3>
    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <div class="row g-2 align-items-center">
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_butt">Chọn mẫu</label>
                <div class="col-form-body col-md">
                    <div class="form-control-has-addon">
                        <select class="form-select" name="nutrition_advice_id" id="nutrition_advice_id" data-plugin="select2" data-placeholder="Lời khuyên dinh dưỡng">
                            <option></option>
                            <% if(nutritionAdvice.length > 0){ %>
                                <% for (let nu_advice of nutritionAdvice){ %>
                                    <option <%=nu_advice.id == examine.nutrition_advice_id ? 'selected' : ''%> value="<%= nu_advice.id %>"><%= nu_advice.name %></option>
                                <%}%>   
                            <%}%>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    <% } %>
    <div class="row g-2 align-items-center" id="nutrition_advice">
        <div class="row align-items-center">
            <div class="col-6 col-md-2 fw-bold p-2">Nhóm TP</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP nên dùng</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP hạn chế dùng</div>
            <div class="col-6 col-md-3 fw-bold p-2">TP không nên dùng</div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm glucid</div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" id="glucid_should_use" name="glucid_should_use"><%=examine.glucid_should_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.glucid_should_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" id="glucid_limited_use" name="glucid_limited_use"><%=examine.glucid_limited_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.glucid_limited_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" id="glucid_should_not_use" name="glucid_should_not_use"><%=examine.glucid_should_not_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.glucid_should_not_use%></div>
                <% } %>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm protein</div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" name="protein_should_use" id="protein_should_use"><%=examine.protein_should_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.protein_should_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" name="protein_limited_use" id="protein_limited_use"><%=examine.protein_limited_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.protein_limited_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control" name="protein_should_not_use" id="protein_should_not_use"><%=examine.protein_should_not_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.protein_should_not_use%></div>
                <% } %>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm lipid</div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="lipid_should_use" id="lipid_should_use"><%=examine.lipid_should_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.lipid_should_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="lipid_limited_use" id="lipid_limited_use"><%=examine.lipid_limited_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.lipid_limited_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="lipid_should_not_use" id="lipid_should_not_use"><%=examine.lipid_should_not_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.lipid_should_not_use%></div>
                <% } %>
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col-12 col-md-2 fw-bold p-2">Nhóm VTM & CK</div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="vitamin_ck_should_use" id="vitamin_ck_should_use"><%=examine.vitamin_ck_should_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.vitamin_ck_should_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="vitamin_ck_limited_use" id="vitamin_ck_limited_use"><%=examine.vitamin_ck_limited_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.vitamin_ck_limited_use%></div>
                <% } %>
            </div>
            <div class="col-12 col-md-3 p-2">
                <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                    <textarea class="form-control text-box single-line" name="vitamin_ck_should_not_use" id="vitamin_ck_should_not_use"><%=examine.vitamin_ck_should_not_use%></textarea>
                <% }else{ %>
                    <div class="form-control form-control-title"><%= examine.vitamin_ck_should_not_use%></div>
                <% } %>
            </div>
        </div>
    </div>
    <h3 class="box-title-break">Chế độ vận động sinh hoạt</h3>
    <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
        <div class="row g-2 align-items-center">
            <div class="col-12 col-md-6 d-flex">
                <label class="col-form-label col-md-auto fw-bold mt-2" for="cus_butt">Chọn mẫu</label>
                <div class="col-form-body col-md">
                    <div class="form-control-has-addon">
                        <select class="form-select" name="active_mode_of_living_id" id="active_mode_of_living_id" data-plugin="select2" data-placeholder="Chế độ vận động sinh hoạt">
                            <option></option>
                            <% if(activeModeOfLiving.length > 0){ %>
                                <% for (let ac_Moliving of activeModeOfLiving){ %>
                                    <option <%=ac_Moliving.id == examine.active_mode_of_living_id ? 'selected' : ''%> value="<%= ac_Moliving.id %>"><%= ac_Moliving.name %></option>
                                <%}%>   
                            <%}%>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    <% } %>
    <div class="row">
        <div class="col-form-body col-md">
            <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
                <div class="form-control-has-addon">
                    <textarea class="form-control" id="active_mode_of_living" placeholder="Chế độ vận động sinh hoạt" name="active_mode_of_living"><%= examine.active_mode_of_living%></textarea>
                    <button class="btn form-control-addon btn-link p-0 min-h-auto" type="button" onclick="clearInput('#active_mode_of_living')">
                        <svg class="iconsvg-close">
                            <use xlink:href="/public/content/images/sprite.svg#close"></use>
                        </svg>
                    </button>
                </div>
            <% }else{ %>
                <div class="form-control form-control-title"><%= examine.active_mode_of_living%></div>
            <% } %>
          </div>
    </div>
    <div class="row g-2 justify-content-center mt-0">
        <% if(!(examine.status == 4 || (examine.status == 3 && isDetail == true))){ %>
            <div class="col-6 col-md-auto">
            <button class="btn btn-primary box-btn w-100 text-uppercase" type="button" onclick="saveExamine(2, 0, {})" title="Lưu phiếu khám">
                <svg class="iconsvg-send-2 flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#save"></use>
                </svg>Lưu
            </button>
            </div>
            <div class="col-6 col-md-auto">
            <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList()" title="Hủy thao tác sửa phiếu khám">
                <svg class="iconsvg-close flex-shrink-0 fs-16px me-2">
                <use xlink:href="/public/content/images/sprite.svg#close"></use>
                </svg>Huỷ
            </button>
            </div>
        <% } %>
      </div>
</form>