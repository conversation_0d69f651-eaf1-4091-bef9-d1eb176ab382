<table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
    <thead>
      <tr>
        <td class="table-sort">
          <span>Tê<PERSON> bệnh nhân</span>
        </td>
        <td class="table-sort">
          <span>Phone</span>
        </td>
        <td>
          <span>Address</span>
        </td>
        <td class="table-sort">
          <span>Chu<PERSON><PERSON></span>
        </td>
        <td class="table-sort">
          <span>Thời gian</span>
        </td>
        <td class="table-sort w-1">Hành động</td>
      </tr>
    </thead>
    <tbody>
      <% if (listExamine.length > 0){%>
        <% for (i = 0;i < listExamine.length;i++){%>
          <tr>
            <td class="min-w-150px">
              <div class="flex-center-y fs-13px">
                <img class="img-fluid me-2" src="/public/content/images/user.svg" alt="" />
                <%=listExamine[i].cus_name%>
              </div>
            </td>
            <td class="fs-6 text-red"><%=listExamine[i].cus_phone%></td>
            <td class="fs-13px text-primary"><%=listExamine[i].cus_address%></td>
            <td class="fs-13px"><%=listExamine[i].diagnostic%></td>
            <td class="fs-13px"><%=moment(listExamine[i].created_at).format("HH:mm:ss")%>
              <br>
              <small class="text-body-2"><%=moment(listExamine[i].created_at).format("DD/MM/YYYY")%></small>
            </td>
            <td>
              <div class="flex-center-x gap-10px" style="position: relative;">
                <a title="Xem" onclick="viewDetailExamine('<%=listExamine[i].id%>')">Chi tiết</a>
              </div>
            </td>
          </tr>
        <%}%>
      <%} else {%>
      <tr>
        <td colspan="9" style="text-align: center;"><b>Không có dữ liệu!</b></td>
      </tr>
      <% } %>
    </tbody>
  </table>