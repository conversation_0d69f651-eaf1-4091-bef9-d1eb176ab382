<html lang="en">
   <head>
      <%- include('../layout/head') %>
      <title><PERSON><PERSON><PERSON> h<PERSON><PERSON> chẩn</title>
   </head>
   <body>
      <div class="page">
         <%- include('../layout/header',{user: user}) %>
         <%- include('../layout/sidebar') %>
         <div class="page-main">
            <% if (errors.length > 0){%>
            <div class="container-fluid">
               <div class="row gy-32px">
                     <div class="alert-dismissable">
                         <div class="alert alert-danger">
                             <ul>
                                 <% for (i = 0;i < errors.length;i++){%>
                                     <li><%=errors[i]%></li>
                                 <%}%>
                             </ul>
                         </div>
                     </div>
               </div>
            </div>
            <% } else { %>
            <div class="container">
              <div>
                <div class="tab box">
                  <ul class="tab-nav nav nav-tabs justify-content-center">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#kham" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="true" id="kham-tab" onclick="changeTabConsultation(1)">Khám</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#canthiepdinhduong" data-bs-toggle="tab" role="tab" aria-controls="tab" aria-selected="false" id="canthiepdinhduong-tab" onclick="changeTabConsultation(2)">Can thiệp dinh dưỡng</a>
                    </li>
                  </ul>
                </div>

                <div class="tab-content">
                  <div class="tab-pane fade box-body show active" id="kham">
                    <%- include('../consultation/kham') %>
                  </div>
                  <div class="tab-pane fade box-body" id="canthiepdinhduong">
                    <%- include('../consultation/hoichuan') %>
                  </div>
                </div>
              </div>
            </div>
            <script>
              $(document).ready(function() {
                dataExamine.page = "<%=page%>";
                dataExamine.id_consultation = "<%=consultation.id%>";
                dataExamine.isDetail = "<%=isDetail%>";
                dataConsultation.clinicalExam = <%-JSON.stringify(clinicalExam) %>;
                dataConsultation.nutritionTracking = <%-JSON.stringify(nutritionTracking) %>;
                dataConsultation.subclinical = <%-JSON.stringify(subclinical) %>;
                dataConsultation.subclinicalOrther = <%-JSON.stringify(subclinicalOrther) %>;
                dataConsultation.medicine = <%-JSON.stringify(medicine) %>;
                dataConsultation.additional = <%-JSON.stringify(additional) %>;

                let firstItem = {id: 1, date: moment().format("DD-MM-YYYY"), content: ''};
                if(dataConsultation.clinicalExam.length == 0) dataConsultation.clinicalExam.push({...firstItem});
                for(let item1 of dataConsultation.clinicalExam){
                  addClinicalExamHtml(item1);
                }
                if(dataConsultation.nutritionTracking.length == 0) dataConsultation.nutritionTracking.push({...firstItem});
                for(let item2 of dataConsultation.nutritionTracking){
                  addNutritionTrackingHtml(item2);
                }
                if(dataConsultation.subclinicalOrther.length == 0) dataConsultation.subclinicalOrther.push({...firstItem});
                for(let item3 of dataConsultation.subclinicalOrther){
                  addAnalysisOrtherHtml(item3);
                }

                if(dataConsultation.subclinical.date.length > 0){
                  for(let [index, date] of dataConsultation.subclinical.date.entries()){
                    addAnalysisDateHtml(date.date, date.id);
                  }
                }
                if(dataConsultation.subclinical.data.length > 0){
                  for(let item of dataConsultation.subclinical.data){
                    addAnalysisHtml(item);
                  }
                }
                if(dataConsultation.medicine.date.length > 0){
                  for(let [index, date] of dataConsultation.medicine.date.entries()){
                    addMedicineConsultationDateHtml(date.date, date.id);
                  }
                }
                if(dataConsultation.medicine.data.length > 0){
                  for(let item of dataConsultation.medicine.data){
                    addMedicineConsultationHtml(item);
                  }
                }

                if(dataConsultation.additional.length > 0){
                  for(let item of dataConsultation.additional){
                    addAdditionalHtml(item);
                  }
                }
                setTimeout(()=>{
                  $('#medicine_type_id').trigger('change');
                  $('#type_medicine_select').trigger('change');
                }, 1000)
              });
              // Lưu tự động mỗi 5 phút
              setInterval(()=>{
                saveConsultation(1, {});
              }, 300000)
            </script>
            <% } %>
         </div>
          <%- include('../layout/footer', { footer_class: ""}) %>
      </div>
   </body>
</html>