<form id="hoichuan_form" class="form-horizontal">
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Khám lâm sàng</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm khám lâm sàng" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#modal_kham_lam_sang" id="btn_modal_kham_lam_sang">
                <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_kham_lam_sang">
        <div class="table-responsive-inner">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                <thead>
                    <tr>
                        <td class="text-center h-02">Ngày</td>
                        <td class="text-center h-02">Nội dung</td>
                        <td class="w-1 h-02"></td>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
        </div>
    </div>
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Dinh dưỡng</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm theo dõi dinh dưỡng" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#modal_theo_doi_dinh_duong" id="btn_modal_theo_doi_dinh_duong">
                <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_theo_doi_dinh_duong">
        <div class="table-responsive-inner">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                <thead>
                    <tr>
                        <td class="text-center h-02">Ngày</td>
                        <td class="text-center h-02">Nội dung</td>
                        <td class="w-1 h-02"></td>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
        </div>
    </div>
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Cận lâm sàng</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm cận lâm sàng" class="btn btn-add" onclick="openModalAnalysis()" id="btn_modal_can_lam_sang"> 
                <svg class="iconsvg-success">
                    <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_can_lam_sang">
            <div class="table-responsive-inner pb-2">
                <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                    <thead>
                        <tr id="tb_can_lam_sang_head">
                            <td class="text-center h-02 p-0">Xét nghiệm</td>
                            <td class="text-center h-02 p-0">Giá trị tham chiếu</td>
                            <td class="w-1 h-02 p-0" id="td_btn_add_date_subclinical">
                                <button title="Thêm ngày" class="btn btn-orange" type="button" data-bs-toggle="modal" data-bs-target="#modal_can_lam_sang_them_ngay">
                                    <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    </thead>
                    <tbody>
                        
                    </tbody>
                </table>
            </div>
        </div>
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Khác</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm cận lâm sàng khác" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#modal_can_lam_sang_khac" id="btn_modal_can_lam_sang_khac">
                <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_can_lam_sang_khac">
        <div class="table-responsive-inner">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                <thead>
                    <tr>
                        <td class="text-center h-02">Ngày</td>
                        <td class="text-center h-02">Nội dung</td>
                        <td class="w-1 h-02"></td>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
        </div>
    </div>
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Thuốc điều trị</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm thuốc điều trị" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#modal_medicine" id="btn_modal_medicine">
                <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_theo_doi_thuoc">
        <div class="table-responsive-inner pb-2">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                <thead>
                    <tr id="tb_theo_doi_thuoc_head">
                        <td class="text-center h-02 p-0">Thuốc</td>
                        <td class="w-1 h-02 p-0" id="td_btn_add_date_medicine">
                            <button title="Thêm ngày" class="btn btn-action btn-action-send" type="button" data-bs-toggle="modal" data-bs-target="#modal_medicine_add_date">
                                <svg class="iconsvg-success">
                                    <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                </svg>
                            </button>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
        </div>
    </div>
    <div class="d-flex gap-2 align-items-center">
        <div class="flex-fill">
            <h3 class="box-title-break">Bổ sung</h3>
        </div>
        <div class="">
            <button type="button" title="Thêm bổ xung" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#modal_additional" id="btn_modal_additional">
                <svg class="iconsvg-success">
                                        <use xlink:href="/public/content/images/sprite.svg#plus-circle"></use>
                                    </svg>
            </button>
        </div>
    </div>
    <div class="table-responsive-xl table-responsive-flush mb-4" id="tb_theo_doi_bo_xung">
        <div class="table-responsive-inner">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                <thead>
                    <tr>
                        <td class="text-center h-02">STT</td>
                        <td class="text-center h-02">Thuốc</td>
                        <td class="text-center h-02">Cách dùng</td>
                        <td class="text-center h-02" style="width: 100px;">SL</td>
                        <td class="text-center h-02">Đơn vị</td>
                        <td class="w-1 h-02" id="active_table_medicine"></td>
                    </tr>
                </thead>
                
            </table>
        </div>
    </div>
    <div class="row g-2 justify-content-center mt-0">
        <% if(isDetail !==true){ %>
            <div class="col-6 col-md-auto">
                <button class="btn btn-primary box-btn w-100 text-uppercase" type="button"
                    onclick="saveConsultation(0, {})" title="Lưu">
                    <svg class="iconsvg-send-2 flex-shrink-0 fs-6 me-2">
                        <use xlink:href="/public/content/images/sprite.svg#save"></use>
                    </svg>Lưu
                </button>
            </div>
            <div class="col-6 col-md-auto">
                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" onclick="returnList1()"
                    title="Quay lại danh sách">
                    <svg class="iconsvg-close flex-shrink-0 fs-6 me-2">
                        <use xlink:href="/public/content/images/sprite.svg#close"></use>
                    </svg>Huỷ
                </button>
            </div>
            <% } %>
    </div>
</form>

<div class="modal" id="modal_kham_lam_sang">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_kham_lam_sang_title">
                    Khám lâm sàng
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="kham_lam_sang_ngay"
                            placeholder="Ngày" />
                    </div>
                </div>
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Nội dung</label>
                    <div class="col-md">
                        <textarea class="form-control" id="kham_lam_sang_noi_dung" rows="5"></textarea>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addClinicalExam()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_theo_doi_dinh_duong">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_theo_doi_dinh_duong_title">
                    Dinh dưỡng
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="theo_doi_dinh_duong_ngay"
                            placeholder="Ngày" />
                    </div>
                </div>
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Nội dung</label>
                    <div class="col-md">
                        <textarea class="form-control" id="theo_doi_dinh_duong_noi_dung" rows="5"></textarea>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addNutritionTracking()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_can_lam_sang">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_can_lam_sang_title">
                    Cận lâm sàng
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="analysis" class="col-md-auto col-form-label-modal fw-bold">Xét nghiệm</label>
                    <div class="col-md">
                        <select class="form-select" id="analysis" data-plugin="select2" data-parent="#modal_can_lam_sang" data-options='{"tags":false,"dropdownCssClass":"select2-dropdown-tagging","selectionCssClass":"select2-selection--tagging","closeOnSelect": false}' data-placeholder="Chọn xét nghiệm" multiple="multiple">
                            <option value="all">Chọn tất cả</option>
                            <% if(analysis.length > 0){ %>
                              <% for (let item of analysis){ %>
                                <option value="<%= item.id %>" data-max="<%=item.max%>" data-min="<%=item.min%>" data-reference="<%=item.reference_value%>"><%= item.name %></option>
                              <%}%>   
                            <%}%>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addAnalysis()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_can_lam_sang_them_ngay">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_can_lam_sang_them_ngay_title">
                    Cận lâm sàng thêm ngày
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="analysis" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="can_lam_sang_ngay" placeholder="Ngày" />
                    </div>
                </div>
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="formFile" class="col-md-auto col-form-label-modal fw-bold">File tải lên</label>
                    <div class="col-md">
                        <input class="form-control" type="file" id="formFile" onchange="changeInputFile()" accept="excel/xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addAnalysisDate()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_can_lam_sang_khac">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_can_lam_sang_khac_title">
                    Cận lâm sàng khác
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="can_lam_sang_khac_ngay"
                            placeholder="Ngày" />
                    </div>
                </div>
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Nội dung</label>
                    <div class="col-md">
                        <textarea class="form-control" id="can_lam_sang_khac_noi_dung" rows="5"></textarea>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addAnalysisOrther()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_medicine">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_medicine_title">
                    Thuốc điều trị
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="type_medicine_select" class="col-md-auto col-form-label-modal fw-bold">Phân loại Thuốc</label>
                    <div class="col-md">
                        <select class="form-select" id="type_medicine_select" data-parent="#modal_medicine" data-plugin="select2" data-placeholder="Chọn thuốc">
                            <option value="0">Chưa phân loại</option>
                            <% if(medicineType.length > 0){ %>
                              <% for (let item of medicineType){ %>
                                <option value="<%= item.id %>"><%= item.name %></option>
                              <%}%>   
                            <%}%>
                        </select>
                    </div>
                </div>
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="medicine_select" class="col-md-auto col-form-label-modal fw-bold">Thuốc</label>
                    <div class="col-md">
                        <select class="form-select" id="medicine_select" data-options='{"tags":false,"dropdownCssClass":"select2-dropdown-tagging","selectionCssClass":"select2-selection--tagging","closeOnSelect": false}' data-parent="#modal_medicine" data-plugin="select2" data-placeholder="Chọn thuốc" multiple="multiple">
                            
                        </select>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addMedicineConsultation()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_medicine_add_date">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_medicine_add_date_title">
                    Thêm ngày theo dõi thuốc
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="analysis" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="medicine_date" placeholder="Ngày" />
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addMedicineConsultationDate()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_additional">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 70vw !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_additional_title">
                    Bổ xung
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="row g-2 align-items-center">
                    <div class="col-12 col-md-6 col-xl-4">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="policyTitle" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                            <div class="col-md">
                                <input type="text" class="form-control form-control-title p-1" id="bo_xung_ngay"
                                    placeholder="Ngày" />
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-xl-4">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="medicine_type_id" class="col-md-auto fw-bold">Phân loại Thuốc</label>
                            <div class="col-md">
                                <select class="form-select" id="medicine_type_id" data-plugin="select2" data-parent="#modal_additional" data-placeholder="Chọn thuốc">
                                    <option value="0">Chưa phân loại</option>
                                    <% if(medicineType.length > 0){ %>
                                      <% for (let item of medicineType){ %>
                                        <option value="<%= item.id %>"><%= item.name %></option>
                                      <%}%>   
                                    <%}%>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-xl-4">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="medicine_id" class="col-md-auto col-form-label-modal fw-bold">Thuốc</label>
                            <div class="col-md">
                                <select class="form-select" id="medicine_id" data-plugin="select2" data-parent="#modal_additional" data-placeholder="Chọn thuốc">
                                    
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-xl-2">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="total_medinice" class="col-md-auto col-form-label-modal fw-bold">Số lượng</label>
                            <div class="col-md">
                                <input type="number" class="form-control form-control-title p-1" id="total_medinice"
                                    placeholder="Số lượng" />
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 col-xl-2">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="unit_medinice" class="col-md-auto col-form-label-modal fw-bold">ĐVT</label>
                            <div class="col-md">
                                <input type="text" class="form-control form-control-title p-1" id="unit_medinice"
                                    placeholder="Đơn vị tính" />
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-12 col-xl-8">
                        <div class="form-group row g-2 mb-3 align-items-center">
                            <label for="use_medinice" class="col-md-auto col-form-label-modal fw-bold">Cách sử dụng</label>
                            <div class="col-form-body col-md">
                                <div class="input-group">
                                  <input type="text" class="form-control form-control-title p-1" id="use_medinice" placeholder="Cách sử dụng">
                                  <button class="btn btn-primary" type="button" onclick="addMedicineConsultationTableModal()" title="Thêm dữ liệu thuốc">Thêm</button>
                                </div>
                              </div>
                        </div>
                    </div>
                </div>
                <div class="row g-2 align-items-center">
                    <div class="table-responsive-xl table-responsive-flush mb-4" style="width: 100%;display: none;" id="tb_prescription">
                      <div class="table-responsive-inner"></div>
                      <table class="table-1 table table-pe-x-3 mb-0 align-middle table-ds-booking">
                        <thead>
                          <tr>
                            <td>STT</td>
                            <td>
                              <span>Tên thuốc</span>
                            </td>
                            <td>
                              <span>HDSD</span>
                            </td>
                            <td style="width: 100px;">
                              <span>Số lượng</span>
                            </td>
                            <td>
                              <span>Đơn vị tính</span>
                            </td>
                            <td id="active_table_medicine" class="w-1"></td>
                          </tr>
                        </thead>
                        <tbody>
                          
                        </tbody>
                      </table>
                    </div>
                  </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-success" onclick="addAdditional()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modal_change_date">
    <div class="modal-dialog align-items-center d-flex h-100 m-0 mx-auto" style="max-width: 700px !important">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title text-center w-100" id="modal_change_date_title">
                    Sửa ngày
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" style="max-height: 80vh; overflow-y: auto">
                <div class="form-group row g-2 mb-3 align-items-center">
                    <label for="analysis" class="col-md-auto col-form-label-modal fw-bold">Ngày</label>
                    <div class="col-md">
                        <input type="text" class="form-control form-control-title p-1" id="date_change" placeholder="Ngày" />
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-danger me-2" onclick="deleteDate()">
                    <svg class="iconsvg-trash flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#trash"></use>
                    </svg>
                    Xóa
                </button>
                <button type="button" class="btn btn-success" onclick="changeDate()">
                    Lưu
                </button>
            </div>
        </div>
    </div>
</div>
<script src="/public/content/js/vendor/read-excel-file.min.js"></script>
<script>
    analysisData = <%-JSON.stringify(analysis) %>;
    console.log("analysisData", analysisData);
    
    // Xử lý sự kiện chọn tất cả cho select2
    $('#analysis').on('select2:select', function(e) {
        if (e.params.data.id === 'all') {
            // Nếu chọn "Chọn tất cả"
            if ($(this).val().includes('all')) {
                // Chọn tất cả các option khác
                $(this).find('option').prop('selected', true);
            } else {
                // Bỏ chọn tất cả
                $(this).find('option').prop('selected', false);
            }
            $(this).trigger('change');
            // Đóng dropdown sau khi chọn
            $(this).select2('close');
        } else {
            // Nếu chọn một option khác, bỏ chọn "Chọn tất cả"
            $(this).find('option[value="all"]').prop('selected', false);
        }
    });

    // Khi thay đổi selection
    $('#analysis').on('change', function() {
        var selectedValues = $(this).val();
        if (selectedValues && selectedValues.length > 0) {
            // Nếu tất cả các option khác được chọn, tự động chọn "Chọn tất cả"
            var allOptionsSelected = $(this).find('option:not([value="all"])').length === selectedValues.length;
            if (allOptionsSelected) {
                $(this).find('option[value="all"]').prop('selected', true);
            }
        }
    });

    let kham_lam_sang_ngay = $("#kham_lam_sang_ngay").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true,
    });
    let theo_doi_dinh_duong_ngay = $("#theo_doi_dinh_duong_ngay").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true,
    });
    let can_lam_sang_ngay = $("#can_lam_sang_ngay").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true
    });
    let can_lam_sang_khac_ngay = $("#can_lam_sang_khac_ngay").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true
    });
    let medicine_date = $("#medicine_date").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true
    });
    let bo_xung_ngay = $("#bo_xung_ngay").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true
    });
    var date_change = $("#date_change").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true
    });

    var excelData = null; // Biến lưu dữ liệu Excel tạm thời

    function changeInputFile() {
        let dataFile = $('#formFile').prop('files');
        if (dataFile && dataFile.length > 0) {
            readXlsxFile(dataFile[0]).then(function(rows) {
                // Bỏ qua hàng đầu tiên (tiêu đề) nếu có
                if (rows.length > 0) rows.shift();
                window.excelData = rows; // Lưu dữ liệu Excel vào biến global
                console.log('excelData loaded:', window.excelData);
            }).catch(function(error){
                console.error("Error reading Excel file:", error);
                displayErrorToastr("Có lỗi khi đọc file Excel.");
            });
        }
    }
</script>