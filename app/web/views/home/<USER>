<form action="/" method="get" id="dashboard_filter">
<div class="statistic_body">
  <div class="box-body">
    <!-- <div class="dashboard_search">
      <div class="row flex-grow-1">
        <div class="col-md-auto">
          <div id="website_filter">
            <div id="website_select"></div>
          </div>
        </div>
        <div class="col-md-auto">
          <div id="status_filter">
            <div id="status_select"></div>
          </div>
        </div>
        <div class="col-md-auto">
          <form action="/" method="get" id="dashboard_filter">
            <div class="statistic_search">
              <div class="flatpickr flatpickr-range" data-options='{"mode":"range"}' data-plugin="flatpickr">
                <% if (!filter.search.fromdate){%>
                <input class="form-control" id="sfilter_date" type="text" placeholder="Thời gian" value="" data-input="data-input" aria-label="Thời gian" />
                <%} else {%>
                <input class="form-control" id="sfilter_date" type="text" placeholder="Thời gian" value="<%=filter.search.fromdate%> - <%=filter.search.todate%>" data-input="data-input" aria-label="Thời gian" />
                <%}%>
             </div>
              <a onclick="statistic_search()" class="btn btn-danger sidebar-btn-booking">
                <svg class="iconsvg-search">
                  <use xlink:href="/public/content/images/sprite.svg#search"></use>
                </svg>
                Thống kê
              </a>
            </div>
          </form>
        </div>
      </div>
    </div> -->
    <div class="row">
      <div class="col-xl-4">
        <div class="box box-stat">
          <h2 class="box-title">Thống kê số lượng khám</h2>
          <div class="row gy-10px gx-3 justify-content-center">
            <div class="col-6">
              <div class="stat">
                <h6 class="stat-title">Tiếp nhận</h6>
                <div class="stat-body">
                  <svg class="iconsvg-arrow-right-circle stat-icon text-gui-duyet">
                    <use xlink:href="/public/content/images/sprite.svg#arrow-right-circle"></use>
                  </svg>
                  <div class="stat-number">
                    <a title="Tiếp nhận" href="/examine?status_ids=1">
                      <%=statsByStatus.post1%>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="stat">
                <h6 class="stat-title">Đang khám</h6>
                <div class="stat-body">
                  <svg class="iconsvg-hourglass stat-icon text-cho-duyet">
                    <use xlink:href="/public/content/images/sprite.svg#hourglass"></use>
                  </svg>
                  <div class="stat-number">
                    <a title="Đang khám" href="/examine?status_ids=2">
                      <%=statsByStatus.post2%>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="stat">
                <h6 class="stat-title">Hoàn thành</h6>
                <div class="stat-body">
                  <svg class="iconsvg-checkmark-circle stat-icon text-da-duyet">
                    <use xlink:href="/public/content/images/sprite.svg#checkmark-circle"></use>
                  </svg>
                  <div class="stat-number">
                    <a title="Hoàn thành" href="/examine?status_ids=3">
                      <%=statsByStatus.post3%>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-6">
              <div class="stat">
                <h6 class="stat-title">Đã hủy</h6>
                <div class="stat-body">
                  <svg class="iconsvg-x-mark-circle stat-icon text-da-huy">
                    <use xlink:href="/public/content/images/sprite.svg#x-mark-circle"></use>
                  </svg>
                  <div class="stat-number">
                    <a title="Đã hủy" href="/examine?status_ids=4">
                      <%=statsByStatus.post4%>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-8">
        <div class="box h-100">
          <h2 class="box-title">Biểu đồ số lượng khám</h2>
          <div class="box-body box-chart-body">
            <div class="box-chart text-center">
              <div id="myChartByDate" class="chart--container"></div>
              <script type="text/javascript">
                var pageview        = <%- JSON.stringify(chartNews) %>;
                let pageviewConfig  = {
                  type: 'mixed',
                  plot: {
                    tooltip: {
                      visible: false
                    },
                    aspect: 'spline',
                    hoverState: {
                      backgroundColor: '#F2564C'
                    }
                  },
                  plotarea: {
                    'margin-right': "10%",
                  },
                  legend: {
                    layout: "1x5",
                    x: "35%",
                    y: "93%",
                    borderColor: "transparent",
                    marker: {
                      borderRadius: 10,
                      borderColor: "transparent"
                    }
                  },
                  scaleX: {
                    labels: pageview['label']
                  },
                  scaleY: {
                    label: {
                      text: 'Số lượt khám',
                      fontSize: '13px',
                      fontWeight: 'normal',
                      color: '#818181'
                    },
                    maxValue: pageview['max_news'],
                    minValue: 0,
                    step: 1
                  },
                  crosshairX: {
                    lineColor: '#424242',
                    lineGapSize: '4px',
                    lineStyle: 'dotted',
                    scaleLabel: {
                      backgroundColor: '#424242'
                    }
                  },
                  series: [
                    // {
                    //   type: 'bar',
                    //   text: 'Số lượt khám',
                    //   values: pageview['news'],
                    //   lineColor: '#5BCD8F',
                    //   backgroundColor: '#5BCD8F',
                    //   scales: 'scale-x, scale-y'
                    // },
                    {
                      type: 'line',
                      text: 'Số lượt khám',
                      values: pageview['news'],
                      lineColor: '#F2564C',
                      backgroundColor: '#F2564C',
                      scales: 'scale-x, scale-y'
                    }
                  ]
                };
                
                zingchart.render({
                    id: 'myChartByDate',
                    data: pageviewConfig,
                    height: '100%',
                    width: '100%',
                });
              </script>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</form>