<div class="row gx-30px gy-5">
  <div class="col">
    <div class="box">
      <div class="box-header">
        <h2 class="box-title">Danh sách khám <small class="text-icon fw-6">(<%=countExamine%>)</small>
        </h2>
        <a class="box-more-link link-primary ms-auto" href="/examine">
          Xem đ<PERSON><PERSON>
          <svg class="iconsvg-arrow-right ms-1 flex-shrink-0">
            <use xlink:href="/public/content/images/sprite.svg#arrow-right"></use>
          </svg>
        </a>
      </div>
      <div class="box-body box-table">
        <div class="table-responsive-md table-responsive-flush">
          <div class="table-responsive-inner">
            <table class="table-1 table table-pe-x-3 mb-0 align-middle">
              <thead>
                <tr>
                  <td>Id</td>
                  <td>T<PERSON><PERSON> b<PERSON><PERSON> nhân</td>
                  <td>Phone</td>
                  <td>Address</td>
                  <td>Ch<PERSON><PERSON></td>
                  <td width="100px">Ng<PERSON><PERSON> t<PERSON>o</td>
                  <td class="w-1">Trạng thái</td>
                </tr>
              </thead>
              <tbody>
              <% if (listExamine && listExamine.length > 0){%>
                <% for (i = 0;i < listExamine.length;i++){%>
                  <tr>
                    <td>
                      <a target="_blank" href="/examine/edit/<%=listExamine[i].id%>?detail=true">
                        <span class="text-primary fw-6 fs-13px">
                          <%=listExamine[i].count_id%>
                        </span>
                      </a>
                    </td>
                    <td>
                      <div class="flex-center-y fs-13px">
                        <img class="img-fluid me-2" src="/public/content/images/user.svg" alt="" />
                        <%=listExamine[i].cus_name%>
                      </div>
                    </td>
                    <td class="fs-6 text-red"><%=listExamine[i].cus_phone%></td>
                    <td class="fs-13px text-primary min-w-150px"><%=listExamine[i].cus_address%></td>
                    <td class="fs-13px min-w-150px"><%=listExamine[i].diagnostic%></td>
                    <td class="fs-13px"><%=moment(listExamine[i].created_at).format("HH:mm:ss")%>
                      <br>
                      <small class="text-body-2"><%=moment(listExamine[i].created_at).format("DD/MM/YYYY")%></small>
                    </td>
                    <td>
                      <span class="badge badge-<%=webService.examineStatusClass(listExamine[i].status).name%>">
                        <%=webService.examineStatusClass(listExamine[i].status).value%>
                      </span>
                    </td>
                  </tr>
                <%}%>
              <%} else {%>
                <tr>
                  <td colspan="5" style="text-align: center;">
                    <b>Không có dữ liệu!</b>
                  </td>
                </tr>
              <% } %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>