<% for (i = 0;i < listConsultation.length;i++){%>
  <tr id="consultation_<%=listConsultation[i].id%>">
    <td>
      <a target="_blank" href="/consultation/edit/<%=listConsultation[i].id%>?detail=true">
        <span class="text-primary fw-6 fs-13px"><%=listConsultation[i].count_id%></span>
      </a>
    </td>
    <td class="min-w-150px">
      <div class="flex-center-y fs-13px">
        <img class="img-fluid me-2" src="/public/content/images/user.svg" alt="" />
        <%=listConsultation[i].cus_name%>
      </div>
    </td>
    <td class="fs-6 text-red"><%=listConsultation[i].cus_phone%></td>
    <td class="fs-13px text-primary"><%=listConsultation[i].cus_address%></td>
    <td class="fs-13px"><%=listConsultation[i].diagnostic%></td>
    <td class="fs-13px"><%=moment(listConsultation[i].created_at).format("HH:mm:ss")%>
      <br>
      <small class="text-body-2"><%=moment(listConsultation[i].created_at).format("DD/MM/YYYY")%></small>
    </td>
    <td>
      <div class="flex-center-x gap-10px" style="position: relative;">
        <a title="Sửa" class="btn btn-action btn-action-edit" target="_blank" href="/consultation/edit/<%=listConsultation[i].id%>">
          <svg class="iconsvg-edit-2">
             <use xlink:href="/public/content/images/sprite.svg#edit-2"></use>
          </svg>
        </a>
        <button class="btn btn-action btn-action-cancel" title="Huỷ phiếu khám" type="button" onclick="showModalCancelConsultation('<%=listConsultation[i].id%>')">
          <svg class="iconsvg-close-circle">
             <use xlink:href="/public/content/images/sprite.svg#trash"></use>
          </svg>
       </button>
      </div>
    </td>
  </tr>
<%}%>