<% if(!paginatorConsultation.hasPrevPage) { %>
  <button disabled class="btn btn-outline-primary paginate-btn-prev" type="button">
    <svg class="iconsvg-chevron-right rotate-180">
      <use xlink:href="/public/content/images/sprite.svg#chevron-right"></use>
    </svg>
  </button>
<% } else { %>
  <a class="btn btn-outline-primary paginate-btn-prev" onclick="getDataSort(0,'page', 2 ,'<%=(paginatorConsultation.page - 1)%>')">
    <svg class="iconsvg-chevron-right rotate-180">
      <use xlink:href="/public/content/images/sprite.svg#chevron-right"></use>
    </svg>
  </a>
<% } %>
<div class="paginate-text">
  <span class="fw-6"><%=(paginatorConsultation.perPage * paginatorConsultation.page) - (paginatorConsultation.perPage -1)%>-<%=(paginatorConsultation.perPage * paginatorConsultation.page) > paginatorConsultation.totalItem ? paginatorConsultation.totalItem : (paginatorConsultation.perPage * paginatorConsultation.page)%></span>
  <span class="text-icon">/ <%=paginatorConsultation.totalItem%></span>
</div>
<% if(!paginatorConsultation.hasNextPage) { %> 
<button disabled class="btn btn-outline-primary paginate-btn-next" type="button">
  <svg class="iconsvg-chevron-right">
    <use xlink:href="/public/content/images/sprite.svg#chevron-right"></use>
  </svg>
</button>
<% } else { %>
<a class="btn btn-outline-primary paginate-btn-next" onclick="getDataSort(1,'page', 2,'<%=(paginatorConsultation.page + 1)%>')">
  <svg class="iconsvg-chevron-right">
    <use xlink:href="/public/content/images/sprite.svg#chevron-right"></use>
  </svg>
</a>
<% } %>