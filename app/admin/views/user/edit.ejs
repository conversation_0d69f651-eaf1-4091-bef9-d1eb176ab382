﻿<html lang="en">
  <head>
    <title>Sửa người dùng</title>
    <%- include('../shared/head') %>
  </head>
  <body class="sidebar-mini layout-fixed control-sidebar-slide-open">
    <div class="wrapper">
      <%- include('../shared/menu',{user: user}) %>
      <div class="content-wrapper">
        <% if (errors.length >0){%>
        <div class="alert alert-danger alert-dismissable">
          <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
          <ul>
          <% for (i = 0;i < errors.length;i++){%>
            <li><%=errors[i]%></li>
          <%}%>
          </ul>
        </div>
        <%}%>
        <form action="/admin/user/edit/<%=pr_user.id%>" id="user-form" method="post" novalidate="novalidate">
          <div class="content-header clearfix">
            <h1 class="float-left">Sửa người dùng - <%= pr_user.name %> 
              <small> 
                <i class="fas fa-arrow-circle-left"></i> 
                <a href="/admin/user">Trở lại danh sách người dùng</a> 
              </small>
            </h1>
            <div class="float-right">
              <button type="submit" name="save" class="btn btn-primary" value="save"> 
                <i class="far fa-save"></i> Lưu 
              </button>
              <button type="submit" name="saveContinue" class="btn btn-primary" value="saveContinue"> 
                <i class="far fa-save"></i> Lưu và tiếp tục sửa 
              </button>
            </div>
          </div>
          <section class="content">
            <div class="container-fluid">
              <div class="form-horizontal">
                <div class="cards-group">
                  <div class="card card-default">
                    <div class="card-body">
                      <%- include('../user/_createorupdate',{pr_user:pr_user}) %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </form>
      </div>
      <%- include('../shared/footer') %>
    </div>
  </body>
</html>