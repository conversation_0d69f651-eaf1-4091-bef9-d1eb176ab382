﻿<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">T<PERSON><PERSON> <PERSON><PERSON><PERSON></label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="diagnostic_name" type="text" value="<%=diagnostic.name%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Chi tiết</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <textarea class="form-control text-box single-line" name="diagnostic_detail"><%=diagnostic.detail%></textarea>
    </div>
  </div>
</div>

<% if(user.isAdmin){ %>
  <div class="form-group row">
    <div class="col-md-3">
      <div class="label-wrapper">
        <label class="col-form-label">Share</label>
      </div>
    </div>
    <div class="col-md-9">
      <div class="input-group">
        <input type="checkbox" <%=diagnostic.share == 1 ? 'checked' : ''%> name="share"/>
      </div>
    </div>
  </div>
  <% }else{ %>
    <input type="hidden" value="<%=diagnostic.share == 1 ? 'on' : ''%>" name="share"/>
  <% } %>