﻿<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Age Min</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="age_min" type="number" value="<%=nutritionalNeed.age_min%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Age Max</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="age_max" type="number" value="<%=nutritionalNeed.age_max%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Giới tính</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="gender" name="gender">
          <option value="0" <%=(nutritionalNeed.gender == 0) ? 'selected' : ''%>>Nữ</option>
          <option value="1" <%=(nutritionalNeed.gender == 1) ? 'selected' : ''%>>Nam</option>
        </select>
        <script>
          $("#gender").kendoDropDownList({
            enable: true
          });
          </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label">Content</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="content" type="text" value="<%=nutritionalNeed.content%>">
    </div>
  </div>
</div>