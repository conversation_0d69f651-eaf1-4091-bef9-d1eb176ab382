<html lang="en">
<head>
    <title>Quản tr<PERSON></title>
	<%- include('../shared/head') %>
</head>
<body class="hold-transition sidebar-mini layout-fixed control-sidebar-slide-open">
    <%- include('../shared/menu',{user: user}) %>
    <div class="content-wrapper">
    	<div class="content-header">
    		<h1>Trang tổng quan</h1>
    	</div>
    	<section class="content">
    		<div class="container-fluid">
    			<div class="container-fluid">
    				<div class="row">
    					<div class="col-md-12">
    						<div class="row">
    							<div class="col-md-12">
    								<div class="card card-primary card-outline configuration-steps" id="pr-common-statistics-card">
    									<div class="card-header with-border clearfix">
    										<div class="card-title">
    											<i class="far fa-chart-bar"></i> Thống kê chung
    										</div>
    										<div class="card-tools float-right">
    											<button type="button" class="btn btn-tool" data-card-widget="collapse"> <i class="fas fa-minus"></i>
    										</div>
    									</div>
										<div class="card-body">
										   <div class="row">
										      <div class="col-lg-3 col-6">
										         <div class="small-box bg-info">
										            <div class="inner">
										               <h3>5</h3>
										               <p>Số phiếu khám chưa hoàn thành</p>
										            </div>
										            <div class="icon"><i class="ion ion-bag"></i></div>
										            <a class="small-box-footer" href="/Admin/"> Xem tất cả <i class="fas fa-arrow-circle-right"></i> </a>
										         </div>
										      </div>
										      <div class="col-lg-3 col-6">
										         <div class="small-box bg-yellow">
										            <div class="inner">
										               <h3>0</h3>
										               <p>Số phiếu khám đã hoàn thành</p>
										            </div>
										            <div class="icon"><i class="ion ion-refresh"></i></div>
										            <a class="small-box-footer" href="/Admin/"> Xem tất cả <i class="fas fa-arrow-circle-right"></i> </a>
										         </div>
										      </div>
										      <div class="col-lg-3 col-6">
										         <div class="small-box bg-green">
										            <div class="inner">
										               <h3>5</h3>
										               <p>Số lượng khách hàng</p>
										            </div>
										            <div class="icon"><i class="ion ion-person-add"></i></div>
										            <a class="small-box-footer" href="/Admin/"> Xem tất cả <i class="fas fa-arrow-circle-right"></i> </a>
										         </div>
										      </div>
										      <div class="col-lg-3 col-6">
										         <div class="small-box bg-red">
										            <div class="inner">
										               <h3>0</h3>
										               <p>Số lượng bác sĩ</p>
										            </div>
										            <div class="icon"><i class="ion ion-pie-graph"></i></div>
										            <a class="small-box-footer" href="/Admin/"> Thêm thông tin <i class="fas fa-arrow-circle-right"></i> </a>
										         </div>
										      </div>
										   </div>
										</div>
    								</div>
    							</div>
    						</div>
							<div class="row">
							   <div class="col-md-6">
							      <div class="card card-primary card-outline" id="order-statistics-card">
							         <div class="card-header with-border">
							            <h3 class="card-title"><i class="fas fa-shopping-cart"></i> Phiếu khám</h3>
							            <div class="card-tools float-right"><button class="btn btn-xs btn-info btn-flat margin-r-5" data-chart-role="toggle-chart" data-chart-period="year">Year</button> <button class="btn btn-xs btn-info btn-flat margin-r-5" data-chart-role="toggle-chart" data-chart-period="month">Month</button> <button class="btn btn-xs btn-info btn-flat bg-light-blue" data-chart-role="toggle-chart" data-chart-period="week">Week</button> <button type="button" class="btn btn-tool margin-l-10" data-card-widget="collapse"> <i class="fas fa-minus"></i> </button></div>
							         </div>
							         <div class="card-body">
							            <div class="chart" style="height:300px">
							               <div class="chartjs-size-monitor">
							                  <div class="chartjs-size-monitor-expand">
							                     <div class=""></div>
							                  </div>
							                  <div class="chartjs-size-monitor-shrink">
							                     <div class=""></div>
							                  </div>
							               </div>
							               <canvas id="order-statistics-chart" height="375" style="display: block; height: 300px; width: 562px;" width="702" class="chartjs-render-monitor"></canvas>
							            </div>
							         </div>
							      </div>
							      <script>
							      	// $(document).ready(function() {
									//     function i(t) {
									//         var i = [],
									//             r = [];
									//         $.ajax({
									//             cache: !1,
									//             type: "GET",
									//             url: "/Admin/Order/LoadOrderStatistics",
									//             data: {
									//                 period: t
									//             },
									//             success: function(t) {
									//                 for (var u = 0; u < t.length; u++) i.push(t[u].date), r.push(t[u].value);
									//                 window.orderStatistics ? (window.orderStatistics.config.data.labels = i, window.orderStatistics.config.data.datasets[0].data = r, window.orderStatistics.update()) : (n.data.labels = i, n.data.datasets[0].data = r, n.data.scales = window.orderStatistics = new Chart(document.getElementById("order-statistics-chart").getContext("2d"), n))
									//             },
									//             error: function() {
									//                 $("#loadOrderStatisticsAlert").click()
									//             }
									//         })
									//     }
									//     var t, n;
									//     $("#order-statistics-card").on("click", 'button[data-card-widget="collapse"]', function() {
									//         var n = !$("#order-statistics-card").hasClass("collapsed-card");
									//         saveUserPreferences("/Admin/Preferences/SavePreference", "Reports.HideOrderStatisticsCard", n);
									//         n ? $('#order-statistics-card button[data-chart-role="toggle-chart"]').attr("disabled", "disabled") : ($('#order-statistics-card button[data-chart-role="toggle-chart"]').removeAttr("disabled"), t || $('#order-statistics-card button[data-chart-role="toggle-chart"][data-chart-period="week"]').trigger("click"))
									//     });
									//     n = {
									//         type: "line",
									//         data: {
									//             labels: [],
									//             datasets: [{
									//                 label: "Orders",
									//                 fillColor: "rgba(60,141,188,0.9)",
									//                 strokeColor: "rgba(60,141,188,0.8)",
									//                 pointColor: "#3b8bba",
									//                 pointStrokeColor: "rgba(60,141,188,1)",
									//                 pointHighlightFill: "#fff",
									//                 pointHighlightStroke: "rgba(60,141,188,1)",
									//                 borderColor: "rgba(60, 141, 188, 0.7)",
									//                 backgroundColor: "rgba(44, 152, 214, 0.5)",
									//                 pointBorderColor: "rgba(37, 103, 142, 0.9)",
									//                 pointBackgroundColor: "rgba(60, 141, 188, 0.4)",
									//                 pointBorderWidth: 1,
									//                 data: []
									//             }]
									//         },
									//         options: {
									//             legend: {
									//                 display: !1
									//             },
									//             scales: {
									//                 xAxes: [{
									//                     display: !0,
									//                     ticks: {
									//                         userCallback: function(n, t) {
									//                             return window.orderStatistics && window.orderStatistics.config.data.labels.length > 12 ? t % 5 == 0 ? n : "" : n
									//                         }
									//                     }
									//                 }],
									//                 yAxes: [{
									//                     display: !0,
									//                     ticks: {
									//                         userCallback: function(n) {
									//                             return (n ^ 0) === n ? n : ""
									//                         },
									//                         min: 0
									//                     }
									//                 }]
									//             },
									//             showScale: !0,
									//             scaleShowGridLines: !1,
									//             scaleGridLineColor: "rgba(0,0,0,.05)",
									//             scaleGridLineWidth: 1,
									//             scaleShowHorizontalLines: !0,
									//             scaleShowVerticalLines: !0,
									//             bezierCurve: !0,
									//             pointDot: !1,
									//             pointDotRadius: 4,
									//             pointDotStrokeWidth: 1,
									//             pointHitDetectionRadius: 20,
									//             datasetStroke: !0,
									//             datasetFill: !0,
									//             maintainAspectRatio: !1,
									//             responsive: !0
									//         }
									//     };
									//     $('#order-statistics-card button[data-chart-role="toggle-chart"]').on("click", function() {
									//         var n = $(this).attr("data-chart-period");
									//         t = n;
									//         i(n);
									//         $('#order-statistics-card button[data-chart-role="toggle-chart"]').removeClass("bg-light-blue");
									//         $(this).addClass("bg-light-blue")
									//     });
									//     $('#order-statistics-card button[data-chart-role="toggle-chart"][data-chart-period="week"]').trigger("click")
									// })
							      </script>
							   </div>
							   <div class="col-md-6">
							      <div class="card card-primary card-outline" id="customer-statistics-card">
							         <div class="card-header with-border">
							            <h3 class="card-title"><i class="far fa-user"></i> Người dùng mới</h3>
							            <div class="card-tools float-right"><button class="btn btn-xs btn-info btn-flat margin-r-5" data-chart-role="toggle-chart" data-chart-period="year">Year</button> <button class="btn btn-xs btn-info btn-flat margin-r-5" data-chart-role="toggle-chart" data-chart-period="month">Month</button> <button class="btn btn-xs btn-info btn-flat bg-light-blue" data-chart-role="toggle-chart" data-chart-period="week">Week</button> <button class="btn btn-tool margin-l-10" data-card-widget="collapse"> <i class="fas fa-minus"></i> </button></div>
							         </div>
							         <div class="card-body">
							            <div class="chart" style="height:300px">
							               <div class="chartjs-size-monitor">
							                  <div class="chartjs-size-monitor-expand">
							                     <div class=""></div>
							                  </div>
							                  <div class="chartjs-size-monitor-shrink">
							                     <div class=""></div>
							                  </div>
							               </div>
							               <canvas id="customer-statistics-chart" height="375" width="702" style="display: block; height: 300px; width: 562px;" class="chartjs-render-monitor"></canvas>
							            </div>
							         </div>
							      </div>
							      <script>
							      	// $(document).ready(function() {
									//     function i(t) {
									//         var i = [],
									//             r = [];
									//         $.ajax({
									//             cache: !1,
									//             type: "GET",
									//             url: "/Admin/Customer/LoadCustomerStatistics",
									//             data: {
									//                 period: t
									//             },
									//             success: function(t) {
									//                 for (var u = 0; u < t.length; u++) i.push(t[u].date), r.push(t[u].value);
									//                 window.customerStatistics ? (window.customerStatistics.config.data.labels = i, window.customerStatistics.config.data.datasets[0].data = r, window.customerStatistics.update()) : (n.data.labels = i, n.data.datasets[0].data = r, n.data.scales = window.customerStatistics = new Chart(document.getElementById("customer-statistics-chart").getContext("2d"), n))
									//             },
									//             error: function() {
									//                 $("#loadCustomerStatisticsAlert").click()
									//             }
									//         })
									//     }
									//     var t, n;
									//     $("#customer-statistics-card").on("click", 'button[data-card-widget="collapse"]', function() {
									//         var n = !$("#customer-statistics-card").hasClass("collapsed-card");
									//         saveUserPreferences("/Admin/Preferences/SavePreference", "Reports.HideCustomerStatisticsCard", n);
									//         n ? $('#customer-statistics-card button[data-chart-role="toggle-chart"]').attr("disabled", "disabled") : ($('#customer-statistics-card button[data-chart-role="toggle-chart"]').removeAttr("disabled"), t || $('#customer-statistics-card button[data-chart-role="toggle-chart"][data-chart-period="week"]').trigger("click"))
									//     });
									//     n = {
									//         type: "line",
									//         data: {
									//             labels: [],
									//             datasets: [{
									//                 label: "New customers",
									//                 fillColor: "rgba(60,141,188,0.9)",
									//                 strokeColor: "rgba(60,141,188,0.8)",
									//                 pointColor: "#00a65a",
									//                 pointStrokeColor: "rgba(0,166,90,1)",
									//                 pointHighlightFill: "#fff",
									//                 pointHighlightStroke: "rgba(0,166,90,1)",
									//                 borderColor: "rgba(0,166,90, 1)",
									//                 backgroundColor: "rgba(0,166,90,0.5)",
									//                 pointBorderColor: "rgba(0,166,90,0.7)",
									//                 pointBackgroundColor: "rgba(0,166,90,0.2)",
									//                 pointBorderWidth: 1,
									//                 data: []
									//             }]
									//         },
									//         options: {
									//             legend: {
									//                 display: !1
									//             },
									//             scales: {
									//                 xAxes: [{
									//                     display: !0,
									//                     ticks: {
									//                         userCallback: function(n, t) {
									//                             return window.customerStatistics && window.customerStatistics.config.data.labels.length > 12 ? t % 5 == 0 ? n : "" : n
									//                         }
									//                     }
									//                 }],
									//                 yAxes: [{
									//                     display: !0,
									//                     ticks: {
									//                         userCallback: function(n) {
									//                             return (n ^ 0) === n ? n : ""
									//                         },
									//                         min: 0
									//                     }
									//                 }]
									//             },
									//             showScale: !0,
									//             scaleShowGridLines: !1,
									//             scaleGridLineColor: "rgba(0,0,0,.05)",
									//             scaleGridLineWidth: 1,
									//             scaleShowHorizontalLines: !0,
									//             scaleShowVerticalLines: !0,
									//             bezierCurve: !0,
									//             pointDot: !1,
									//             pointDotRadius: 4,
									//             pointDotStrokeWidth: 1,
									//             pointHitDetectionRadius: 20,
									//             datasetStroke: !0,
									//             datasetFill: !0,
									//             maintainAspectRatio: !1,
									//             responsive: !0
									//         }
									//     };
									//     $('#customer-statistics-card button[data-chart-role="toggle-chart"]').on("click", function() {
									//         var n = $(this).attr("data-chart-period");
									//         t = n;
									//         i(n);
									//         $('#customer-statistics-card button[data-chart-role="toggle-chart"]').removeClass("bg-light-blue");
									//         $(this).addClass("bg-light-blue")
									//     });
									//     $('#customer-statistics-card button[data-chart-role="toggle-chart"][data-chart-period="week"]').trigger("click")
									// })
							      </script>
							   </div>
							</div>
    					</div>
    				</div>
    			</div>
    		</div>
    	</section>
    </div>
    <%- include('../shared/footer') %>
</body>
</html>