﻿<input type="hidden" name="is_treatment" value="<%=is_treatment%>">
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper">
      <label class="col-form-label flex-center"><PERSON><PERSON><PERSON> thu<PERSON></label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="name" type="text" value="<%=medicine.name%>">
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Đơn vị tính</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <input class="form-control text-box single-line" name="unit" type="text" value="<%=medicine.unit%>">
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Ghi chú</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group input-group-required">
      <textarea class="form-control text-box single-line" name="description"><%=medicine.description%></textarea>
    </div>
  </div>
</div>
<div class="form-group row">
  <div class="col-md-3">
    <div class="label-wrapper flex-center">
      <label class="col-form-label">Phân loại</label>
    </div>
  </div>
  <div class="col-md-9">
    <div class="input-group-append input-group-required">
      <div class="input-group">
        <select id="type_ids" multiple name="type_ids">
          <% if (medicineType.length > 0){%>
            <% for (i = 0;i < medicineType.length;i++){%>
              <option <%=type_ids.indexOf(String(medicineType[i].id)) !== -1 ? 'selected' : ''%> value="<%=medicineType[i].id%>"><%=medicineType[i].name%></option>
            <%}%>
          <%}%>
        </select>
        <script>
          $("#type_ids").kendoMultiSelect().data("kendoMultiSelect");
          </script>
      </div>
      <div class="input-group-btn"><span class="required">*</span></div>
    </div>
  </div>
</div>

<% if(user.isAdmin){ %>
  <div class="form-group row">
    <div class="col-md-3">
      <div class="label-wrapper">
        <label class="col-form-label">Share</label>
      </div>
    </div>
    <div class="col-md-9">
      <div class="input-group">
        <input type="checkbox" <%=medicine.share == 1 ? 'checked' : ''%> name="share"/>
      </div>
    </div>
  </div>
  <% }else{ %>
    <input type="hidden" value="<%=medicine.share == 1 ? 'on' : ''%>" name="share"/>
  <% } %>