﻿var express         = require('express'),
    router          = express.Router(),
    path            = require('path'),
    moment          = require('moment'),
    returnUrl       = "/admin/log",
    notice_admin    = "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn không có quyền truy cập!",
    adminService    = require('./../models/adminModel'),
    logService      = require('../models/logModel'),
    modelService    = require('./../models/logModel'); 

router.get('/', function (req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        res.render(viewPage("list"), { 
            user: req.user,
            originalUrl: returnUrl
        });
    } catch (e) {
        adminService.addToLog(req, res, e.message);
    }
});

router.get('/view/:id', function (req, res) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        modelService.getLogById(req.params.id, function (err, result, fields) {
            if (err) {
                adminService.addToLog(req, res, err);
                return;
            }
            if(result[0] == undefined){
                adminService.addToLog(req, res, 'Không tìm thấy log có log_id=' + req.params.id);
                return;
            }
            res.render(viewPage("view"), {
                user: req.user,
                log: result[0],
                errors: [],
                moment: moment,
                originalUrl: returnUrl
            });
        })
    } catch (e) {
        adminService.addToLog(req, res, e.message);
    }
});

router.post('/delete_selected', function (req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        var selectedIds = req.body.selectedIds;
        modelService.deleteByIds(selectedIds, function (err, results, fields) {
            if (err) {
                adminService.addToLog(req, res, err);
                return;
            }
            res.redirect(returnUrl);
        })
    } catch (e) {
        adminService.addToLog(req, res, e.message);
    }
});

router.post('/', function (req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        modelService.deleteAll(function (err, results, fields) {
            if (err) {
                adminService.addToLog(req, res, err);
                return;
            }
            res.redirect(returnUrl);
        })
    } catch (e) {
        adminService.addToLog(req, res, e.message);
    }
});

router.post('/delete/:id', function (req, res, next) {
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        var record_id = isNaN(parseInt(req.params.id)) ? 0 : parseInt(req.params.id);
        modelService.delete(record_id, function (err, results, fields) {
            if (err) {
                adminService.addToLog(req, res, err);
                return;
            }
            var affectedRow = isNaN(parseInt(results.affectedRows)) ? 0 : parseInt(results.affectedRows);
            if(affectedRow > 0){
                res.redirect(returnUrl); 
            } else {
                adminService.addToLog(req, res, 'Không tìm thấy setting có log_id=' + req.params.id);
            }
        })
    } catch (e) {
        adminService.addToLog(req, res, e.message);
    }
});

router.post('/list', function (req, res, next) {
    var resultMessage = {
        "data": [],
        "error": "",
        "draw": "1",
        "recordsFiltered": 0,
        "recordsTotal": 0
    };
    try {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        if(!logService.authorizeAccess(req.user.role_id, 'log')){
            throw new Error(notice_admin);
        }
        var arrPromise = [],
            parameter  = {
                skip: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                take: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                created_on_from: req.body.created_on_from,
                created_on_to: req.body.created_on_to,
                message: req.body.message
            };

        resultMessage.draw = req.body.draw;
        arrPromise.push(new Promise(function (resolve, reject) {
            modelService.countAllLog(parameter, function (err, result, fields) {
                if (err) {
                    return modelService.create(req, err).then(function(){
                        resultMessage.error = err.sqlMessage;
                        resolve();
                    });
                }
                if (result !== undefined) {
                    resultMessage.recordsTotal    = result[0].count;
                    resultMessage.recordsFiltered = result[0].count;
                }
                resolve();
            });
        }));

        arrPromise.push(new Promise(function (resolve, reject) {
            modelService.getAllLog(parameter, function (err, result, fields) {
                if (err) {
                    return modelService.create(req, err).then(function(){
                        resultMessage.error = err.sqlMessage;
                        resolve();
                    });
                }
                if (result !== undefined) {
                    resultMessage.data = result;
                }
                resolve();
            });
        }));

        return new Promise(function (resolve, reject) {
            Promise.all(arrPromise).then(function () {
                res.send(resultMessage);
            });
        });
    } catch (e) {
        modelService.create(req, e.message).then(function(){
            resultMessage.error = e.message;
            res.send(resultMessage);
        });
    }
});

function viewPage(name) {
    return path.resolve(__dirname, "../views/log/" + name + ".ejs");
}

module.exports = router;