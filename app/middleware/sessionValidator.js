const webService = require('../web/models/webModel');

module.exports = async function(req, res, next) {
    const publicPaths = [
        '/user/login',
        '/user/signup',
        '/user/logout',
        '/user/activeaccount',
        '/public'
    ];

    // Nếu đã đăng nhập và đang truy cập trang login -> redirect về home
    if (req.user && req.path === '/user/login') {
        return res.redirect('/');
    }

    // Kiểm tra nếu path hiện tại là route công khai
    if (publicPaths.some(path => req.path.startsWith(path))) {
        return next();
    }

    // Kiểm tra nếu là request API
    const isApiRequest = req.xhr || req.headers.accept?.includes('application/json');

    // Nếu chưa đăng nhập
    if (!req.user) {
        if (isApiRequest) {
            return res.status(401).json({
                status: false,
                message: "<PERSON><PERSON> lòng đăng nhập"
            });
        } else {
            return res.redirect('/user/login?message=Vui lòng đăng nhập để tiếp tục');
        }
    }

    // Nếu đã đăng nhập, kiểm tra session
    try {
        // Cập nhật lại session_id trong user object nếu cần
        if (!req.user.session_id) {
            console.log('Updating missing session ID for user:', req.user.id);
            await webService.updateSessionId(req.user.id, req.sessionID);
            req.user.session_id = req.sessionID;
        }

        const isValidSession = await webService.validateSession(req.user.id, req.sessionID);
        
        if (!isValidSession) {
            console.log('Invalid session detected:', {
                userId: req.user.id,
                sessionId: req.sessionID,
                path: req.path
            });

            // Xóa session hiện tại
            return req.logout(function(err) {
                if (err) {
                    console.error('Logout error:', err);
                    return next(err);
                }
                if (isApiRequest) {
                    res.status(401).json({
                        status: false,
                        message: "Tài khoản của bạn đã đăng nhập ở thiết bị khác"
                    });
                } else {
                    res.redirect('/user/login?toast=error&message=Tài khoản của bạn đã đăng nhập ở thiết bị khác');
                }
            });
        }
    } catch (error) {
        console.error("Session validation error:", error);
        return next(error);
    }
    
    next();
};

