const jwtService = require('../services/jwtService');
const userService = require('../admin/models/userModel');
const roleUserService = require('../admin/models/roleUsersModel');
const webService = require('../web/models/webModel');
const multiDeviceService = require('../web/service/multiDeviceService');

// Middleware xác thực JWT cho GET requests
const isAuthenticated = async (req, res, next) => {
    try {
        const token = req.cookies.jwt_token || req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.redirect('/user/login?message=Vui lòng đăng nhập để tiếp tục');
        }

        const userData = await jwtService.getUserFromToken(token);
        if (!userData) {
            res.clearCookie('jwt_token');
            return res.redirect('/user/login?message=Phiên đăng nhập đã hết hạn');
        }

        // Kiểm tra session theo chế độ single/multi device
        const settings = await multiDeviceService.getUserSessionSettings(userData.userId);
        if (settings.allow_multiple_devices === 0 || settings.allow_multiple_devices === '0') {
            // Single device: chỉ cho phép 1 session active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession || activeSessions.length > 1) {
                res.clearCookie('jwt_token');
                return res.redirect('/user/login?message=Tài khoản đã đăng nhập ở thiết bị khác');
            }
        } else {
            // Multi device: chỉ cần token hiện tại active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession) {
                res.clearCookie('jwt_token');
                return res.redirect('/user/login?message=Phiên đăng nhập đã hết hạn hoặc bị logout');
            }
        }

        // Lấy thông tin đầy đủ của user từ database
        const fullUserData = await getUserWithRoles(userData.userId);
        if (!fullUserData) {
            res.clearCookie('jwt_token');
            return res.redirect('/user/login?message=Tài khoản không tồn tại');
        }
        // Gán tokenId vào req.user để dùng cho logout all others
        fullUserData.tokenId = userData.tokenId;
        req.user = fullUserData;
        next();
    } catch (error) {
        console.error('JWT Authentication error:', error);
        res.clearCookie('jwt_token');
        return res.redirect('/user/login?message=Lỗi xác thực');
    }
};

// Middleware xác thực JWT cho POST requests
const isAuthenticatedPost = async (req, res, next) => {
    try {
        const token = req.cookies.jwt_token || req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({
                status: false,
                message: "Vui lòng đăng nhập"
            });
        }

        const userData = await jwtService.getUserFromToken(token);
        if (!userData) {
            res.clearCookie('jwt_token');
            return res.status(401).json({
                status: false,
                message: "Phiên đăng nhập đã hết hạn"
            });
        }

        // Kiểm tra session theo chế độ single/multi device
        const settings = await multiDeviceService.getUserSessionSettings(userData.userId);
        if (settings.allow_multiple_devices === 0 || settings.allow_multiple_devices === '0') {
            // Single device: chỉ cho phép 1 session active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession || activeSessions.length > 1) {
                res.clearCookie('jwt_token');
                return res.status(401).json({
                    status: false,
                    message: "Tài khoản đã đăng nhập ở thiết bị khác"
                });
            }
        } else {
            // Multi device: chỉ cần token hiện tại active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession) {
                res.clearCookie('jwt_token');
                return res.status(401).json({
                    status: false,
                    message: "Phiên đăng nhập đã hết hạn hoặc bị logout"
                });
            }
        }

        // Lấy thông tin đầy đủ của user từ database
        const fullUserData = await getUserWithRoles(userData.userId);
        if (!fullUserData) {
            res.clearCookie('jwt_token');
            return res.status(401).json({
                status: false,
                message: "Tài khoản không tồn tại"
            });
        }
        fullUserData.tokenId = userData.tokenId;
        req.user = fullUserData;
        next();
    } catch (error) {
        console.error('JWT Authentication error:', error);
        res.clearCookie('jwt_token');
        return res.status(401).json({
            status: false,
            message: "Lỗi xác thực"
        });
    }
};

// Middleware xác thực JWT cho DataTables
const isAuthenticatedPostList = async (req, res, next) => {
    try {
        const token = req.cookies.jwt_token || req.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({
                status: false,
                message: "Vui lòng đăng nhập"
            });
        }

        const userData = await jwtService.getUserFromToken(token);
        if (!userData) {
            res.clearCookie('jwt_token');
            return res.status(401).json({
                status: false,
                message: "Phiên đăng nhập đã hết hạn"
            });
        }

        // Kiểm tra session theo chế độ single/multi device
        const settings = await multiDeviceService.getUserSessionSettings(userData.userId);
        if (settings.allow_multiple_devices === 0 || settings.allow_multiple_devices === '0') {
            // Single device: chỉ cho phép 1 session active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession || activeSessions.length > 1) {
                res.clearCookie('jwt_token');
                return res.status(401).json({
                    status: false,
                    message: "Tài khoản đã đăng nhập ở thiết bị khác"
                });
            }
        } else {
            // Multi device: chỉ cần token hiện tại active
            const activeSessions = await multiDeviceService.getActiveSessions(userData.userId, req);
            const currentSession = activeSessions.find(s => s.tokenId === userData.tokenId);
            if (!currentSession) {
                res.clearCookie('jwt_token');
                return res.status(401).json({
                    status: false,
                    message: "Phiên đăng nhập đã hết hạn hoặc bị logout"
                });
            }
        }

        // Lấy thông tin đầy đủ của user từ database
        const fullUserData = await getUserWithRoles(userData.userId);
        if (!fullUserData) {
            res.clearCookie('jwt_token');
            return res.status(401).json({
                status: false,
                message: "Tài khoản không tồn tại"
            });
        }
        fullUserData.tokenId = userData.tokenId;
        req.user = fullUserData;
        next();
    } catch (error) {
        console.error('JWT Authentication error:', error);
        res.clearCookie('jwt_token');
        return res.status(401).json({
            status: false,
            message: "Lỗi xác thực"
        });
    }
};

// Hàm lấy thông tin user với roles
const getUserWithRoles = (userId) => {
    return new Promise((resolve, reject) => {
        const arrPromise = [];
        const detailUser = {
            id: 0,
            role_id: [],
            isAdmin: false,
            name: '',
            full_name: '',
            email: '',
            phone: '',
            gender: '',
            department_id: '',
            department_name: '',
            hospital_id: '',
            hospital_name: '',
            hospital_prefix: ''
        };

        arrPromise.push(new Promise(function (resolve, reject) {
            userService.getUserById(userId, function (err, resUser, fields) {
                if (err) {
                    return webService.addToLogService(err, 'jwtAuth getUserById').then(log_id => {
                        resolve();
                    });
                }
                if (resUser !== undefined && resUser[0] !== undefined) {
                    detailUser.id = resUser[0].id;
                    detailUser.name = resUser[0].name;
                    detailUser.full_name = resUser[0].full_name;
                    detailUser.email = resUser[0].email;
                    detailUser.phone = resUser[0].phone;
                    detailUser.gender = resUser[0].gender;
                    detailUser.department_id = resUser[0].department_id;
                    detailUser.department_name = resUser[0].department_name;
                    detailUser.hospital_id = resUser[0].hospital_id;
                    detailUser.hospital_name = resUser[0].hospital_name;
                    detailUser.hospital_prefix = resUser[0].prefix;
                }
                resolve();
            });
        }));
        
        arrPromise.push(new Promise(function (resolve, reject) {
            roleUserService.getRoleByUserId(userId, function (err, result, fields) {
                if (err) {
                    return webService.addToLogService(err, 'jwtAuth getRoleByUserId').then(log_id => {
                        resolve();
                    });
                }
                if (result !== undefined) {
                    for (var i = 0; i < result.length; i++) {
                        detailUser.role_id.push(result[i].role_id);
                        if (result[i].role_id == 1) {
                            detailUser.isAdmin = true;
                        }
                    }
                }
                resolve();
            }); 
        }));

        Promise.all(arrPromise).then(function () {
            if (detailUser.id > 0) {
                resolve(detailUser);
            } else {
                resolve(null);
            }
        });
    });
};

// Middleware kiểm tra quyền admin
const isAdmin = (req, res, next) => {
    if (!req.user) {
        return res.redirect('/user/login');
    }
    
    if (!req.user.isAdmin) {
        return res.status(403).render('error/error.ejs', {
            user: req.user,
            errors: ['Bạn không có quyền truy cập trang này']
        });
    }
    
    next();
};

// Middleware kiểm tra quyền theo role
const hasRole = (requiredRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.redirect('/user/login');
        }
        
        const userRoles = req.user.role_id || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
        
        if (!hasRequiredRole) {
            return res.status(403).render('error/error.ejs', {
                user: req.user,
                errors: ['Bạn không có quyền truy cập trang này']
            });
        }
        
        next();
    };
};

module.exports = {
    isAuthenticated,
    isAuthenticatedPost,
    isAuthenticatedPostList,
    isAdmin,
    hasRole
}; 