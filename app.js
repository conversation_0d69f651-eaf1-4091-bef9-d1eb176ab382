// Load environment variables
require('dotenv').config();

var createError       = require('http-errors');
var express           = require('express');
var path              = require('path');
var cookieParser      = require('cookie-parser');
var logger            = require('morgan');
var bodyParser        = require('body-parser');
var favicon          = require('serve-favicon');

//Web
var home       = require('./app/web/controllers/homeController');
var user       = require('./app/web/controllers/userController');
var mail       = require('./app/web/controllers/sendMailController');
var examine    = require('./app/web/controllers/examineController');
var consultation = require('./app/web/controllers/consultationController');
var exportDoc  = require('./app/web/controllers/exportDocController');
var weather    = require('./app/web/controllers/weatherController');
var test       = require('./app/web/controllers/testController');
var api        = require('./app/web/controllers/apiController');
var devices = require('./app/web/controllers/devicesController');

//Admin
var admin          = require('./app/admin/controllers/adminController');
var admin_user     = require('./app/admin/controllers/userController');
var admin_role     = require('./app/admin/controllers/roleController');
var admin_setting  = require('./app/admin/controllers/settingController');
var admin_error    = require('./app/admin/controllers/errorController');
var admin_log      = require('./app/admin/controllers/logController');
var admin_hospital    = require('./app/admin/controllers/hospitalController');
var admin_department  = require('./app/admin/controllers/departmentController');
var admin_active_mode_of_living  = require('./app/admin/controllers/activeModeOfLivingController');
var admin_nutrition_advice  = require('./app/admin/controllers/nutritionAdviceController');
var admin_medicine  = require('./app/admin/controllers/medicineController');
var admin_medicine_type = require('./app/admin/controllers/medicineTypeController');
var admin_alternative_food  = require('./app/admin/controllers/alternativeFoodController');
var admin_food_type  = require('./app/admin/controllers/foodTypeController');
var admin_food_info  = require('./app/admin/controllers/foodInfoController');
var admin_medical_test  = require('./app/admin/controllers/medicalTestController');
var admin_medical_test_type = require('./app/admin/controllers/medicalTestTypeController');
var admin_menu_time  = require('./app/admin/controllers/menuTimeController');
var admin_diagnostic  = require('./app/admin/controllers/diagnosticController');
var admin_standard_weight_height  = require('./app/admin/controllers/standardWeightHeightController');
var admin_menu_example = require('./app/admin/controllers/menuExampleController');
var admin_nutritional_needs = require('./app/admin/controllers/nutritionalNeedsController');
var admin_index_by_age = require('./app/admin/controllers/indexByAgeController');
var admin_height_by_weight = require('./app/admin/controllers/heightByWeightController');
var admin_subclinical = require('./app/admin/controllers/subclinicalController');

var db              = require('./app/config/db');
var EventEmitter    = require('events').EventEmitter;
var event           = new EventEmitter();
event.setMaxListeners(0);
db.connect(db.MODE_PRODUCTION, function () {
  console.log('Conenct Database successfully');
})

var app = express();

// view engine setup
app.set('views', path.join(__dirname, '/app/web/views'));
app.set('view engine', 'ejs');

// Đảm bảo cookieParser được cấu hình trước
app.use(cookieParser('IqaXyt9DGugwUobMVtMKANLFeqZfIrSF'));

// JWT Middleware - thay thế hoàn toàn session authentication
app.use(async (req, res, next) => {
    try {
        const token = req.cookies.jwt_token || req.headers.authorization?.replace('Bearer ', '');
        
        if (token) {
            const jwtService = require('./app/services/jwtService');
            const userData = await jwtService.getUserFromToken(token);
            if (userData) {
                // Lấy thông tin đầy đủ của user từ database
                const userService = require('./app/admin/models/userModel');
                const roleUserService = require('./app/admin/models/roleUsersModel');
                const webService = require('./app/web/models/webModel');
                
                const arrPromise = [];
                const detailUser = {
                    id: 0,
                    role_id: [],
                    isAdmin: false,
                    name: '',
                    full_name: '',
                    email: '',
                    phone: '',
                    gender: '',
                    department_id: '',
                    department_name: '',
                    hospital_id: '',
                    hospital_name: '',
                    hospital_prefix: ''
                };

                arrPromise.push(new Promise(function (resolve, reject) {
                    userService.getUserById(userData.userId, function (err, resUser, fields) {
                        if (err) {
                            console.error('Error getting user by ID:', err);
                            resolve();
                        } else if (resUser !== undefined && resUser[0] !== undefined) {
                            detailUser.id = resUser[0].id;
                            detailUser.name = resUser[0].name;
                            detailUser.full_name = resUser[0].full_name;
                            detailUser.email = resUser[0].email;
                            detailUser.phone = resUser[0].phone;
                            detailUser.gender = resUser[0].gender;
                            detailUser.department_id = resUser[0].department_id;
                            detailUser.department_name = resUser[0].department_name;
                            detailUser.hospital_id = resUser[0].hospital_id;
                            detailUser.hospital_name = resUser[0].hospital_name;
                            detailUser.hospital_prefix = resUser[0].prefix;
                        }
                        resolve();
                    });
                }));
                
                arrPromise.push(new Promise(function (resolve, reject) {
                    roleUserService.getRoleByUserId(userData.userId, function (err, result, fields) {
                        if (err) {
                            console.error('Error getting user roles:', err);
                            resolve();
                        } else if (result !== undefined) {
                            for (var i = 0; i < result.length; i++) {
                                detailUser.role_id.push(result[i].role_id);
                                if (result[i].role_id == 1) {
                                    detailUser.isAdmin = true;
                                }
                            }
                        }
                        resolve();
                    }); 
                }));

                await Promise.all(arrPromise);
                if (detailUser.id > 0) {
                    detailUser.tokenId = userData.tokenId; // Thêm tokenId cho logout all others
                    req.user = detailUser;
                }
            }
        }
        // Nếu không có hospital_id thì đăng xuất
        if (req.user && !req.user.hospital_id && !req.user.department_id && req.user.role_id !== 2 && req.path !== '/user/logout' && req.path !== '/user/login' ) {
            res.clearCookie('jwt_token');
            return res.redirect('/user/login?message=User không thuộc bệnh viện nào!');
        }
        next();
    } catch (error) {
        console.error('JWT middleware error:', error);
        // Không gây lỗi 500, chỉ log và tiếp tục
        next();
    }
});

app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use(favicon(path.join(__dirname, 'public', 'content/images', 'favicon.ico')));
app.use("/public", express.static(path.join(__dirname, 'public')));

app.use(bodyParser.json({ limit: '500mb' }));
app.use(bodyParser.urlencoded({ limit: '500mb', extended: false }));
app.use(cookieParser());

//Web
app.use('/', home);
app.use('/user', user);
app.use('/examine', examine);
app.use('/consultation', consultation);
app.use('/mail', mail);
app.use('/export', exportDoc);
app.use('/weather', weather);
app.use('/test', test);
app.use('/api', api);
app.use('/devices', devices);

//Admin
app.use('/admin', admin);
app.use('/admin/user', admin_user);
app.use('/admin/role', admin_role);
app.use('/admin/setting', admin_setting);
app.use('/admin/error', admin_error);
app.use('/admin/log', admin_log);
app.use('/admin/hospital', admin_hospital);
app.use('/admin/subclinical', admin_subclinical);
app.use('/admin/department', admin_department);
app.use('/admin/active-mode-of-living', admin_active_mode_of_living);
app.use('/admin/nutrition-advice', admin_nutrition_advice);
app.use('/admin/medicine-type', admin_medicine_type);
app.use('/admin/medicine', admin_medicine);
app.use('/admin/alternative-food', admin_alternative_food);
app.use('/admin/food-type', admin_food_type);
app.use('/admin/food-info', admin_food_info);
app.use('/admin/medical-test', admin_medical_test);
app.use('/admin/medical-test-type', admin_medical_test_type);
app.use('/admin/menu-time', admin_menu_time);
app.use('/admin/diagnostic', admin_diagnostic);
app.use('/admin/standard-weight-height', admin_standard_weight_height);
app.use('/admin/menu-example', admin_menu_example);
app.use('/admin/nutritional-needs', admin_nutritional_needs);
app.use('/admin/index-by-age', admin_index_by_age);
app.use('/admin/height-by-weight', admin_height_by_weight);

app.get('/robots.txt', function (req, res) {
  res.type('text/plain');
  res.send("User-agent: *\nDisallow: /public/")
});

// catch 404 and forward to error handler
app.use(function (req, res, next) {
  res.status(404).render("error/error.ejs", {
      user: req.user,
      errors: []
  });
});

// error handler
if (app.get('env') === 'development') {
  app.use(function (err, req, res, next) {
    res.status(500).render("error/error.ejs", {
      user: req.user,
      errors: [err.message]
    });
  });
}

module.exports = app
