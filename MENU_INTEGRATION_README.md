# Tích hợp tính năng Thực đơn mẫu từ project Benh-<PERSON>han

## Tổng quan
Tính năng thực đơn mẫu đã được tích hợp từ project `benh-nhan` sang project `tu-van-dinh-duong`. T<PERSON>h năng này cho phép:

- <PERSON><PERSON><PERSON><PERSON> lý thực phẩm và món ăn thông qua API từ project benh-nhan
- Xây dựng thực đơn với cấu trúc courses (món ăn)
- Tính toán dinh dưỡng tự động
- Lưu và xuất thực đơn mẫu

## Cấu trúc files đã thay đổi

### 1. Controllers
- **tu-van-dinh-duong/app/web/controllers/apiController.js**: API endpoints để lấy dữ liệu từ benh-nhan
- **tu-van-dinh-duong/app/web/controllers/examineController.js**: Thêm API endpoints cho thực đơn mẫu

### 2. Views
- **tu-van-dinh-duong/app/web/views/examine/thucdonmau.ejs**: <PERSON><PERSON>o diện thực đơn mẫu với cấu trúc courses

### 3. JavaScript
- **tu-van-dinh-duong/public/content/js/menuExample.js**: Logic xử lý thực đơn mẫu

### 4. Configuration
- **tu-van-dinh-duong/.env**: Cấu hình URL API của project benh-nhan
- **tu-van-dinh-duong/app.js**: Load environment variables

## API Endpoints mới

### Trong apiController.js:
- `GET /api/foods` - Lấy danh sách thực phẩm từ benh-nhan
- `GET /api/dishes` - Lấy danh sách món ăn từ benh-nhan  
- `GET /api/dish-foods/:id` - Lấy chi tiết thực phẩm của món ăn
- `GET /api/test-connection` - Test kết nối đến benh-nhan

### Trong examineController.js:
- `GET /examine/api/foods` - Proxy API lấy thực phẩm
- `GET /examine/api/dishes` - Proxy API lấy món ăn
- `GET /examine/api/dish-foods/:id` - Proxy API lấy chi tiết món ăn
- `POST /examine/save-menu-example` - Lưu thực đơn mẫu
- `POST /examine/export-menu-excel` - Xuất Excel thực đơn

## Cấu hình môi trường

Trong file `.env`, cấu hình URL của project benh-nhan:
```
BENH_NHAN_API_URL=http://localhost:3001
```

## Dependencies mới
- `axios`: Để thực hiện HTTP requests
- `dotenv`: Để load environment variables

## Cách sử dụng

1. **Khởi động cả 2 projects**:
   - Project benh-nhan chạy trên port 3001
   - Project tu-van-dinh-duong chạy trên port 3002

2. **Truy cập tính năng**:
   - Vào phần "Thực đơn mẫu" trong examine
   - Chọn thực đơn mẫu có sẵn hoặc tạo mới
   - Thêm thực phẩm hoặc món ăn vào thực đơn
   - Lưu thực đơn mẫu

3. **Các tính năng chính**:
   - **Chọn mẫu**: Chọn từ danh sách thực đơn mẫu có sẵn
   - **Thêm thực phẩm**: Tìm kiếm và thêm thực phẩm với khối lượng
   - **Thêm món ăn**: Chọn món ăn (sẽ tự động thêm các thực phẩm của món)
   - **Quản lý courses**: Tổ chức thực phẩm theo từng món ăn
   - **Tính toán dinh dưỡng**: Tự động tính tổng năng lượng, protein, etc.
   - **Lưu mẫu**: Lưu thực đơn để sử dụng lại
   - **Xuất Excel**: Xuất thực đơn ra file Excel

## Lưu ý kỹ thuật

1. **Cross-project API**: Sử dụng HTTP requests để giao tiếp giữa 2 projects
2. **Error handling**: Có xử lý lỗi khi project benh-nhan không khả dụng
3. **Authentication**: Tất cả API đều yêu cầu đăng nhập
4. **Data structure**: Cấu trúc dữ liệu courses được thêm vào để tương thích
5. **Virtual Select**: Sử dụng VirtualSelect cho dropdown với search

## Troubleshooting

1. **Lỗi kết nối API**: Kiểm tra project benh-nhan có chạy không
2. **Không load được thực phẩm**: Kiểm tra URL trong .env file
3. **Lỗi JavaScript**: Kiểm tra console browser để debug
4. **Lỗi authentication**: Đảm bảo user đã đăng nhập

## Phát triển tiếp

Các tính năng có thể phát triển thêm:
- Xuất Excel thực tế (hiện tại chỉ là placeholder)
- Lưu thực đơn vào database (hiện tại chỉ trả về success)
- Thêm validation chi tiết hơn
- Cải thiện UI/UX
- Thêm tính năng copy/duplicate thực đơn
- Thêm báo cáo dinh dưỡng chi tiết
