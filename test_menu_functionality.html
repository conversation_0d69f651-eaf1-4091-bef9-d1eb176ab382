<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Menu Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>Test Menu Table Functionality</h2>
        
        <!-- Column Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>C<PERSON>u hình cột hiển thị</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Chọn cột hiển thị:</h6>
                        <div id="column_checkboxes"></div>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary" onclick="applyColumnConfig()">Áp dụng</button>
                        <button type="button" class="btn btn-secondary" onclick="resetColumnConfig()">Đặt lại</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Table -->
        <div class="card">
            <div class="card-header">
                <h5>Bảng thực đơn</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive" id="tb_menu">
                    <table class="table table-bordered table-hover">
                        <thead class="text-center">
                            <!-- Header sẽ được tạo động -->
                        </thead>
                        <tbody>
                            <!-- Dữ liệu sẽ được tạo động -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock data và cấu hình
        let currentDisplayConfig = {
            visible_columns: ['weight', 'energy', 'protein', 'fat', 'carbohydrate'],
            column_order: ['weight', 'energy', 'protein', 'fat', 'carbohydrate']
        };

        const availableColumns = {
            'weight': { label: 'Khối lượng (g)', group: 'basic', default: true },
            'energy': { label: 'Năng lượng (kcal)', group: 'main_nutrients', default: true },
            'protein': { label: 'Protein (g)', group: 'main_nutrients', default: true },
            'fat': { label: 'Chất béo (g)', group: 'main_nutrients', default: true },
            'carbohydrate': { label: 'Carbohydrate (g)', group: 'main_nutrients', default: true },
            'fiber': { label: 'Chất xơ (g)', group: 'main_nutrients', default: false },
            'calci': { label: 'Canxi (mg)', group: 'minerals', default: false },
            'fe': { label: 'Sắt (mg)', group: 'minerals', default: false }
        };

        const columnGroups = {
            'basic': 'Thông tin cơ bản',
            'main_nutrients': 'Chất dinh dưỡng chính',
            'minerals': 'Khoáng chất'
        };

        // Mock menu data
        const mockMenuData = [
            {
                id: 1,
                name: 'Bữa sáng',
                courses: [
                    { id: 1, name: 'Món chính' }
                ],
                listFood: [
                    {
                        id: 1,
                        name: 'Cơm trắng',
                        course_id: 1,
                        weight: 100,
                        energy: 130,
                        protein: 2.7,
                        fat: 0.3,
                        carbohydrate: 28,
                        fiber: 0.4,
                        calci: 10,
                        fe: 0.8
                    },
                    {
                        id: 2,
                        name: 'Thịt heo luộc',
                        course_id: 1,
                        weight: 50,
                        energy: 139,
                        protein: 19,
                        fat: 7,
                        carbohydrate: 0,
                        fiber: 0,
                        calci: 6.7,
                        fe: 0.96
                    }
                ]
            }
        ];

        // Hàm cập nhật header bảng
        function updateTableHeader() {
            const totalColumns = 1 + currentDisplayConfig.visible_columns.length + 1;
            
            let headerHtml = `
                <tr>
                    <td rowspan="2"><span>Giờ ăn</span></td>
                    <td colspan="${totalColumns}">
                        <input type="text" id="name_menu" class="form-control" style="text-align: center;" value="Thực đơn test">
                    </td>
                </tr>
                <tr>
                    <td>Thực phẩm</td>
            `;

            currentDisplayConfig.visible_columns.forEach(column => {
                const columnName = availableColumns[column].label;
                if (columnName) {
                    headerHtml += `<td>${columnName}</td>`;
                }
            });

            headerHtml += `<td style="min-width: 40px;">Thao tác</td></tr>`;
            $('#tb_menu thead').html(headerHtml);
        }

        // Hàm tạo checkboxes
        function createColumnCheckboxes() {
            let html = '';
            
            Object.keys(columnGroups).forEach(groupKey => {
                html += `<div class="mb-3">
                    <h6 class="text-secondary">${columnGroups[groupKey]}</h6>`;
                
                Object.keys(availableColumns).forEach(columnKey => {
                    const column = availableColumns[columnKey];
                    if (column.group === groupKey) {
                        const isChecked = currentDisplayConfig.visible_columns.includes(columnKey) ? 'checked' : '';
                        html += `
                            <div class="form-check">
                                <input class="form-check-input column-checkbox" type="checkbox" 
                                       id="col_${columnKey}" value="${columnKey}" ${isChecked}>
                                <label class="form-check-label" for="col_${columnKey}">
                                    ${column.label}
                                </label>
                            </div>
                        `;
                    }
                });
                
                html += '</div>';
            });
            
            $('#column_checkboxes').html(html);
        }

        // Hàm áp dụng cấu hình
        function applyColumnConfig() {
            const checkedColumns = [];
            $('.column-checkbox:checked').each(function() {
                checkedColumns.push($(this).val());
            });
            
            currentDisplayConfig.visible_columns = checkedColumns;
            currentDisplayConfig.column_order = checkedColumns;
            
            updateTableHeader();
            renderTableData();
            
            Swal.fire({
                icon: 'success',
                title: 'Thành công!',
                text: 'Đã áp dụng cấu hình hiển thị cột mới.',
                timer: 2000,
                showConfirmButton: false
            });
        }

        // Hàm đặt lại cấu hình
        function resetColumnConfig() {
            const defaultColumns = Object.keys(availableColumns).filter(key => availableColumns[key].default);
            
            currentDisplayConfig = {
                visible_columns: defaultColumns,
                column_order: defaultColumns
            };
            
            $('.column-checkbox').prop('checked', false);
            defaultColumns.forEach(col => {
                $(`#col_${col}`).prop('checked', true);
            });
            
            updateTableHeader();
            renderTableData();
        }

        // Hàm render dữ liệu bảng
        function renderTableData() {
            let tbody = '';
            
            mockMenuData.forEach(menuTime => {
                const totalRows = menuTime.courses.length + menuTime.listFood.length;
                const colspanCount = 1 + currentDisplayConfig.visible_columns.length;
                
                // First row with menu time name
                tbody += `<tr>
                    <td rowspan="${totalRows}" style="writing-mode: vertical-rl; vertical-align: middle; text-align: center;">${menuTime.name}</td>
                    <td colspan="${colspanCount}" style="text-align: center;">
                        <input type="text" class="form-control" value="${menuTime.courses[0].name}" style="text-align: center;">
                    </td>
                    <td style="text-align: center;">
                        <button class="btn btn-sm btn-outline-danger">X</button>
                    </td>
                </tr>`;
                
                // Food rows
                menuTime.listFood.forEach(food => {
                    tbody += `<tr>
                        <td>${food.name}</td>`;
                    
                    currentDisplayConfig.visible_columns.forEach(column => {
                        let value = food[column] || 0;
                        if (column === 'weight') {
                            tbody += `<td><input type="number" class="form-control form-control-sm" value="${value}" step="0.01"></td>`;
                        } else {
                            tbody += `<td>${value}</td>`;
                        }
                    });
                    
                    tbody += `<td style="text-align: center;">
                        <button class="btn btn-sm btn-outline-danger">X</button>
                    </td></tr>`;
                });
            });
            
            $('#tb_menu tbody').html(tbody);
        }

        // Khởi tạo
        $(document).ready(function() {
            createColumnCheckboxes();
            updateTableHeader();
            renderTableData();
        });
    </script>
</body>
</html>
