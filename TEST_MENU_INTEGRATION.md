# Test Tích hợp T<PERSON>h năng Thực đơn mẫu

## Checklist Test

### ✅ 1. Cấu hình và Setup
- [x] File .env đã được tạo với BENH_NHAN_API_URL
- [x] Package axios và dotenv đã được cài đặt
- [x] File examine.js đã được cập nhật với các hàm API mới
- [x] File thucdonmau.ejs đã được cập nhật với giao diện mới
- [x] API endpoints đã được thêm vào examineController.js và apiController.js

### 🔄 2. Test API Endpoints
**Cần test khi cả 2 project đang chạy:**

#### API từ benh-nhan:
- [ ] GET `/examine/api/foods` - Lấy danh sách thực phẩm
- [ ] GET `/examine/api/dishes` - L<PERSON>y danh sách món ăn  
- [ ] GET `/examine/api/dish-foods/:id` - <PERSON><PERSON><PERSON> chi tiết thực phẩm của món ăn

#### API nội bộ:
- [ ] POST `/examine/save-menu-example` - L<PERSON>u thực đơn mẫu
- [ ] POST `/examine/export-menu-excel` - Xuất Excel thực đơn

### 🔄 3. Test Giao diện

#### Chọn thực đơn mẫu:
- [ ] Dropdown "Chọn thực đơn mẫu" hiển thị đúng danh sách
- [ ] Nút "Thêm" hoạt động, thêm thực đơn vào dropdown "Thực đơn đã chọn"
- [ ] Nút "Tạo mới" tạo thực đơn trống

#### Thêm thực phẩm:
- [ ] Dropdown "Chọn giờ ăn" hiển thị đúng
- [ ] Filter "Loại thực phẩm" và "Năm dữ liệu" hoạt động
- [ ] Dropdown "Thực phẩm" search được từ API benh-nhan
- [ ] Input "Khối lượng" nhập được số
- [ ] Nút "Thêm" thêm thực phẩm vào bảng

#### Thêm món ăn:
- [ ] Dropdown "Chọn giờ ăn" hiển thị đúng
- [ ] Dropdown "Món ăn" search được từ API benh-nhan
- [ ] Nút "Thêm món ăn" thêm tất cả thực phẩm của món vào bảng

#### Cấu hình bảng:
- [ ] Nút "Cấu hình cột hiển thị" mở modal
- [ ] Modal hiển thị các checkbox cho từng cột
- [ ] Nút "Áp dụng" cập nhật header và nội dung bảng
- [ ] Bảng hiển thị đúng các cột đã chọn

#### Bảng thực đơn:
- [ ] Header bảng hiển thị đúng theo cấu hình
- [ ] Thực phẩm hiển thị đúng trong bảng
- [ ] Input "Khối lượng" trong bảng có thể chỉnh sửa
- [ ] Khi thay đổi khối lượng, các giá trị dinh dưỡng tự động cập nhật
- [ ] Nút "×" xóa thực phẩm khỏi bảng
- [ ] Footer hiển thị tổng dinh dưỡng và phần trăm

#### Lưu và xuất:
- [ ] Textarea "Ghi chú" có thể nhập
- [ ] Nút "Lưu thực đơn" hiển thị modal xác nhận
- [ ] Modal xác nhận có nút "Có" và "Không"
- [ ] Nút "Có" gọi API lưu thực đơn
- [ ] Nút "Tải thực đơn" gọi API xuất Excel

### 🔄 4. Test Tính toán

#### Tính toán dinh dưỡng:
- [ ] Khi thêm thực phẩm, giá trị dinh dưỡng tính đúng theo khối lượng
- [ ] Khi thay đổi khối lượng, tất cả giá trị dinh dưỡng cập nhật đúng
- [ ] Tổng dinh dưỡng trong footer tính đúng
- [ ] Phần trăm P, L, G tính đúng

#### Tính toán từ món ăn:
- [ ] Khi thêm món ăn, tất cả thực phẩm của món được thêm với đúng khối lượng
- [ ] Giá trị dinh dưỡng của từng thực phẩm trong món tính đúng

### 🔄 5. Test Error Handling

#### Lỗi kết nối:
- [ ] Khi project benh-nhan không chạy, hiển thị thông báo lỗi phù hợp
- [ ] Dropdown vẫn hoạt động (hiển thị "Không tìm thấy kết quả")

#### Validation:
- [ ] Không chọn giờ ăn khi thêm thực phẩm → hiển thị lỗi
- [ ] Không chọn thực phẩm khi thêm → hiển thị lỗi  
- [ ] Không nhập khối lượng hoặc nhập <= 0 → hiển thị lỗi
- [ ] Không chọn món ăn khi thêm món → hiển thị lỗi

### 🔄 6. Test Responsive

#### Mobile/Tablet:
- [ ] Giao diện hiển thị đúng trên mobile
- [ ] Các dropdown hoạt động tốt trên touch device
- [ ] Bảng có scroll ngang khi cần
- [ ] Modal hiển thị đúng trên màn hình nhỏ

## Hướng dẫn Test

### Bước 1: Khởi động projects
```bash
# Terminal 1 - Project benh-nhan
cd /home/<USER>/project/benh-nhan
npm start # hoặc node app.js

# Terminal 2 - Project tu-van-dinh-duong  
cd /home/<USER>/project/tu-van-dinh-duong
npm start # hoặc node app.js
```

### Bước 2: Truy cập tính năng
1. Mở browser, truy cập project tu-van-dinh-duong
2. Đăng nhập vào hệ thống
3. Vào phần "Khám" → chọn một phiếu khám
4. Chuyển đến tab "Thực đơn mẫu"

### Bước 3: Test từng tính năng
Thực hiện test theo checklist ở trên, đánh dấu ✅ cho các mục đã test thành công.

## Lỗi thường gặp và cách fix

### 1. Lỗi "Cannot read property of undefined"
- **Nguyên nhân**: Biến global chưa được khởi tạo
- **Fix**: Kiểm tra file examine.js đã load đúng chưa

### 2. Dropdown không hiển thị dữ liệu
- **Nguyên nhân**: API benh-nhan không hoạt động hoặc URL sai
- **Fix**: Kiểm tra .env file và project benh-nhan có chạy không

### 3. Bảng không hiển thị đúng cột
- **Nguyên nhân**: Cấu hình tableConfig chưa đúng
- **Fix**: Kiểm tra dataExamine.tableConfig trong console

### 4. Tính toán dinh dưỡng sai
- **Nguyên nhân**: Hàm caculateFoodInfo có lỗi hoặc dữ liệu từ API sai
- **Fix**: Debug hàm caculateFoodInfo và kiểm tra dữ liệu response

## Kết quả Test
- **Ngày test**: ___________
- **Người test**: ___________  
- **Tổng số tính năng**: 50+
- **Số tính năng pass**: ___/50+
- **Số tính năng fail**: ___/50+
- **Ghi chú**: ___________
