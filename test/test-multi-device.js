const jwtService = require('../app/services/jwtService');
const multiDeviceService = require('../app/web/service/multiDeviceService');
const db = require('../app/config/db');

console.log('=== Testing Multi-Device Logic ===');

// Kết nối database
db.connect(db.MODE_PRODUCTION, async function () {
    console.log('Connected to database');
    
    try {
        // Test với user_id = 13 (user có allow_multiple_devices = 1)
        const userId = 13;
        
        console.log('\n1. Kiểm tra cài đặt session của user:', userId);
        const settings = await multiDeviceService.getUserSessionSettings(userId);
        console.log('Session settings:', settings);
        
        console.log('\n2. Kiểm tra các session active trực tiếp từ DB:');
        db.get().query('SELECT * FROM user_sessions WHERE user_id = ? AND is_active = 1 ORDER BY login_at DESC', [userId], function(err, results) {
            if (err) {
                console.error('Error:', err);
            } else {
                console.log('Active sessions count:', results.length);
                console.log('Active sessions:', results);
                
                console.log('\n3. Test logic multi-device:');
                if (settings.allow_multiple_devices === 1) {
                    console.log('User đang ở chế độ multi-device');
                    if (results.length >= settings.max_sessions) {
                        console.log('Vượt quá giới hạn session, sẽ logout session cũ nhất');
                        const oldestSession = results[results.length - 1];
                        console.log('Session cũ nhất:', oldestSession);
                    } else {
                        console.log('Chưa vượt quá giới hạn, có thể tạo session mới');
                    }
                } else {
                    console.log('User đang ở chế độ single-device');
                }
                
                console.log('\n4. Kiểm tra token trong bảng user:');
                db.get().query('SELECT id, email, jwt_token_id, token_created_at FROM user WHERE id = ?', [userId], function(err, userResults) {
                    if (err) {
                        console.error('Error:', err);
                    } else {
                        console.log('User token info:', userResults[0]);
                    }
                    process.exit(0);
                });
            }
        });
        
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}); 