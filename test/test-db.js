const db = require('../app/config/db');

console.log('Testing database connection...');

db.connect(db.MODE_PRODUCTION, function () {
    console.log('Connected to database successfully');
    
    // Kiểm tra bảng user_sessions
    db.get().query('SHOW TABLES LIKE "user_sessions"', function(err, results) {
        if (err) {
            console.error('Error checking user_sessions table:', err);
            process.exit(1);
        }
        
        console.log('user_sessions table exists:', results.length > 0);
        
        if (results.length > 0) {
            // Kiểm tra số lượng session
            db.get().query('SELECT COUNT(*) as count FROM user_sessions', function(err, countResults) {
                if (err) {
                    console.error('Error counting sessions:', err);
                } else {
                    console.log('Sessions count:', countResults[0].count);
                }
                
                // Kiểm tra bảng user_session_settings
                db.get().query('SHOW TABLES LIKE "user_session_settings"', function(err, settingsResults) {
                    console.log('user_session_settings table exists:', settingsResults.length > 0);
                    
                    if (settingsResults.length > 0) {
                        db.get().query('SELECT * FROM user_session_settings LIMIT 5', function(err, settings) {
                            if (err) {
                                console.error('Error getting settings:', err);
                            } else {
                                console.log('User session settings:', settings);
                            }
                            process.exit(0);
                        });
                    } else {
                        process.exit(0);
                    }
                });
            });
        } else {
            console.log('user_sessions table does not exist - need to run migration script');
            process.exit(0);
        }
    });
}); 