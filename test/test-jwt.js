const jwtService = require('../app/services/jwtService');

async function testJWT() {
    console.log('=== Testing JWT Authentication ===\n');

    // Test 1: Tạo token
    console.log('1. Testing token creation...');
    const userData = {
        id: 1,
        email: '<EMAIL>',
        name: 'testuser',
        full_name: 'Test User',
        role_id: [1, 2],
        isAdmin: true
    };

    try {
        const tokenData = jwtService.createToken(userData.id, userData);
        console.log('✅ Token created successfully');
        console.log('Token ID:', tokenData.tokenId);
        console.log('Token:', tokenData.token.substring(0, 50) + '...');
        console.log('Payload:', tokenData.payload);
        console.log('');

        // Test 2: Xác thực token
        console.log('2. Testing token verification...');
        const verification = jwtService.verifyToken(tokenData.token);
        if (verification.valid) {
            console.log('✅ Token verified successfully');
            console.log('Payload:', verification.payload);
        } else {
            console.log('❌ Token verification failed:', verification.error);
        }
        console.log('');

        // Test 3: Lấy user từ token
        console.log('3. Testing getUserFromToken...');
        const userFromToken = await jwtService.getUserFromToken(tokenData.token);
        if (userFromToken) {
            console.log('✅ User retrieved from token successfully');
            console.log('User ID:', userFromToken.userId);
            console.log('Email:', userFromToken.email);
        } else {
            console.log('❌ Failed to get user from token');
        }
        console.log('');

        // Test 4: Làm mới token
        console.log('4. Testing token refresh...');
        const newToken = await jwtService.refreshToken(tokenData.token);
        if (newToken) {
            console.log('✅ Token refreshed successfully');
            console.log('New token:', newToken.substring(0, 50) + '...');
        } else {
            console.log('❌ Token refresh failed');
        }
        console.log('');

        // Test 5: Test với token không hợp lệ
        console.log('5. Testing invalid token...');
        const invalidToken = 'invalid.token.here';
        const invalidVerification = jwtService.verifyToken(invalidToken);
        if (!invalidVerification.valid) {
            console.log('✅ Invalid token correctly rejected');
            console.log('Error:', invalidVerification.error);
        } else {
            console.log('❌ Invalid token was accepted');
        }

    } catch (error) {
        console.error('❌ Test failed:', error);
    }

    console.log('\n=== JWT Test Completed ===');
}

// Chạy test
testJWT(); 