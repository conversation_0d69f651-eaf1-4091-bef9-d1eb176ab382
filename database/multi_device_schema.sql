-- <PERSON><PERSON><PERSON> cập nhật database để hỗ trợ đăng nhập nhiều thiết bị
-- Ch<PERSON>y script này để thêm bảng user_sessions và cập nhật bảng user

-- Cập nhật bảng user để hỗ trợ nhiều thiết bị
ALTER TABLE `user` 
ADD COLUMN `max_sessions` INT DEFAULT 5 COMMENT 'Số lượng session tối đa cho phép',
ADD COLUMN `allow_multiple_devices` TINYINT(1) DEFAULT 1 COMMENT 'Cho phép đăng nhập nhiều thiết bị';

-- Tạo bảng user_sessions để lưu tất cả session
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `jwt_token_id` VARCHAR(255) NOT NULL,
  `device_name` VARCHAR(255) NULL COMMENT 'T<PERSON><PERSON> thiết bị (tự động detect)',
  `device_type` ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
  `browser` VARCHAR(100) NULL,
  `os` VARCHAR(100) NULL,
  `device_info` TEXT NULL COMMENT 'Thông tin thiết bị chi tiết',
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `location` VARCHAR(255) NULL COMMENT 'Vị trí đăng nhập (nếu có)',
  `login_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `last_activity` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `logout_at` TIMESTAMP NULL DEFAULT NULL,
  `is_active` TINYINT(1) DEFAULT 1,
  `is_current_session` TINYINT(1) DEFAULT 0 COMMENT 'Session hiện tại',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_jwt_token_id` (`jwt_token_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_current_session` (`is_current_session`),
  KEY `idx_last_activity` (`last_activity`),
  CONSTRAINT `fk_user_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Lịch sử đăng nhập của user';

-- Tạo bảng user_session_settings để lưu cài đặt session
CREATE TABLE `user_session_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `max_sessions` INT DEFAULT 5,
  `session_timeout_hours` INT DEFAULT 24,
  `allow_multiple_devices` TINYINT(1) DEFAULT 1,
  `notify_new_login` TINYINT(1) DEFAULT 1 COMMENT 'Thông báo khi có đăng nhập mới',
  `auto_logout_inactive` TINYINT(1) DEFAULT 1 COMMENT 'Tự động logout khi không hoạt động',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_user_session_settings_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Cài đặt session của user';

-- Thêm dữ liệu mặc định cho user hiện tại
INSERT INTO `user_session_settings` (`user_id`, `max_sessions`, `session_timeout_hours`, `allow_multiple_devices`, `notify_new_login`, `auto_logout_inactive`)
SELECT `id`, 5, 24, 1, 1, 1 FROM `user` WHERE `active` = 1;

-- Tạo index để tối ưu hiệu suất
CREATE INDEX `idx_user_sessions_user_active` ON `user_sessions` (`user_id`, `is_active`);
CREATE INDEX `idx_user_sessions_token_active` ON `user_sessions` (`jwt_token_id`, `is_active`); 