# Tài liệu tính năng Xây dựng thực đơn

Đây là tài liệu mô tả chi tiết về tính năng xây dựng và quản lý thực đơn trong dự án.

## 1. Tổng quan

Tính năng này cho phép người dùng (bá<PERSON> sĩ, chuyên gia dinh dưỡng) tạo ra các thực đơn chi tiết cho bệnh nhân. Các thực đơn có thể được xây dựng từ một danh sách các thực đơn mẫu có sẵn, hoặc được tạo mới hoàn toàn bằng cách thêm các thực phẩm và món ăn riêng lẻ. Sau khi hoàn tất, người dùng có thể xuất các thực đơn đã tạo ra file Excel để lưu trữ hoặc chia sẻ.

<PERSON>á<PERSON> file chính liên quan đến tính năng:
-   **View (Giao diện):** `app/web/views/examine/thucdonmau.ejs`
-   **JavaScript (Logic):** `public/content/js/examine.js`

## 2. Chức năng chính

### 2.1. Tạo và quản lý thực đơn

-   **Tạo thực đơn mới:** Người dùng có thể tạo một hoặc nhiều thực đơn trống để bắt đầu xây dựng. Mỗi thực đơn sẽ có một tên riêng (mặc định là "Thực đơn 1", "Thực đơn 2",...).
-   **Chọn từ thực đơn mẫu:** Người dùng có thể chọn một thực đơn từ danh sách các thực đơn mẫu đã được định nghĩa trước. Toàn bộ chi tiết của thực đơn mẫu sẽ được sao chép vào một thực đơn mới trong phiếu khám của bệnh nhân.
-   **Chuyển đổi giữa các thực đơn:** Giao diện cho phép chọn và hiển thị chi tiết của từng thực đơn đã được tạo hoặc thêm vào.

### 2.2. Xây dựng thực đơn chi tiết

-   **Thêm thực phẩm:**
    -   Người dùng chọn giờ ăn (sáng, trưa, tối,...).
    -   Tìm kiếm thực phẩm từ một API ngoài. API này cung cấp thông tin dinh dưỡng chi tiết.
    -   Nhập khối lượng (weight) của thực phẩm.
    -   Hệ thống sẽ tự động tính toán các giá trị dinh dưỡng (năng lượng, protein, lipid,...) dựa trên khối lượng và dữ liệu từ API.
    -   Thực phẩm đã thêm sẽ được hiển thị trong bảng chi tiết của thực đơn đang chọn.
-   **Thêm món ăn:**
    -   Tương tự như thêm thực phẩm, người dùng có thể tìm kiếm và thêm các món ăn đã được định nghĩa sẵn.
    -   Toàn bộ thành phần thực phẩm của món ăn sẽ được thêm vào thực đơn.
-   **Chỉnh sửa và xóa:** Người dùng có thể thay đổi khối lượng của từng thực phẩm (hệ thống sẽ tự động tính lại dinh dưỡng) hoặc xóa thực phẩm khỏi thực đơn.

### 2.3. Cấu hình hiển thị bảng

-   Người dùng có thể tùy chỉnh các cột thông tin dinh dưỡng được hiển thị trong bảng chi tiết thực đơn.
-   Một cửa sổ (modal) cho phép bật/tắt hiển thị của hàng chục chỉ số dinh dưỡng khác nhau (protein, lipid, canxi, vitamin,...).
-   Sự thay đổi cấu hình sẽ được áp dụng ngay lập tức vào bảng chi tiết.

### 2.4. Tính toán tổng dinh dưỡng

-   Hệ thống tự động tính toán và hiển thị tổng giá trị dinh dưỡng của toàn bộ thực phẩm có trong thực đơn.
-   Bảng tổng hợp bao gồm tổng năng lượng (kcal) và tỷ lệ phần trăm năng lượng từ Protein, Lipid, và Carbohydrate (%P, %L, %G).

### 2.5. Xuất file Excel

-   Người dùng có thể xuất toàn bộ các thực đơn đã tạo trong phiên làm việc hiện tại ra một file Excel duy nhất.
-   **Mỗi thực đơn sẽ được tạo trên một sheet riêng biệt** trong file Excel.
-   Tên của mỗi sheet sẽ tương ứng với tên của thực đơn.
-   File Excel bao gồm:
    -   Tên thực đơn và ghi chú.
    -   Bảng chi tiết các thực phẩm theo từng giờ ăn.
    -   Các cột thông tin dinh dưỡng được hiển thị theo cấu hình của người dùng.
    -   Dòng tổng và dòng tỷ lệ phần trăm dinh dưỡng.
-   Tính năng này được thực hiện hoàn toàn ở phía client (trình duyệt) bằng thư viện `ExcelJS` và `FileSaver.js`.

## 3. Luồng dữ liệu

1.  **Tải trang:** Trình duyệt tải file `thucdonmau.ejs` và `examine.js`. Dữ liệu ban đầu về thực đơn mẫu, giờ ăn,... được truyền từ server vào EJS.
2.  **Tìm kiếm thực phẩm/món ăn:** Khi người dùng tìm kiếm, `examine.js` gửi yêu cầu AJAX đến API của một dự án khác để lấy danh sách và thông tin dinh dưỡng.
3.  **Thao tác trên giao diện:** Mọi thao tác như thêm/xóa/sửa thực phẩm, tạo thực đơn mới,... đều được xử lý ở phía client bằng JavaScript. Dữ liệu thực đơn được lưu trong biến `dataExamine.menuExamine`.
4.  **Lưu phiếu khám:** Khi người dùng lưu toàn bộ phiếu khám, dữ liệu thực đơn (`dataExamine.menuExamine`) được chuyển thành chuỗi JSON và gửi về server để lưu vào cơ sở dữ liệu.
5.  **Xuất Excel:** Khi người dùng nhấn nút "Tải thực đơn", hàm `exportMenuExample()` trong `examine.js` sẽ được gọi. Hàm này sử dụng `ExcelJS` để tạo file Excel từ dữ liệu trong biến `dataExamine.menuExamine` và dùng `FileSaver.js` để kích hoạt việc tải file về máy người dùng.
