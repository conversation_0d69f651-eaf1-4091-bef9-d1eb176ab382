{"version": 3, "sources": ["messages/kendo.messages.en-US.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "column", "columns", "columnVisibility", "clear", "done", "settings", "lock", "unlock", "DateRangePicker", "startLabel", "endLabel", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "print", "headerRows", "headerColumns", "tableSummaryPlaceholder", "associateNone", "associateScope", "associateIds", "copyFormat", "applyFormat", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "title", "and", "or", "selectValue", "value", "into", "FilterMultiCheck", "checkAll", "clearAll", "selectedItemsFormat", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "expandCollapseColumnHeader", "groupHeader", "ungroup<PERSON>eader", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "TreeListPager", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "resetSeries", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "resetSeriesWindowTitle", "deleteWindowOccurrence", "deleteWindowSeries", "deleteRecurringConfirmation", "deleteSeriesConfirmation", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "insertCommentDialog", "comment", "removeComment", "insertImageDialog", "typeError", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "colorPicker", "toolbar", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "insertComment", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "ListBox", "tools", "moveUp", "moveDown", "transferTo", "transferFrom", "transferAllTo", "transferAllFrom", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "Calendar", "weekColumnHeader", "<PERSON><PERSON>", "Confirm", "Prompt", "DateInput", "hour", "minute", "dayperiod", "List", "noData", "DropDownList", "ComboBox", "AutoComplete", "MultiColumnComboBox", "DropDownTree", "singleTag", "deleteTag", "MultiSelect", "Cha<PERSON>", "placeholder", "to<PERSON><PERSON><PERSON><PERSON>", "sendButton", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;CAgBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,QACTC,OAAU,SACVC,QAAW,WACXC,WAAc,iBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,QACTC,OAAU,SACVC,QAAW,WACXC,WAAc,iBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,iBACjBC,eAAkB,kBAClBC,OAAU,SACVC,OAAU,SACVC,QAAW,UACXC,iBAAoB,oBACpBC,MAAS,QACTX,OAAU,SACVY,KAAQ,OACRC,SAAY,uBACZC,KAAQ,OACRC,OAAU,YAMRvB,MAAMC,GAAGuB,kBACbxB,MAAMC,GAAGuB,gBAAgBrB,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuB,gBAAgBrB,UAAUC,QAAQC,UACxDoB,WAAc,QACdC,SAAY,SAMV1B,MAAMC,GAAG0B,SACb3B,MAAMC,GAAG0B,OAAOxB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0B,OAAOxB,UAAUC,QAAQC,UAC/CuB,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,cAAiB,gBACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,cACjBC,YAAe,kBACfC,aAAgB,mBAChBC,YAAe,UACfC,oBAAuB,wBACvBC,kBAAqB,sBACrBC,OAAU,SACVC,QAAW,UACXC,WAAc,mBACdC,OAAU,mBACVC,YAAe,eACfC,WAAc,cACdC,WAAc,cACdC,SAAY,YACZC,SAAY,qBACZC,gBAAmB,mBACnBC,SAAY,mBACZC,gBAAmB,mBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,QACbC,UAAa,mBACbC,MAAS,SACTC,YAAe,eACfC,WAAc,SACdC,eAAkB,aAClBC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,gBAAmB,sEACnBC,WAAc,yCACdC,cAAiB,+FACjBC,kBAAqB,4CACrBC,gBAAmB,cACnBC,aAAgB,iBAChBC,WAAc,aACdC,YAAe,cACfC,eAAkB,cAClBC,UAAa,QACbC,eAAkB,cAClBC,SAAY,OACZC,YAAe,UACfC,oBAAuB,0BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,KACzBC,aAAgB,SAChBC,gBAAmB,mBACnBC,YAAe,eACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,UAAa,aACbC,aAAgB,gBAChBC,SAAY,KACZC,YAAe,eACfC,SAAY,QACZC,QAAW,OACXC,iBAAoB,gBACpBC,QAAW,UACXC,QAAW,UACXC,MAAS,QACTC,OAAU,SACVC,MAAS,QACTC,YAAe,eACfC,YAAe,eACfC,WAAc,cACdC,UAAa,YACbC,WAAc,aACdC,SAAY,YACZC,GAAM,KACNC,OAAU,SACVC,YAAe,eACfC,gBAAmB,mBACnBC,SAAY,YACZC,0BAA6B,oBAC7BC,UAAa,aACbC,YAAe,eACfC,WAAc,cACdC,aAAgB,iBAChBC,eAAkB,mBAClBC,cAAiB,kBACjBC,gBAAmB,oBACnBC,kBAAqB,sBACrBC,iBAAoB,qBACpBC,gBAAmB,oBACnBC,kBAAqB,sBACrBC,iBAAoB,qBACpBC,YAAe,mBACf1G,QAAW,UACX2G,KAAQ,OACRC,eAAkB,mBAClBC,MAAS,QACTC,WAAc,cACdC,cAAiB,iBACjBC,wBAA2B,6CAC3BC,cAAiB,OACjBC,eAAkB,oCAClBC,aAAgB,sBAChBC,WAAc,cACdC,YAAe,kBAMbtI,MAAMC,GAAGsI,cACbvI,MAAMC,GAAGsI,YAAYpI,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsI,YAAYpI,UAAUC,QAAQC,UACpDqD,WAAc,SACdE,QAAW,aACXE,YAAe,OACfD,YAAe,OACfK,kBAAqB,4CACrBT,YAAe,eACfO,WAAc,yCACdD,gBAAmB,sEACnBE,cAAiB,+FACjBuE,cAAiB,2BACjBC,OAAU,YAMRzI,MAAMC,GAAGyI,aACb1I,MAAMC,GAAGyI,WAAWvI,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyI,WAAWvI,UAAUC,QAAQC,UACnDsI,OAAU,UACVC,QAAW,WACX7H,OAAU,SACVI,MAAS,QACT0H,SAAY,cAMV7I,MAAMC,GAAGyI,aACb1I,MAAMC,GAAGyI,WAAWvI,UAAUC,QAAQ0I,UACtChJ,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyI,WAAWvI,UAAUC,QAAQ0I,WACnDC,QACEC,GAAM,cACNC,IAAO,kBACPC,WAAc,cACdC,SAAY,WACZC,eAAkB,mBAClBC,SAAY,YACZC,OAAU,UACVC,UAAa,cACbC,QAAW,WACXC,WAAc,eACdC,cAAiB,eACjBC,iBAAoB,aAEtBC,QACEZ,GAAM,cACNC,IAAO,kBACPY,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,eACNV,OAAU,UACVC,UAAa,eAEfU,MACEjB,GAAM,cACNC,IAAO,kBACPY,IAAO,uBACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,YACNV,OAAU,UACVC,UAAa,eAEfW,OACElB,GAAM,cACNC,IAAO,kBACPK,OAAU,UACVC,UAAa,kBAObvJ,MAAMC,GAAGkK,aACbnK,MAAMC,GAAGkK,WAAWhK,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkK,WAAWhK,UAAUC,QAAQC,UACnD+J,KAAQ,8BACRC,MAAS,6BACT1B,OAAU,UACVC,QAAW,WACX7H,OAAU,SACVI,MAAS,QACTmJ,IAAO,MACPC,GAAM,KACNC,YAAe,iBACf3B,SAAY,WACZ4B,MAAS,QACTjK,OAAU,SACVY,KAAQ,OACRsJ,KAAQ,QAMN1K,MAAMC,GAAGkK,aACbnK,MAAMC,GAAGkK,WAAWhK,UAAUC,QAAQ0I,UACtChJ,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkK,WAAWhK,UAAUC,QAAQ0I,WACnDC,QACEC,GAAM,cACNC,IAAO,kBACPC,WAAc,cACdC,SAAY,WACZC,eAAkB,mBAClBC,SAAY,YACZC,OAAU,UACVC,UAAa,cACbC,QAAW,WACXC,WAAc,eACdC,cAAiB,eACjBC,iBAAoB,aAEtBC,QACEZ,GAAM,cACNC,IAAO,kBACPY,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,eACNV,OAAU,UACVC,UAAa,eAEfU,MACEjB,GAAM,cACNC,IAAO,kBACPY,IAAO,uBACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,YACNV,OAAU,UACVC,UAAa,eAEfW,OACElB,GAAM,cACNC,IAAO,kBACPK,OAAU,UACVC,UAAa,kBAObvJ,MAAMC,GAAG0K,mBACb3K,MAAMC,GAAG0K,iBAAiBxK,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0K,iBAAiBxK,UAAUC,QAAQC,UACzDuK,SAAY,aACZC,SAAY,YACZ1J,MAAS,QACTJ,OAAU,SACV0H,OAAU,SACVjI,OAAU,SACVsK,oBAAuB,qBACvB1J,KAAQ,OACRsJ,KAAQ,QAMN1K,MAAMC,GAAG8K,QACb/K,MAAMC,GAAG8K,MAAM5K,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8K,MAAM5K,UAAUC,QAAQC,UAC9C2K,SACEC,SAAY,YACZC,OAAU,WACVC,YAAe,YACfC,aAAgB,YAChBC,IAAO,iBAET7K,OAAU,SACV8K,4BAA+B,oBAC/BC,sBAAyB,cACzBC,QAAW,SACXC,QACEC,aAAgB,SAChBC,YAAe,OACfC,IAAO,MACPC,gBAAmB,WACnBC,UAAa,YACbC,qBAAwB,YACxBC,gBAAmB,YACnBC,MAAS,QACT5B,MAAS,QACT6B,YAAe,SAEjBC,KAAQ,OACRC,OACEC,IAAO,MACPT,IAAO,MACPU,MAAS,QACTL,MAAS,QACTM,KAAQ,OACRC,KAAQ,WAORxM,MAAMC,GAAGwM,OACbzM,MAAMC,GAAGwM,KAAKtM,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,KAAKtM,UAAUC,QAAQC,UAC7CqM,UACElM,OAAU,iBACVmM,WAAc,SACdC,OAAU,iBACVpB,QAAW,SACXqB,KAAQ,OACRC,MAAS,kBACTzB,IAAO,gBACPc,KAAQ,eACRY,OAAU,SACVC,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,+CAChBC,cAAiB,UAEnBC,UAAa,wBACb5E,OAAU,YACV6E,2BAA8B,GAC9BC,YAAe,8BACfC,cAAiB,mCAMfxN,MAAMC,GAAGwN,WACbzN,MAAMC,GAAGwN,SAAStN,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwN,SAAStN,UAAUC,QAAQC,UAC/CqN,OAAU,wBACVC,QAAW,aACXC,cAAiB,kBACjBC,MAAS,QACTnB,UACIG,KAAQ,OACRG,OAAU,SACVL,WAAc,SACdC,OAAU,iBACVkB,YAAe,mBACftC,QAAW,SACXsB,MAAS,kBACTzB,IAAO,oBAOXrL,MAAMC,GAAG8N,YACb/N,MAAMC,GAAG8N,UAAU5N,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,UAAU5N,UAAUC,QAAQC,UAClD2N,MAAS,mEAMPhO,MAAMC,GAAGgO,iBACbjO,MAAMC,GAAGgO,eAAe9N,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgO,eAAe9N,UAAUC,SAC/C8N,YAAe,iBACfC,cAAiB,oBAMfnO,MAAMC,GAAGmO,cACbpO,MAAMC,GAAGmO,YAAYjO,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmO,YAAYjO,UAAUC,QAAQC,UACpDgO,MAAS,QACTC,KAAQ,OACRC,KAAQ,OACRC,OAAU,SACVC,QAAW,UACXC,WAAc,iBAMZ1O,MAAMC,GAAG0O,QACb3O,MAAMC,GAAG0O,MAAMxO,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0O,MAAMxO,UAAUC,QAAQC,UAC9CuO,SAAY,MACZC,QAAW,yBACXb,MAAS,sBACTc,KAAQ,OACRC,GAAM,SACNC,aAAgB,iBAChBC,MAAS,uBACTC,SAAY,0BACZC,KAAQ,sBACRC,KAAQ,sBACRC,QAAW,UACXC,UAAa,gBAMXtP,MAAMC,GAAGsP,gBACbvP,MAAMC,GAAGsP,cAAcpP,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsP,cAAcpP,UAAUC,QAAQC,UACtDuO,SAAY,MACZC,QAAW,yBACXb,MAAS,sBACTc,KAAQ,OACRC,GAAM,SACNC,aAAgB,iBAChBC,MAAS,uBACTC,SAAY,0BACZC,KAAQ,sBACRC,KAAQ,sBACRC,QAAW,UACXC,UAAa,gBAMXtP,MAAMC,GAAGuP,YACbxP,MAAMC,GAAGuP,UAAUrP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuP,UAAUrP,UAAUC,QAAQC,UAClDoP,cAAiB,wBACjBC,aAAgB,0BAChBC,UAAa,2BAMX3P,MAAMC,GAAG2P,iBACb5P,MAAMC,GAAG2P,eAAezP,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2P,eAAezP,UAAUC,QAAQC,UACvD+J,KAAQ,8BACRyF,aAAgB,gBAChB9O,OAAU,SACV+O,QAAW,oBACXzF,MAAS,oBACTlJ,MAAS,QACT4O,GAAM,KACNvP,OAAU,SACVsI,WACEK,SAAY,WACZC,eAAkB,mBAClBF,WAAc,cACdG,SAAY,YACZL,GAAM,cACNC,IAAO,sBAOPjJ,MAAMC,GAAG+P,mBACbhQ,MAAMC,GAAG+P,iBAAiB7P,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+P,iBAAiB7P,UAAUC,QAAQC,UACzD4P,aACEC,MAAS,QACTC,OAAU,SACVC,MAAS,QACTC,OAAU,SACVC,QAAW,UACXC,OAAU,UAEZJ,QACEK,YAAe,iBACfC,SAAY,YAEdL,OACEI,YAAe,iBACfC,SAAY,WAEdJ,QACEI,SAAY,WACZD,YAAe,iBACfE,SAAY,eAEdJ,SACEE,YAAe,iBACfE,SAAY,cACZD,SAAY,YACZpE,IAAO,QAETkE,QACEC,YAAe,iBACfE,SAAY,cACZD,SAAY,WACZ1B,GAAM,QAERnD,KACE+E,MAAS,OACTC,YAAe,OACfV,MAAS,QACTW,MAAS,SACTC,WAAc,iBACdC,GAAM,OAERC,iBACE/B,MAAS,QACTgC,OAAU,SACVC,MAAS,QACTC,OAAU,SACV/B,KAAQ,QAEVgC,UACE/E,IAAO,MACPgF,QAAW,UACXC,QAAW,kBAOXtR,MAAMC,GAAGsR,YACbvR,MAAMC,GAAGsR,UAAUpR,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsR,UAAUpR,UAAUC,QAAQC,UAClDmR,OAAU,UACVvH,KAAQ,OACRwH,MAAS,QACTC,KAAQ,OACRC,YAAe,gBACfC,YAAe,sBACfC,MAAS,QACT1F,KAAQ,OACR3L,OAAU,SACVgL,QAAW,SACXsG,YAAe,eACfC,kBAAqB,eACrBC,cAAiB,+BACjBC,eAAkB,wBAClBhF,UACEE,aAAgB,+CAElBf,OACEC,IAAO,MACPE,KAAQ,OACR2F,SAAY,YACZC,OAAU,SACV7F,MAAS,SAEX8F,oBACEL,kBAAqB,wBACrBM,uBAA0B,eAC1BC,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,4BAA+B,yDAC/BC,yBAA4B,oDAC5BC,gBAAmB,sBACnBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,wEACnBC,cAAiB,uEAEnBrH,QACEpB,MAAS,QACT4B,MAAS,QACTL,IAAO,MACPmH,YAAe,gBACfC,YAAe,cACfC,OAAU,SACVC,SAAY,IACZC,cAAiB,iBACjBC,YAAe,eACfC,kBAAqB,wCACrBC,oBAAuB,YACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,cACd9H,YAAe,YAOf3L,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAASsT,gBACpD3T,MAAM0T,YAAYrT,SAASsT,cAC3B7T,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAASsT,eACxCC,WAAc,cACdC,cAAiB,iBACjBC,wBAA2B,4BAC3BC,sBAAyB,0BACzBC,eAAkB,kBAClBC,WAAc,cACdC,UAAa,aACbC,YAAe,eACfC,aAAgB,gBAChBC,UAAa,YACbC,MAAS,cACTC,YAAe,kBACfhU,MAAS,QACTC,OAAU,YAIRR,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAASmU,UACpDxU,MAAM0T,YAAYrT,SAASmU,QAC3B1U,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAASmU,SACxCjU,MAAS,QACT4L,KAAQ,OACR3L,OAAU,SACViU,OAAU,SACV5G,MAAS,QACT6G,OAAU,SACVC,OAAU,KACVC,mBACEvK,MAAS,SACTwK,YACEjL,OAAU,SACVkL,SAAY,WACZ7K,KAAQ,SAGZ8K,kBACE1K,MAAS,QAEX2K,gBACE3K,MAAS,aAEX4K,eACE5K,MAAS,WAEX6K,iBACE7K,MAAS,YACT8K,SACCC,aAAgB,aAChBlT,cAAiB,SACjBE,aAAgB,cAChBC,YAAe,UACfgT,SAAY,YACZC,YAAe,eACfC,YAAe,iBAGlBC,aACEnL,MAAS,cACT8K,SACEM,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,YAGfC,cACExL,MAAS,eACT8K,SACEW,YAAe,eACfC,WAAc,cACdC,cAAiB,iBACjBC,SAAY,mBAGhBC,oBACEC,KAAQ,8CACR9L,MAAS,gBAEX+L,kBACE/L,MAAS,kBACTgM,YAAe,sCACfC,UAAa,iBACbC,UACEC,IAAO,YACP5M,OAAU,SACVuM,KAAQ,OACRlM,KAAQ,OACRwM,OAAU,iBACVC,KAAQ,QAEVC,WACEC,YAAe,eACfC,SAAY,YACZC,QAAW,UACXC,WAAc,cACdC,QAAW,WACXC,WAAc,eACdC,qBAAwB,2BACxBC,kBAAqB,yBAEvBC,kBACER,YAAe,mBACfC,SAAY,gBACZC,QAAW,sBACXC,WAAc,0BACdC,QAAW,eACXC,WAAc,mBACdC,qBAAwB,+BACxBC,kBAAqB,4BACrBV,OAAU,mCAEZY,QACEd,SAAY,WACZe,SAAY,WACZC,IAAO,MACPC,IAAO,MACP/M,MAAS,QACTwB,MAAS,QACTL,IAAO,MACP6L,cAAiB,kBACjBC,YAAe,eACfC,YAAe,eACfC,SAAY,YACZtB,UAAa,aACbD,YAAe,eACfwB,YAAe,gBAEjBC,cACEC,UAAa,aACbC,YAAe,iBAGnBC,gBACE5N,MAAS,YACTgN,QACEa,SAAY,YACZC,WAAc,eACdC,WAAc,SACdC,UAAa,aACbC,QAAW,UACXC,YAAe,cACfzQ,MAAS,QACT0Q,WAAc,aACdC,OAAU,SACVC,aAAgB,eAChBC,WAAc,eAGlBC,oBACEC,aAAgB,wCAElBC,mBACEzO,MAAS,sBACTwO,aAAgB,+FAChBxB,QACE0B,QAAW,WACXC,OAAU,UACVC,SAAY,cAGhBC,4BACEL,aAAgB,0DAElBM,qBACE9O,MAAS,iBACTgN,QACE+B,QAAW,UACXC,cAAiB,mBAGrBC,mBACEjP,MAAS,eACTD,KAAQ,yCACRmP,UAAa,6CAKbvZ,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAASmZ,aACpDxZ,MAAM0T,YAAYrT,SAASmZ,WAC3B1Z,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAASmZ,YACxC3Y,cAAiB,oBACjBC,eAAkB,oBAClB2Y,cAAiB,kBACjBC,kBAAqB,sBACrBnZ,MAAS,QACTkI,OAAU,SACVkR,aAAgB,2BAChBxY,MAAS,QACTyY,OAAU,WACVC,aAAgB,OAChBvP,IAAO,MACPC,GAAM,KACNzB,WACEC,QACEI,SAAY,gBACZC,eAAkB,wBAClBF,WAAc,mBACdG,SAAY,kBAEdY,MACEjB,GAAO,UACPC,IAAO,cACPe,GAAO,iBACPF,GAAO,iBAETF,QACEZ,GAAM,cACNC,IAAO,kBACPY,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,oBAMRhK,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAASyZ,cACpD9Z,MAAM0T,YAAYrT,SAASyZ,YAC3Bha,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAASyZ,aACxCxF,MAAS,cACTC,YAAe,kBACfhU,MAAS,QACTC,OAAU,YAIRR,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAAS0Z,UACpD/Z,MAAM0T,YAAYrT,SAAS0Z,QAC3Bja,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAAS0Z,SACxC5U,cAAiB,kBACjBC,eAAkB,mBAClBC,YAAe,gBACfC,YAAe,gBACfgB,UAAa,YACb0T,kBACE5E,aAAgB,aAChBlT,cAAiB,SACjBE,aAAgB,cAChBC,YAAe,UACfgT,SAAY,YACZC,YAAe,eACfC,YAAe,gBAEjB0E,gBAAmB,aACnBrY,KAAQ,OACRsY,QAAW,UACXJ,aACExF,MAAS,cACTC,YAAe,mBAEjB4F,KAAQ,OACRC,IAAO,MACP5U,aAAgB,gBAChBD,UAAa,aACb8U,YAAe,uBACftZ,OAAU,SACVuZ,WAAc,OACdpX,SAAY,YACZqX,OAAU,mBACVC,aACEC,UAAa,YACb7Q,OAAU,SACV8Q,QAAW,UACXC,UAAa,YACb7F,SAAY,WACZ7K,KAAQ,OACRyH,KAAQ,OACRkJ,SAAY,YACZC,SAAY,WACZC,YAAe,mBAEjBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,eACVC,eACEpF,YAAe,eACfC,WAAc,cACdC,cAAiB,iBACjBC,SAAY,kBAEdkF,cAAiB,iBACjBvY,YAAe,eACff,OAAU,SACVuZ,MAAS,cACTC,cACE5F,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,WAEb0F,KAAQ,UACRC,MAAS,QACTC,aACEC,KAAQ,OACRC,KAAQ,QAEVC,OAAU,aACVC,QAAW,iBACXC,SAAY,kBACZC,aACEC,aAAgB,oBAChBC,cAAiB,oBACjBC,aAAgB,oBAChBC,cAAiB,qBAEnBC,UAAa,aACbC,SAAY,YACZta,UAAa,YACbua,WAAc,wBAIZrc,MAAM0T,aAAe1T,MAAM0T,YAAYrT,SAASic,OACpDtc,MAAM0T,YAAYrT,SAASic,KAC3Bxc,EAAEQ,QAAO,EAAMN,MAAM0T,YAAYrT,SAASic,MACxCC,QACEC,sBAAyB,sIACzBC,4BAA+B,0DAC/BC,gBAAmB,6EAErBC,MACEC,KAAQ,OACRC,OAAU,SACVC,KAAQ,WAOR9c,MAAMC,GAAG8c,SACb/c,MAAMC,GAAG8c,OAAO5c,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8c,OAAO5c,UAAUC,SACvC4c,oBAAuB,WACvBC,oBAAuB,cAMrBjd,MAAMC,GAAGid,UACbld,MAAMC,GAAGid,QAAQ/c,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGid,QAAQ/c,UAAUC,QAAQC,UAChD8c,OACE1I,OAAU,SACV2I,OAAU,UACVC,SAAY,YACZC,WAAc,cACdC,aAAgB,gBAChBC,cAAiB,kBACjBC,gBAAmB,wBAOnBzd,MAAMC,GAAGwN,WACbzN,MAAMC,GAAGwN,SAAStN,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwN,SAAStN,UAAUC,QAAQC,UACjDqN,OAAU,wBACVC,QAAW,aACXC,cAAiB,kBACjBC,MAAS,QACTnB,UACIG,KAAQ,OACRG,OAAU,SACVL,WAAc,SACdC,OAAU,iBACVkB,YAAe,mBACftC,QAAW,SACXsB,MAAS,kBACTzB,IAAO,oBAOTrL,MAAMC,GAAGyd,WACb1d,MAAMC,GAAGyd,SAASvd,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyd,SAASvd,UAAUC,QAAQC,UACjDsN,QAAW,aACXC,cAAiB,kBACjBC,MAAS,WAMP7N,MAAMC,GAAG0d,SACb3d,MAAMC,GAAG0d,OAAOxd,UAAUC,QAAQwd,aAClC9d,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0d,OAAOxd,UAAUC,QAAQwd,cAC/C7Q,OAAU,kBACVvM,OAAU,SACVqN,MAAS,QACT4G,OAAU,SACVoJ,mBAAsB,QACtBC,oBAAuB,eACvBtV,cAAiB,4BACjBuV,gBAAmB,YACnBC,eAAkB,WAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,eACzBC,qBAAwB,OACxBC,mBAAsB,uBACtBC,mBAAsB,uBACtBC,qBAAwB,4BAMtBve,MAAMC,GAAGue,YACbxe,MAAMC,GAAGue,UAAUre,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGue,UAAUre,UAAUC,QAAQC,UAClDoe,SAAY,kBACZC,QAAW,mBACXnH,IAAO,6CACPC,IAAO,6CACPmH,KAAQ,mBACRC,MAAS,yBACTC,IAAO,uBACP5U,KAAQ,wBACR6U,YAAe,gEAKb9e,MAAMC,GAAG8e,WACb/e,MAAMC,GAAG8e,SAAS1e,SAClBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8e,SAAS1e,UAC7BsN,QAAS,gBAMT3N,MAAMC,GAAG+e,SACbhf,MAAMC,GAAG+e,OAAO7e,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+e,OAAO7e,UAAUC,QAAQwd,cAC/CqB,MAAS,WAKPjf,MAAMC,GAAGif,WACblf,MAAMC,GAAGif,SAAS/e,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGif,SAAS/e,UAAUC,QAAQC,UACjD8e,iBAAoB,MAMlBnf,MAAMC,GAAGmf,QACbpf,MAAMC,GAAGmf,MAAMjf,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmf,MAAMjf,UAAUC,QAAQwd,cAC9CjJ,OAAU,QAMR3U,MAAMC,GAAGof,UACbrf,MAAMC,GAAGof,QAAQlf,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGof,QAAQlf,UAAUC,QAAQwd,cAChDjJ,OAAU,KACVnU,OAAU,YAKRR,MAAMC,GAAGqf,SACbtf,MAAMC,GAAGqf,OAAOnf,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqf,OAAOnf,UAAUC,QAAQwd,cAC/CjJ,OAAU,KACVnU,OAAU,YAKRR,MAAMC,GAAGsf,YACXvf,MAAMC,GAAGsf,UAAUpf,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsf,UAAUpf,UAAUC,QAAQC,UAClDmM,KAAQ,OACRF,MAAS,QACTD,IAAO,MACPgF,QAAW,kBACXmO,KAAQ,QACRC,OAAU,UACVxO,OAAU,UACVyO,UAAa,WAMf1f,MAAMC,GAAG0f,OACT3f,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,UAC7Cc,MAAS,QACTye,OAAU,oBAMZ5f,MAAMC,GAAG4f,eACT7f,MAAMC,GAAG4f,aAAa1f,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4f,aAAa1f,UAAUC,QAAQC,SAAUL,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,WAKjGL,MAAMC,GAAG6f,WACT9f,MAAMC,GAAG6f,SAAS3f,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6f,SAAS3f,UAAUC,QAAQC,SAAUL,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,WAK7FL,MAAMC,GAAG8f,eACT/f,MAAMC,GAAG8f,aAAa5f,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8f,aAAa5f,UAAUC,QAAQC,SAAUL,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,WAKjGL,MAAMC,GAAG+f,sBACThgB,MAAMC,GAAG+f,oBAAoB7f,UAAUC,QAAQC,SAC/CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+f,oBAAoB7f,UAAUC,QAAQC,SAAUL,MAAMC,GAAG0f,KAAKxf,UAAUC,QAAQC,WAKxGL,MAAMC,GAAGggB,eACTjgB,MAAMC,GAAGggB,aAAa9f,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGggB,aAAa9f,UAAUC,QAAQC,UACnD6f,UAAa,mBACb/e,MAAS,QACTgf,UAAa,SACbP,OAAU,oBAMd5f,MAAMC,GAAGmgB,cACTpgB,MAAMC,GAAGmgB,YAAYjgB,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmgB,YAAYjgB,UAAUC,QAAQC,UAClD6f,UAAa,mBACb/e,MAAS,QACTgf,UAAa,SACbP,OAAU,oBAMd5f,MAAMC,GAAGogB,OACTrgB,MAAMC,GAAGogB,KAAKlgB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGogB,KAAKlgB,UAAUC,QAAQC,UAC3CigB,YAAe,oBACfC,aAAgB,iBAChBC,WAAc,mBAInBC,OAAOzgB,MAAM0gB", "file": "kendo.messages.en-US.min.js", "sourcesContent": ["/*!\n * Copyright 2020 Progress Software Corporation and/or one of its subsidiaries or affiliates. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\",\n  \"noColor\": \"no color\",\n  \"clearColor\": \"Clear color\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\",\n  \"noColor\": \"no color\",\n  \"clearColor\": \"Clear color\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Sort Ascending\",\n  \"sortDescending\": \"Sort Descending\",\n  \"filter\": \"Filter\",\n  \"column\": \"Column\",\n  \"columns\": \"Columns\",\n  \"columnVisibility\": \"Column Visibility\",\n  \"clear\": \"Clear\",\n  \"cancel\": \"Cancel\",\n  \"done\": \"Done\",\n  \"settings\": \"Edit Column Settings\",\n  \"lock\": \"Lock\",\n  \"unlock\": \"Unlock\"\n});\n}\n\n/* DateRangePicker messages */\n\nif (kendo.ui.DateRangePicker) {\nkendo.ui.DateRangePicker.prototype.options.messages =\n$.extend(true, kendo.ui.DateRangePicker.prototype.options.messages,{\n  \"startLabel\": \"Start\",\n  \"endLabel\": \"End\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Bold\",\n  \"italic\": \"Italic\",\n  \"underline\": \"Underline\",\n  \"strikethrough\": \"Strikethrough\",\n  \"superscript\": \"Superscript\",\n  \"subscript\": \"Subscript\",\n  \"justifyCenter\": \"Center text\",\n  \"justifyLeft\": \"Align text left\",\n  \"justifyRight\": \"Align text right\",\n  \"justifyFull\": \"Justify\",\n  \"insertUnorderedList\": \"Insert unordered list\",\n  \"insertOrderedList\": \"Insert ordered list\",\n  \"indent\": \"Indent\",\n  \"outdent\": \"Outdent\",\n  \"createLink\": \"Insert hyperlink\",\n  \"unlink\": \"Remove hyperlink\",\n  \"insertImage\": \"Insert image\",\n  \"insertFile\": \"Insert file\",\n  \"insertHtml\": \"Insert HTML\",\n  \"viewHtml\": \"View HTML\",\n  \"fontName\": \"Select font family\",\n  \"fontNameInherit\": \"(inherited font)\",\n  \"fontSize\": \"Select font size\",\n  \"fontSizeInherit\": \"(inherited size)\",\n  \"formatBlock\": \"Format\",\n  \"formatting\": \"Format\",\n  \"foreColor\": \"Color\",\n  \"backColor\": \"Background color\",\n  \"style\": \"Styles\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"uploadFile\": \"Upload\",\n  \"overflowAnchor\": \"More tools\",\n  \"orderBy\": \"Arrange by:\",\n  \"orderBySize\": \"Size\",\n  \"orderByName\": \"Name\",\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\n  \"overwriteFile\": 'A file with name \"{0}\" already exists in the current directory. Do you want to overwrite it?',\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"imageWebAddress\": \"Web address\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWidth\": \"Width (px)\",\n  \"imageHeight\": \"Height (px)\",\n  \"fileWebAddress\": \"Web address\",\n  \"fileTitle\": \"Title\",\n  \"linkWebAddress\": \"Web address\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"dialogUpdate\": \"Update\",\n  \"dialogInsert\": \"Insert\",\n  \"dialogButtonSeparator\": \"or\",\n  \"dialogCancel\": \"Cancel\",\n  \"cleanFormatting\": \"Clean formatting\",\n  \"createTable\": \"Create table\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteRow\": \"Delete row\",\n  \"deleteColumn\": \"Delete column\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Table Wizard\",\n  \"tableTab\": \"Table\",\n  \"cellTab\": \"Cell\",\n  \"accessibilityTab\": \"Accessibility\",\n  \"caption\": \"Caption\",\n  \"summary\": \"Summary\",\n  \"width\": \"Width\",\n  \"height\": \"Height\",\n  \"units\": \"Units\",\n  \"cellSpacing\": \"Cell Spacing\",\n  \"cellPadding\": \"Cell Padding\",\n  \"cellMargin\": \"Cell Margin\",\n  \"alignment\": \"Alignment\",\n  \"background\": \"Background\",\n  \"cssClass\": \"CSS Class\",\n  \"id\": \"ID\",\n  \"border\": \"Border\",\n  \"borderStyle\": \"Border Style\",\n  \"collapseBorders\": \"Collapse borders\",\n  \"wrapText\": \"Wrap text\",\n  \"associateCellsWithHeaders\": \"Associate headers\",\n  \"alignLeft\": \"Align Left\",\n  \"alignCenter\": \"Align Center\",\n  \"alignRight\": \"Align Right\",\n  \"alignLeftTop\": \"Align Left Top\",\n  \"alignCenterTop\": \"Align Center Top\",\n  \"alignRightTop\": \"Align Right Top\",\n  \"alignLeftMiddle\": \"Align Left Middle\",\n  \"alignCenterMiddle\": \"Align Center Middle\",\n  \"alignRightMiddle\": \"Align Right Middle\",\n  \"alignLeftBottom\": \"Align Left Bottom\",\n  \"alignCenterBottom\": \"Align Center Bottom\",\n  \"alignRightBottom\": \"Align Right Bottom\",\n  \"alignRemove\": \"Remove Alignment\",\n  \"columns\": \"Columns\",\n  \"rows\": \"Rows\",\n  \"selectAllCells\": \"Select All Cells\",\n  \"print\": \"Print\",\n  \"headerRows\": \"Header Rows\",\n  \"headerColumns\": \"Header Columns\",\n  \"tableSummaryPlaceholder\": \"Summary attribute is not HTML5 compatible.\",\n  \"associateNone\": \"None\",\n  \"associateScope\": \"Associate using 'scope' attribute\",\n  \"associateIds\": \"Associate using Ids\",\n  \"copyFormat\": \"Copy format\",\n  \"applyFormat\": \"Apply format\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Upload\",\n  \"orderBy\": \"Arrange by\",\n  \"orderByName\": \"Name\",\n  \"orderBySize\": \"Size\",\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"overwriteFile\": \"A file with name \\\"{0}\\\" already exists in the current directory. Do you want to overwrite it?\",\n  \"dropFilesHere\": \"drop file here to upload\",\n  \"search\": \"Search\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"is true\",\n  \"isFalse\": \"is false\",\n  \"filter\": \"Filter\",\n  \"clear\": \"Clear\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"startswith\": \"Starts with\",\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"endswith\": \"Ends with\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\",\n    \"isempty\": \"Is empty\",\n    \"isnotempty\": \"Is not empty\",\n    \"isnullorempty\": \"Has no value\",\n    \"isnotnullorempty\": \"Has value\"\n  },\n  \"number\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is greater than or equal to\",\n    \"gt\": \"Is greater than\",\n    \"lte\": \"Is less than or equal to\",\n    \"lt\": \"Is less than\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"date\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is after or equal to\",\n    \"gt\": \"Is after\",\n    \"lte\": \"Is before or equal to\",\n    \"lt\": \"Is before\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"enums\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Show items with value that:\",\n  \"title\": \"Show items with value that\",\n  \"isTrue\": \"is true\",\n  \"isFalse\": \"is false\",\n  \"filter\": \"Filter\",\n  \"clear\": \"Clear\",\n  \"and\": \"And\",\n  \"or\": \"Or\",\n  \"selectValue\": \"-Select value-\",\n  \"operator\": \"Operator\",\n  \"value\": \"Value\",\n  \"cancel\": \"Cancel\",\n  \"done\": \"Done\",\n  \"into\": \"in\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"startswith\": \"Starts with\",\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"endswith\": \"Ends with\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\",\n    \"isempty\": \"Is empty\",\n    \"isnotempty\": \"Is not empty\",\n    \"isnullorempty\": \"Has no value\",\n    \"isnotnullorempty\": \"Has value\"\n  },\n  \"number\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is greater than or equal to\",\n    \"gt\": \"Is greater than\",\n    \"lte\": \"Is less than or equal to\",\n    \"lt\": \"Is less than\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"date\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is after or equal to\",\n    \"gt\": \"Is after\",\n    \"lte\": \"Is before or equal to\",\n    \"lt\": \"Is before\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"enums\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Select All\",\n  \"clearAll\": \"Clear All\",\n  \"clear\": \"Clear\",\n  \"filter\": \"Filter\",\n  \"search\": \"Search\",\n  \"cancel\": \"Cancel\",\n  \"selectedItemsFormat\": \"{0} items selected\",\n  \"done\": \"Done\",\n  \"into\": \"in\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Add Child\",\n    \"append\": \"Add Task\",\n    \"insertAfter\": \"Add Below\",\n    \"insertBefore\": \"Add Above\",\n    \"pdf\": \"Export to PDF\"\n  },\n  \"cancel\": \"Cancel\",\n  \"deleteDependencyWindowTitle\": \"Delete dependency\",\n  \"deleteTaskWindowTitle\": \"Delete task\",\n  \"destroy\": \"Delete\",\n  \"editor\": {\n    \"assingButton\": \"Assign\",\n    \"editorTitle\": \"Task\",\n    \"end\": \"End\",\n    \"percentComplete\": \"Complete\",\n    \"resources\": \"Resources\",\n    \"resourcesEditorTitle\": \"Resources\",\n    \"resourcesHeader\": \"Resources\",\n    \"start\": \"Start\",\n    \"title\": \"Title\",\n    \"unitsHeader\": \"Units\"\n  },\n  \"save\": \"Save\",\n  \"views\": {\n    \"day\": \"Day\",\n    \"end\": \"End\",\n    \"month\": \"Month\",\n    \"start\": \"Start\",\n    \"week\": \"Week\",\n    \"year\": \"Year\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Cancel changes\",\n    \"canceledit\": \"Cancel\",\n    \"create\": \"Add new record\",\n    \"destroy\": \"Delete\",\n    \"edit\": \"Edit\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Save changes\",\n    \"select\": \"Select\",\n    \"update\": \"Update\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Cancel\",\n    \"confirmation\": \"Are you sure you want to delete this record?\",\n    \"confirmDelete\": \"Delete\"\n  },\n  \"noRecords\": \"No records available.\",\n  \"search\": \"Search...\",\n  \"expandCollapseColumnHeader\": \"\",\n  \"groupHeader\": \"Press ctrl + space to group\",\n  \"ungroupHeader\": \"Press ctrl + space to ungroup\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n    \"noRows\": \"No records to display\",\n    \"loading\": \"Loading...\",\n    \"requestFailed\": \"Request failed.\",\n    \"retry\": \"Retry\",\n    \"commands\": {\n        \"edit\": \"Edit\",\n        \"update\": \"Update\",\n        \"canceledit\": \"Cancel\",\n        \"create\": \"Add new record\",\n        \"createchild\": \"Add child record\",\n        \"destroy\": \"Delete\",\n        \"excel\": \"Export to Excel\",\n        \"pdf\": \"Export to PDF\"\n    }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Drag a column header and drop it here to group by that column\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Increase value\",\n  \"downArrowText\": \"Decrease value\"\n});\n}\n\n/* MediaPlayer messages */\n\nif (kendo.ui.MediaPlayer) {\nkendo.ui.MediaPlayer.prototype.options.messages =\n$.extend(true, kendo.ui.MediaPlayer.prototype.options.messages,{\n  \"pause\": \"Pause\",\n  \"play\": \"Play\",\n  \"mute\": \"Mute\",\n  \"unmute\": \"Unmute\",\n  \"quality\": \"Quality\",\n  \"fullscreen\": \"Full Screen\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} of {2} items\",\n  \"empty\": \"No items to display\",\n  \"page\": \"Page\",\n  \"of\": \"of {0}\",\n  \"itemsPerPage\": \"items per page\",\n  \"first\": \"Go to the first page\",\n  \"previous\": \"Go to the previous page\",\n  \"next\": \"Go to the next page\",\n  \"last\": \"Go to the last page\",\n  \"refresh\": \"Refresh\",\n  \"morePages\": \"More pages\"\n});\n}\n\n/* TreeListPager messages */\n\nif (kendo.ui.TreeListPager) {\nkendo.ui.TreeListPager.prototype.options.messages =\n$.extend(true, kendo.ui.TreeListPager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} of {2} items\",\n  \"empty\": \"No items to display\",\n  \"page\": \"Page\",\n  \"of\": \"of {0}\",\n  \"itemsPerPage\": \"items per page\",\n  \"first\": \"Go to the first page\",\n  \"previous\": \"Go to the previous page\",\n  \"next\": \"Go to the next page\",\n  \"last\": \"Go to the last page\",\n  \"refresh\": \"Refresh\",\n  \"morePages\": \"More pages\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Drop Data Fields Here\",\n  \"columnFields\": \"Drop Column Fields Here\",\n  \"rowFields\": \"Drop Rows Fields Here\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Show items with value that:\",\n  \"filterFields\": \"Fields Filter\",\n  \"filter\": \"Filter\",\n  \"include\": \"Include Fields...\",\n  \"title\": \"Fields to include\",\n  \"clear\": \"Clear\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Cancel\",\n  \"operators\": {\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"startswith\": \"Starts with\",\n    \"endswith\": \"Ends with\",\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Never\",\n    \"hourly\": \"Hourly\",\n    \"daily\": \"Daily\",\n    \"weekly\": \"Weekly\",\n    \"monthly\": \"Monthly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"interval\": \" hour(s)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"interval\": \" day(s)\"\n  },\n  \"weekly\": {\n    \"interval\": \" week(s)\",\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \",\n    \"interval\": \" month(s)\",\n    \"day\": \"Day \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \",\n    \"interval\": \" year(s)\",\n    \"of\": \" of \"\n  },\n  \"end\": {\n    \"label\": \"End:\",\n    \"mobileLabel\": \"Ends\",\n    \"never\": \"Never\",\n    \"after\": \"After \",\n    \"occurrence\": \" occurrence(s)\",\n    \"on\": \"On \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"second\": \"second\",\n    \"third\": \"third\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"all day\",\n  \"date\": \"Date\",\n  \"event\": \"Event\",\n  \"time\": \"Time\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\",\n  \"today\": \"Today\",\n  \"save\": \"Save\",\n  \"cancel\": \"Cancel\",\n  \"destroy\": \"Delete\",\n  \"resetSeries\": \"Reset Series\",\n  \"deleteWindowTitle\": \"Delete event\",\n  \"ariaSlotLabel\": \"Selected from {0:t} to {1:t}\",\n  \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Are you sure you want to delete this event?\"\n  },\n  \"views\": {\n    \"day\": \"Day\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Month\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"resetSeriesWindowTitle\": \"Reset Series\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"deleteRecurringConfirmation\": \"Are you sure you want to delete this event occurrence?\",\n    \"deleteSeriesConfirmation\": \"Are you sure you want to delete the whole series?\",\n    \"editWindowTitle\": \"Edit Recurring Item\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\"\n  },\n  \"editor\": {\n    \"title\": \"Title\",\n    \"start\": \"Start\",\n    \"end\": \"End\",\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Description\",\n    \"repeat\": \"Repeat\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Start timezone\",\n    \"endTimezone\": \"End timezone\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"timezoneEditorButton\": \"Time zone\",\n    \"timezoneTitle\": \"Time zones\",\n    \"noTimezone\": \"No timezone\",\n    \"editorTitle\": \"Event\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"All borders\",\n  \"insideBorders\": \"Inside borders\",\n  \"insideHorizontalBorders\": \"Inside horizontal borders\",\n  \"insideVerticalBorders\": \"Inside vertical borders\",\n  \"outsideBorders\": \"Outside borders\",\n  \"leftBorder\": \"Left border\",\n  \"topBorder\": \"Top border\",\n  \"rightBorder\": \"Right border\",\n  \"bottomBorder\": \"Bottom border\",\n  \"noBorders\": \"No border\",\n  \"reset\": \"Reset color\",\n  \"customColor\": \"Custom color...\",\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Apply\",\n  \"save\": \"Save\",\n  \"cancel\": \"Cancel\",\n  \"remove\": \"Remove\",\n  \"retry\": \"Retry\",\n  \"revert\": \"Revert\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Format\",\n    \"categories\": {\n      \"number\": \"Number\",\n      \"currency\": \"Currency\",\n      \"date\": \"Date\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Font\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Font size\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Borders\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Alignment\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Align left\",\n     \"justifyCenter\": \"Center\",\n     \"justifyRight\": \"Align right\",\n     \"justifyFull\": \"Justify\",\n     \"alignTop\": \"Align top\",\n     \"alignMiddle\": \"Align middle\",\n     \"alignBottom\": \"Align bottom\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Merge cells\",\n    \"buttons\": {\n      \"mergeCells\": \"Merge all\",\n      \"mergeHorizontally\": \"Merge horizontally\",\n      \"mergeVertically\": \"Merge vertically\",\n      \"unmerge\": \"Unmerge\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Freeze panes\",\n    \"buttons\": {\n      \"freezePanes\": \"Freeze panes\",\n      \"freezeRows\": \"Freeze rows\",\n      \"freezeColumns\": \"Freeze columns\",\n      \"unfreeze\": \"Unfreeze panes\"\n    }\n  },\n  \"confirmationDialog\": {\n    \"text\": \"Are you sure you want to remove this sheet?\",\n    \"title\": \"Sheet remove\"\n  },\n  \"validationDialog\": {\n    \"title\": \"Data Validation\",\n    \"hintMessage\": \"Please enter a valid {0} value {1}.\",\n    \"hintTitle\": \"Validation {0}\",\n    \"criteria\": {\n      \"any\": \"Any value\",\n      \"number\": \"Number\",\n      \"text\": \"Text\",\n      \"date\": \"Date\",\n      \"custom\": \"Custom Formula\",\n      \"list\": \"List\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"greater than\",\n      \"lessThan\": \"less than\",\n      \"between\": \"between\",\n      \"notBetween\": \"not between\",\n      \"equalTo\": \"equal to\",\n      \"notEqualTo\": \"not equal to\",\n      \"greaterThanOrEqualTo\": \"greater than or equal to\",\n      \"lessThanOrEqualTo\": \"less than or equal to\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"greater than {0}\",\n      \"lessThan\": \"less than {0}\",\n      \"between\": \"between {0} and {1}\",\n      \"notBetween\": \"not between {0} and {1}\",\n      \"equalTo\": \"equal to {0}\",\n      \"notEqualTo\": \"not equal to {0}\",\n      \"greaterThanOrEqualTo\": \"greater than or equal to {0}\",\n      \"lessThanOrEqualTo\": \"less than or equal to {0}\",\n      \"custom\": \"that satisfies the formula: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Criteria\",\n      \"comparer\": \"Comparer\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Value\",\n      \"start\": \"Start\",\n      \"end\": \"End\",\n      \"onInvalidData\": \"On invalid data\",\n      \"rejectInput\": \"Reject input\",\n      \"showWarning\": \"Show warning\",\n      \"showHint\": \"Show hint\",\n      \"hintTitle\": \"Hint title\",\n      \"hintMessage\": \"Hint message\",\n      \"ignoreBlank\": \"Ignore blank\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Type title\",\n      \"typeMessage\": \"Type message\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Export...\",\n    \"labels\": {\n      \"fileName\": \"File name\",\n      \"saveAsType\": \"Save as type\",\n      \"exportArea\": \"Export\",\n      \"paperSize\": \"Paper size\",\n      \"margins\": \"Margins\",\n      \"orientation\": \"Orientation\",\n      \"print\": \"Print\",\n      \"guidelines\": \"Guidelines\",\n      \"center\": \"Center\",\n      \"horizontally\": \"Horizontally\",\n      \"vertically\": \"Vertically\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Cannot change part of a merged cell.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Copying and pasting\",\n    \"errorMessage\": \"These actions cannot be invoked through the menu. Please use the keyboard shortcuts instead:\",\n    \"labels\": {\n      \"forCopy\": \"for copy\",\n      \"forCut\": \"for cut\",\n      \"forPaste\": \"for paste\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"That action cannot be performed on multiple selection.\"\n  },\n  \"insertCommentDialog\": {\n    \"title\": \"Insert comment\",\n    \"labels\": {\n      \"comment\": \"Comment\",\n      \"removeComment\": \"Remove comment\"\n    }\n  },\n  \"insertImageDialog\": {\n    \"title\": \"Insert image\",\n    \"info\": \"Drag an image here, or click to select\",\n    \"typeError\": \"Please select a JPEG, PNG or GIF image\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Sort range A to Z\",\n  \"sortDescending\": \"Sort range Z to A\",\n  \"filterByValue\": \"Filter by value\",\n  \"filterByCondition\": \"Filter by condition\",\n  \"apply\": \"Apply\",\n  \"search\": \"Search\",\n  \"addToCurrent\": \"Add to current selection\",\n  \"clear\": \"Clear\",\n  \"blanks\": \"(Blanks)\",\n  \"operatorNone\": \"None\",\n  \"and\": \"AND\",\n  \"or\": \"OR\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Text contains\",\n      \"doesnotcontain\": \"Text does not contain\",\n      \"startswith\": \"Text starts with\",\n      \"endswith\": \"Text ends with\"\n    },\n    \"date\": {\n      \"eq\":  \"Date is\",\n      \"neq\": \"Date is not\",\n      \"lt\":  \"Date is before\",\n      \"gt\":  \"Date is after\"\n    },\n    \"number\": {\n      \"eq\": \"Is equal to\",\n      \"neq\": \"Is not equal to\",\n      \"gte\": \"Is greater than or equal to\",\n      \"gt\": \"Is greater than\",\n      \"lte\": \"Is less than or equal to\",\n      \"lt\": \"Is less than\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.colorPicker) {\nkendo.spreadsheet.messages.colorPicker =\n$.extend(true, kendo.spreadsheet.messages.colorPicker,{\n  \"reset\": \"Reset color\",\n  \"customColor\": \"Custom color...\",\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Add column left\",\n  \"addColumnRight\": \"Add column right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"alignment\": \"Alignment\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Align left\",\n    \"justifyCenter\": \"Center\",\n    \"justifyRight\": \"Align right\",\n    \"justifyFull\": \"Justify\",\n    \"alignTop\": \"Align top\",\n    \"alignMiddle\": \"Align middle\",\n    \"alignBottom\": \"Align bottom\"\n  },\n  \"backgroundColor\": \"Background\",\n  \"bold\": \"Bold\",\n  \"borders\": \"Borders\",\n  \"colorPicker\": {\n    \"reset\": \"Reset color\",\n    \"customColor\": \"Custom color...\"\n  },\n  \"copy\": \"Copy\",\n  \"cut\": \"Cut\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"excelImport\": \"Import from Excel...\",\n  \"filter\": \"Filter\",\n  \"fontFamily\": \"Font\",\n  \"fontSize\": \"Font size\",\n  \"format\": \"Custom format...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatic\",\n    \"number\": \"Number\",\n    \"percent\": \"Percent\",\n    \"financial\": \"Financial\",\n    \"currency\": \"Currency\",\n    \"date\": \"Date\",\n    \"time\": \"Time\",\n    \"dateTime\": \"Date time\",\n    \"duration\": \"Duration\",\n    \"moreFormats\": \"More formats...\"\n  },\n  \"formatDecreaseDecimal\": \"Decrease decimal\",\n  \"formatIncreaseDecimal\": \"Increase decimal\",\n  \"freeze\": \"Freeze panes\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Freeze panes\",\n    \"freezeRows\": \"Freeze rows\",\n    \"freezeColumns\": \"Freeze columns\",\n    \"unfreeze\": \"Unfreeze panes\"\n  },\n  \"insertComment\": \"Insert comment\",\n  \"insertImage\": \"Insert image\",\n  \"italic\": \"Italic\",\n  \"merge\": \"Merge cells\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Merge all\",\n    \"mergeHorizontally\": \"Merge horizontally\",\n    \"mergeVertically\": \"Merge vertically\",\n    \"unmerge\": \"Unmerge\"\n  },\n  \"open\": \"Open...\",\n  \"paste\": \"Paste\",\n  \"quickAccess\": {\n    \"redo\": \"Redo\",\n    \"undo\": \"Undo\"\n  },\n  \"saveAs\": \"Save As...\",\n  \"sortAsc\": \"Sort ascending\",\n  \"sortDesc\": \"Sort descending\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Sort sheet A to Z\",\n    \"sortSheetDesc\": \"Sort sheet Z to A\",\n    \"sortRangeAsc\": \"Sort range A to Z\",\n    \"sortRangeDesc\": \"Sort range Z to A\"\n  },\n  \"textColor\": \"Text Color\",\n  \"textWrap\": \"Wrap text\",\n  \"underline\": \"Underline\",\n  \"validation\": \"Data validation...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Cannot insert cells due to data loss possibility. Select another insert location or delete the data from the end of your worksheet.\",\n    \"filterRangeContainingMerges\": \"Cannot create a filter within a range containing merges\",\n    \"validationError\": \"The value that you entered violates the validation rules set on the cell.\"\n  },\n  \"tabs\": {\n    \"home\": \"Home\",\n    \"insert\": \"Insert\",\n    \"data\": \"Data\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Increase\",\n  \"decreaseButtonTitle\": \"Decrease\"\n});\n}\n\n/* ListBox messaages */\n\nif (kendo.ui.ListBox) {\nkendo.ui.ListBox.prototype.options.messages =\n$.extend(true, kendo.ui.ListBox.prototype.options.messages,{\n  \"tools\": {\n    \"remove\": \"Delete\",\n    \"moveUp\": \"Move Up\",\n    \"moveDown\": \"Move Down\",\n    \"transferTo\": \"Transfer To\",\n    \"transferFrom\": \"Transfer From\",\n    \"transferAllTo\": \"Transfer All To\",\n    \"transferAllFrom\": \"Transfer All From\"\n  }\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"No records to display\",\n  \"loading\": \"Loading...\",\n  \"requestFailed\": \"Request failed.\",\n  \"retry\": \"Retry\",\n  \"commands\": {\n      \"edit\": \"Edit\",\n      \"update\": \"Update\",\n      \"canceledit\": \"Cancel\",\n      \"create\": \"Add new record\",\n      \"createchild\": \"Add child record\",\n      \"destroy\": \"Delete\",\n      \"excel\": \"Export to Excel\",\n      \"pdf\": \"Export to PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Loading...\",\n  \"requestFailed\": \"Request failed.\",\n  \"retry\": \"Retry\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Select files...\",\n  \"cancel\": \"Cancel\",\n  \"retry\": \"Retry\",\n  \"remove\": \"Remove\",\n  \"clearSelectedFiles\": \"Clear\",\n  \"uploadSelectedFiles\": \"Upload files\",\n  \"dropFilesHere\": \"Drop files here to upload\",\n  \"statusUploading\": \"uploading\",\n  \"statusUploaded\": \"uploaded\",\n  \"statusWarning\": \"warning\",\n  \"statusFailed\": \"failed\",\n  \"headerStatusUploading\": \"Uploading...\",\n  \"headerStatusUploaded\": \"Done\",\n  \"invalidMaxFileSize\": \"File size too large.\",\n  \"invalidMinFileSize\": \"File size too small.\",\n  \"invalidFileExtension\": \"File type not allowed.\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} is required\",\n  \"pattern\": \"{0} is not valid\",\n  \"min\": \"{0} should be greater than or equal to {1}\",\n  \"max\": \"{0} should be smaller than or equal to {1}\",\n  \"step\": \"{0} is not valid\",\n  \"email\": \"{0} is not valid email\",\n  \"url\": \"{0} is not valid URL\",\n  \"date\": \"{0} is not valid date\",\n  \"dateCompare\": \"End date should be greater than or equal to the start date\"\n});\n}\n\n/* kendo.ui.progress method */\nif (kendo.ui.progress) {\nkendo.ui.progress.messages =\n$.extend(true, kendo.ui.progress.messages, {\n    loading: \"Loading...\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Close\"\n});\n}\n\n/* Calendar */\nif (kendo.ui.Calendar) {\nkendo.ui.Calendar.prototype.options.messages =\n$.extend(true, kendo.ui.Calendar.prototype.options.messages, {\n  \"weekColumnHeader\": \"\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancel\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancel\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\n  kendo.ui.DateInput.prototype.options.messages =\n    $.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n      \"year\": \"year\",\n      \"month\": \"month\",\n      \"day\": \"day\",\n      \"weekday\": \"day of the week\",\n      \"hour\": \"hours\",\n      \"minute\": \"minutes\",\n      \"second\": \"seconds\",\n      \"dayperiod\": \"AM/PM\"\n    });\n}\n\n/* List messages */\n\nif (kendo.ui.List) {\n    kendo.ui.List.prototype.options.messages =\n    $.extend(true, kendo.ui.List.prototype.options.messages,{\n      \"clear\": \"clear\",\n      \"noData\": \"No data found.\"\n    });\n}\n\n/* DropDownList messages */\n\nif (kendo.ui.DropDownList) {\n    kendo.ui.DropDownList.prototype.options.messages =\n    $.extend(true, kendo.ui.DropDownList.prototype.options.messages, kendo.ui.List.prototype.options.messages);\n}\n\n/* ComboBox messages */\n\nif (kendo.ui.ComboBox) {\n    kendo.ui.ComboBox.prototype.options.messages =\n    $.extend(true, kendo.ui.ComboBox.prototype.options.messages, kendo.ui.List.prototype.options.messages);\n}\n\n/* AutoComplete messages */\n\nif (kendo.ui.AutoComplete) {\n    kendo.ui.AutoComplete.prototype.options.messages =\n    $.extend(true, kendo.ui.AutoComplete.prototype.options.messages, kendo.ui.List.prototype.options.messages);\n}\n\n/* MultiColumnComboBox messages */\n\nif (kendo.ui.MultiColumnComboBox) {\n    kendo.ui.MultiColumnComboBox.prototype.options.messages =\n    $.extend(true, kendo.ui.MultiColumnComboBox.prototype.options.messages, kendo.ui.List.prototype.options.messages);\n}\n\n/* DropDownTree messages */\n\nif (kendo.ui.DropDownTree) {\n    kendo.ui.DropDownTree.prototype.options.messages =\n    $.extend(true, kendo.ui.DropDownTree.prototype.options.messages,{\n        \"singleTag\": \"item(s) selected\",\n        \"clear\": \"clear\",\n        \"deleteTag\": \"delete\",\n        \"noData\": \"No data found.\"\n    });\n}\n\n/* MultiSelect messages */\n\nif (kendo.ui.MultiSelect) {\n    kendo.ui.MultiSelect.prototype.options.messages =\n    $.extend(true, kendo.ui.MultiSelect.prototype.options.messages,{\n        \"singleTag\": \"item(s) selected\",\n        \"clear\": \"clear\",\n        \"deleteTag\": \"delete\",\n        \"noData\": \"No data found.\"\n    });\n}\n\n/* Chat messages */\n\nif (kendo.ui.Chat) {\n    kendo.ui.Chat.prototype.options.messages =\n    $.extend(true, kendo.ui.Chat.prototype.options.messages,{\n        \"placeholder\": \"Type a message...\",\n        \"toggleButton\": \"Toggle toolbar\",\n        \"sendButton\": \"Send message\"\n    });\n}\n\n})(window.kendo.jQuery);\n}));"]}