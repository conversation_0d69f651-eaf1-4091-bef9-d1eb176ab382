!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(r="undefined"!=typeof globalThis?globalThis:r||self).readXlsxFile=e()}(this,(function(){"use strict";var r={createDocument:function(r){return(new DOMParser).parseFromString(r.trim(),"text/xml")}},e=Uint8Array,t=Uint16Array,n=Uint32Array,o=new e([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new e([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new e([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),u=function(r,e){for(var o=new t(31),i=0;i<31;++i)o[i]=e+=1<<r[i-1];var a=new n(o[30]);for(i=1;i<30;++i)for(var u=o[i];u<o[i+1];++u)a[u]=u-o[i]<<5|i;return[o,a]},f=u(o,2),c=f[0],l=f[1];c[28]=258,l[258]=28;for(var s=u(i,0)[0],p=new t(32768),y=0;y<32768;++y){var b=(43690&y)>>>1|(21845&y)<<1;b=(61680&(b=(52428&b)>>>2|(13107&b)<<2))>>>4|(3855&b)<<4,p[y]=((65280&b)>>>8|(255&b)<<8)>>>1}var m=function(r,e,n){for(var o=r.length,i=0,a=new t(e);i<o;++i)r[i]&&++a[r[i]-1];var u,f=new t(e);for(i=0;i<e;++i)f[i]=f[i-1]+a[i-1]<<1;if(n){u=new t(1<<e);var c=15-e;for(i=0;i<o;++i)if(r[i])for(var l=i<<4|r[i],s=e-r[i],y=f[r[i]-1]++<<s,b=y|(1<<s)-1;y<=b;++y)u[p[y]>>>c]=l}else for(u=new t(o),i=0;i<o;++i)r[i]&&(u[i]=p[f[r[i]-1]++]>>>15-r[i]);return u},v=new e(288);for(y=0;y<144;++y)v[y]=8;for(y=144;y<256;++y)v[y]=9;for(y=256;y<280;++y)v[y]=7;for(y=280;y<288;++y)v[y]=8;var d=new e(32);for(y=0;y<32;++y)d[y]=5;var h=m(v,9,1),g=m(d,5,1),w=function(r){for(var e=r[0],t=1;t<r.length;++t)r[t]>e&&(e=r[t]);return e},O=function(r,e,t){var n=e/8|0;return(r[n]|r[n+1]<<8)>>(7&e)&t},S=function(r,e){var t=e/8|0;return(r[t]|r[t+1]<<8|r[t+2]<<16)>>(7&e)},j=function(r,o,i){(null==o||o<0)&&(o=0),(null==i||i>r.length)&&(i=r.length);var a=new(2==r.BYTES_PER_ELEMENT?t:4==r.BYTES_PER_ELEMENT?n:e)(i-o);return a.set(r.subarray(o,i)),a},A=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(r,e,t){var n=new Error(e||A[r]);if(n.code=r,Error.captureStackTrace&&Error.captureStackTrace(n,E),!t)throw n;return n},x=new e(0),P=function(r,e){return r[e]|r[e+1]<<8},I=function(r,e){return(r[e]|r[e+1]<<8|r[e+2]<<16|r[e+3]<<24)>>>0},k=function(r,e){return I(r,e)+4294967296*I(r,e+4)};function T(r,t){return function(r,t,n){var u=r.length;if(!u||n&&n.f&&!n.l)return t||new e(0);var f=!t||n,l=!n||n.i;n||(n={}),t||(t=new e(3*u));var p=function(r){var n=t.length;if(r>n){var o=new e(Math.max(2*n,r));o.set(t),t=o}},y=n.f||0,b=n.p||0,v=n.b||0,d=n.l,A=n.d,x=n.m,P=n.n,I=8*u;do{if(!d){y=O(r,b,1);var k=O(r,b+1,3);if(b+=3,!k){var T=r[(L=4+((b+7)/8|0))-4]|r[L-3]<<8,D=L+T;if(D>u){l&&E(0);break}f&&p(v+T),t.set(r.subarray(L,D),v),n.b=v+=T,n.p=b=8*D,n.f=y;continue}if(1==k)d=h,A=g,x=9,P=5;else if(2==k){var N=O(r,b,31)+257,C=O(r,b+10,15)+4,_=N+O(r,b+5,31)+1;b+=14;for(var F=new e(_),M=new e(19),U=0;U<C;++U)M[a[U]]=O(r,b+3*U,7);b+=3*C;var R=w(M),$=(1<<R)-1,B=m(M,R,1);for(U=0;U<_;){var L,V=B[O(r,b,$)];if(b+=15&V,(L=V>>>4)<16)F[U++]=L;else{var X=0,z=0;for(16==L?(z=3+O(r,b,3),b+=2,X=F[U-1]):17==L?(z=3+O(r,b,7),b+=3):18==L&&(z=11+O(r,b,127),b+=7);z--;)F[U++]=X}}var G=F.subarray(0,N),Y=F.subarray(N);x=w(G),P=w(Y),d=m(G,x,1),A=m(Y,P,1)}else E(1);if(b>I){l&&E(0);break}}f&&p(v+131072);for(var q=(1<<x)-1,W=(1<<P)-1,H=b;;H=b){var J=(X=d[S(r,b)&q])>>>4;if((b+=15&X)>I){l&&E(0);break}if(X||E(2),J<256)t[v++]=J;else{if(256==J){H=b,d=null;break}var K=J-254;if(J>264){var Q=o[U=J-257];K=O(r,b,(1<<Q)-1)+c[U],b+=Q}var Z=A[S(r,b)&W],rr=Z>>>4;if(Z||E(3),b+=15&Z,Y=s[rr],rr>3&&(Q=i[rr],Y+=S(r,b)&(1<<Q)-1,b+=Q),b>I){l&&E(0);break}f&&p(v+131072);for(var er=v+K;v<er;v+=4)t[v]=t[v-Y],t[v+1]=t[v+1-Y],t[v+2]=t[v+2-Y],t[v+3]=t[v+3-Y];v=er}}n.l=d,n.p=H,n.b=v,n.f=y,d&&(y=1,n.m=x,n.d=A,n.n=P)}while(!y);return v==t.length?t:j(t,0,v)}(r,t)}var D="undefined"!=typeof TextDecoder&&new TextDecoder;try{D.decode(x,{stream:!0}),1}catch(r){}function N(r,e){if(e){for(var t="",n=0;n<r.length;n+=16384)t+=String.fromCharCode.apply(null,r.subarray(n,n+16384));return t}if(D)return D.decode(r);var o=function(r){for(var e="",t=0;;){var n=r[t++],o=(n>127)+(n>223)+(n>239);if(t+o>r.length)return[e,j(r,t-1)];o?3==o?(n=((15&n)<<18|(63&r[t++])<<12|(63&r[t++])<<6|63&r[t++])-65536,e+=String.fromCharCode(55296|n>>10,56320|1023&n)):e+=1&o?String.fromCharCode((31&n)<<6|63&r[t++]):String.fromCharCode((15&n)<<12|(63&r[t++])<<6|63&r[t++]):e+=String.fromCharCode(n)}}(r),i=o[0];return o[1].length&&E(8),i}var C=function(r,e){return e+30+P(r,e+26)+P(r,e+28)},_=function(r,e,t){var n=P(r,e+28),o=N(r.subarray(e+46,e+46+n),!(2048&P(r,e+8))),i=e+46+n,a=I(r,e+20),u=t&&4294967295==a?F(r,i):[a,I(r,e+24),I(r,e+42)],f=u[0],c=u[1],l=u[2];return[P(r,e+10),f,c,o,i+P(r,e+30)+P(r,e+32),l]},F=function(r,e){for(;1!=P(r,e);e+=4+P(r,e+2));return[k(r,e+12),k(r,e+4),k(r,e+20)]};function M(r){return r instanceof File||r instanceof Blob?r.arrayBuffer().then(U):U(r)}function U(r){var t=function(r,t){for(var n={},o=r.length-22;101010256!=I(r,o);--o)(!o||r.length-o>65558)&&E(13);var i=P(r,o+8);if(!i)return{};var a=I(r,o+16),u=4294967295==a;u&&(o=I(r,o-12),101075792!=I(r,o)&&E(13),i=I(r,o+32),a=I(r,o+48));for(var f=t&&t.filter,c=0;c<i;++c){var l=_(r,a,u),s=l[0],p=l[1],y=l[2],b=l[3],m=l[4],v=l[5],d=C(r,v);a=m,f&&!f({name:b,size:p,originalSize:y,compression:s})||(s?8==s?n[b]=T(r.subarray(d,d+p),new e(y)):E(14,"unknown compression type "+s):n[b]=j(r,d,d+p))}return n}(new Uint8Array(r));return Promise.resolve(function(r){for(var e=[],t=0,n=Object.keys(r);t<n.length;t++){var o=n[t];e[o]=N(r[o])}return e}(t))}function R(r,e){for(var t=0;t<r.childNodes.length;){var n=r.childNodes[t];if(1===n.nodeType&&V(n)===e)return n;t++}}function $(r,e){for(var t=[],n=0;n<r.childNodes.length;){var o=r.childNodes[n];1===o.nodeType&&V(o)===e&&t.push(o),n++}return t}function B(r,e,t){for(var n=0;n<r.childNodes.length;){var o=r.childNodes[n];e?1===o.nodeType&&V(o)===e&&t(o,n):t(o,n),n++}}var L=/.+\:/;function V(r){return r.tagName.replace(L,"")}function X(r){if(1!==r.nodeType)return r.textContent;for(var e="<"+V(r),t=0;t<r.attributes.length;)e+=" "+r.attributes[t].name+'="'+r.attributes[t].value+'"',t++;e+=">";for(var n=0;n<r.childNodes.length;)e+=X(r.childNodes[n]),n++;return e+="</"+V(r)+">"}function z(r){var e,t,n=r.documentElement;return e=function(r){var e=R(r,"t");if(e)return e.textContent;var t="";return B(r,"r",(function(r){t+=R(r,"t").textContent})),t},t=[],B(n,"si",(function(r,n){t.push(e(r,n))})),t}function G(r,e){var t=e.createDocument(r),n={},o=R(t.documentElement,"workbookPr");o&&"1"===o.getAttribute("date1904")&&(n.epoch1904=!0),n.sheets=[];return function(r){return $(R(r.documentElement,"sheets"),"sheet")}(t).forEach((function(r){r.getAttribute("name")&&n.sheets.push({id:r.getAttribute("sheetId"),name:r.getAttribute("name"),relationId:r.getAttribute("r:id")})})),n}function Y(r,e){var t=e.createDocument(r),n={sheets:{},sharedStrings:void 0,styles:void 0};return function(r){return $(r.documentElement,"Relationship")}(t).forEach((function(r){var e=r.getAttribute("Target");switch(r.getAttribute("Type")){case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles":n.styles=q(e);break;case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings":n.sharedStrings=q(e);break;case"http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet":n.sheets[r.getAttribute("Id")]=q(e)}})),n}function q(r){return"/"===r[0]?r.slice("/".length):"xl/"+r}function W(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,n)}return t}function H(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?W(Object(t),!0).forEach((function(e){J(r,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):W(Object(t)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}))}return r}function J(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function K(r,e){if(!r)return{};var t,n,o=e.createDocument(r),i=(t=o,n=R(t.documentElement,"cellStyleXfs"),n?$(n,"xf"):[]).map(Z),a=function(r){var e=R(r.documentElement,"numFmts");return e?$(e,"numFmt"):[]}(o).map(Q).reduce((function(r,e){return r[e.id]=e,r}),[]);return function(r){var e=R(r.documentElement,"cellXfs");return e?$(e,"xf"):[]}(o).map((function(r){return r.hasAttribute("xfId")?H(H({},i[r.xfId]),Z(r,a)):Z(r,a)}))}function Q(r){return{id:r.getAttribute("numFmtId"),template:r.getAttribute("formatCode")}}function Z(r,e){var t={};if(r.hasAttribute("numFmtId")){var n=r.getAttribute("numFmtId");e[n]?t.numberFormat=e[n]:t.numberFormat={id:n}}return t}function rr(r,e){return r?z(e.createDocument(r)):[]}function er(r,e){e&&e.epoch1904&&(r+=1462);return new Date(Math.round(24*(r-25569)*36e5))}function tr(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=function(r,e){if(!r)return;if("string"==typeof r)return nr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);"Object"===t&&r.constructor&&(t=r.constructor.name);if("Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return nr(r,e)}(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function nr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function or(r,e,t,n){if(e){var o=t[e];if(!o)throw new Error("Cell style not found: ".concat(e));if(!o.numberFormat)return!1;if(ir.indexOf(Number(o.numberFormat.id))>=0||n.dateFormat&&o.numberFormat.template===n.dateFormat||!1!==n.smartDateParser&&o.numberFormat.template&&function(r){for(var e,t=tr((r=(r=(r=r.toLowerCase()).replace(ar,"")).replace(ur,"")).split(/\W+/));!(e=t()).done;){var n=e.value;if(fr.indexOf(n)<0)return!1}return!0}(o.numberFormat.template))return!0}}var ir=[14,15,16,17,18,19,20,21,22,27,30,36,45,46,47,50,57],ar=/^\[\$-414\]/,ur=/;@$/;var fr=["ss","mm","h","hh","am","pm","d","dd","m","mm","mmm","mmmm","yy","yyyy","e"];function cr(r,e,t){var n=t.getInlineStringValue,o=t.getInlineStringXml,i=t.getStyleId,a=t.styles,u=t.values,f=t.properties,c=t.options;switch(e||(e="n"),e){case"str":r=lr(r,c);break;case"inlineStr":if(void 0===(r=n()))throw new Error('Unsupported "inline string" cell value structure: '.concat(o()));r=lr(r,c);break;case"s":var l=Number(r);if(isNaN(l))throw new Error('Invalid "shared" string index: '.concat(r));if(l>=u.length)throw new Error('An out-of-bounds "shared" string index: '.concat(r));r=lr(r=u[l],c);break;case"b":if("1"===r)r=!0;else{if("0"!==r)throw new Error('Unsupported "boolean" cell value: '.concat(r));r=!1}break;case"z":r=void 0;break;case"e":r=function(r){switch(r){case 0:return"#NULL!";case 7:return"#DIV/0!";case 15:return"#VALUE!";case 23:return"#REF!";case 29:return"#NAME?";case 36:return"#NUM!";case 42:return"#N/A";case 43:return"#GETTING_DATA";default:return"#ERROR_".concat(r)}}(r);break;case"d":if(void 0===r)break;var s=new Date(r);if(isNaN(s.valueOf()))throw new Error('Unsupported "date" cell value: '.concat(r));r=s;break;case"n":if(void 0===r)break;var p=Number(r);if(isNaN(p))throw new Error('Invalid "numeric" cell value: '.concat(r));r=p,or(0,i(),a,c)&&(r=er(r,f));break;default:throw new TypeError("Cell type not supported: ".concat(e))}return void 0===r&&(r=null),r}function lr(r,e){return!1!==e.trim&&(r=r.trim()),""===r&&(r=void 0),r}var sr=["","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];function pr(r){for(var e=0,t=0;t<r.length;)e*=26,e+=sr.indexOf(r[t]),t++;return e}function yr(r){return r=r.split(/(\d+)/),[parseInt(r[1]),pr(r[0].trim())]}function br(r,e,t,n,o,i,a){var u,f=yr(r.getAttribute("r")),c=function(r,e){return R(e,"v")}(0,r),l=c&&c.textContent;return r.hasAttribute("t")&&(u=r.getAttribute("t")),{row:f[0],column:f[1],value:cr(l,u,{getInlineStringValue:function(){return function(r,e){if(e.firstChild&&"is"===V(e.firstChild)&&e.firstChild.firstChild&&"t"===V(e.firstChild.firstChild))return e.firstChild.firstChild.textContent}(0,r)},getInlineStringXml:function(){return X(r)},getStyleId:function(){return r.getAttribute("s")},styles:o,values:n,properties:i,options:a})}}function mr(r,e,t,n,o,i){var a=function(r){var e=R(r.documentElement,"sheetData"),t=[];return B(e,"row",(function(r){B(r,"c",(function(r){t.push(r)}))})),t}(r);return 0===a.length?[]:a.map((function(r){return br(r,0,0,t,n,o,i)}))}function vr(r,e){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null==t)return;var n,o,i=[],a=!0,u=!1;try{for(t=t.call(r);!(a=(n=t.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(r){u=!0,o=r}finally{try{a||null==t.return||t.return()}finally{if(u)throw o}}return i}(r,e)||function(r,e){if(!r)return;if("string"==typeof r)return dr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);"Object"===t&&r.constructor&&(t=r.constructor.name);if("Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return dr(r,e)}(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function hr(r){var e=function(r){var e=R(r.documentElement,"dimension");if(e)return e.getAttribute("ref")}(r);if(e)return 1===(e=e.split(":").map(yr).map((function(r){var e=vr(r,2);return{row:e[0],column:e[1]}}))).length&&(e=[e[0],e[0]]),e}function gr(r,e,t,n,o,i){var a=e.createDocument(r),u=mr(a,0,t,n,o,i),f=hr(a)||function(r){var e=function(r,e){return r-e},t=r.map((function(r){return r.row})).sort(e),n=r.map((function(r){return r.column})).sort(e),o=t[0],i=t[t.length-1];return[{row:o,column:n[0]},{row:i,column:n[n.length-1]}]}(u);return{cells:u,dimensions:f}}function wr(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=function(r,e){if(!r)return;if("string"==typeof r)return Or(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);"Object"===t&&r.constructor&&(t=r.constructor.name);if("Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Or(r,e)}(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Or(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Sr(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=function(r,e){if(!r)return;if("string"==typeof r)return jr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);"Object"===t&&r.constructor&&(t=r.constructor.name);if("Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return jr(r,e)}(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function jr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Ar(r,e){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null==t)return;var n,o,i=[],a=!0,u=!1;try{for(t=t.call(r);!(a=(n=t.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(r){u=!0,o=r}finally{try{a||null==t.return||t.return()}finally{if(u)throw o}}return i}(r,e)||Er(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Er(r,e){if(r){if("string"==typeof r)return xr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?xr(r,e):void 0}}function xr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Pr(r,e){var t=r.dimensions,n=r.cells;if(0===n.length)return[];var o=Ar(t,2);o[0];for(var i=o[1],a=i.column,u=i.row,f=new Array(u),c=0;c<u;){f[c]=new Array(a);for(var l=0;l<a;)f[c][l]=null,l++;c++}for(var s,p=function(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=Er(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(n);!(s=p()).done;){var y=s.value,b=y.row-1,m=y.column-1;m<a&&b<u&&(f[b][m]=y.value)}var v=e.rowMap;if(v)for(var d=0;d<f.length;)v[d]=d,d++;return f=function(r){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.rowMap,n=e.accessor,o=void 0===n?function(r){return r}:n,i=e.onlyTrimAtTheEnd,a=r.length-1;a>=0;){for(var u,f=!0,c=wr(r[a]);!(u=c()).done;){if(null!==o(u.value)){f=!1;break}}if(f)r.splice(a,1),t&&t.splice(a,1);else if(i)break;a--}return r}(function(r){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=e.accessor,n=void 0===t?function(r){return r}:t,o=e.onlyTrimAtTheEnd,i=r[0].length-1;i>=0;){for(var a,u=!0,f=Sr(r);!(a=f()).done;){if(null!==n(a.value[i])){u=!1;break}}if(u)for(var c=0;c<r.length;)r[c].splice(i,1),c++;else if(o)break;i--}return r}(f,{onlyTrimAtTheEnd:!0}),{onlyTrimAtTheEnd:!0,rowMap:v}),e.transformData&&(f=e.transformData(f)),f}function Ir(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=function(r,e){if(!r)return;if("string"==typeof r)return kr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);"Object"===t&&r.constructor&&(t=r.constructor.name);if("Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return kr(r,e)}(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function kr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Tr(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,n)}return t}function Dr(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?Tr(Object(t),!0).forEach((function(e){Nr(r,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):Tr(Object(t)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}))}return r}function Nr(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Cr(r,e){if("number"==typeof r){var t=e[r-1];return t&&t.relationId}for(var n,o=Ir(e);!(n=o()).done;){var i=n.value;if(i.name===r)return i.relationId}}function _r(r,e){var t=e&&e.map((function(r,e){return'"'.concat(r.name,'" (#').concat(e+1,")")})).join(", ");return new Error("Sheet ".concat("number"==typeof r?"#"+r:'"'+r+'"'," not found in the *.xlsx file.").concat(e?" Available sheets: "+t+".":""))}function Fr(r){return(Fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Mr(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}function Ur(r,e){if(e&&("object"===Fr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(r){if(void 0===r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(r)}function Rr(r){var e="function"==typeof Map?new Map:void 0;return(Rr=function(r){if(null===r||(t=r,-1===Function.toString.call(t).indexOf("[native code]")))return r;var t;if("function"!=typeof r)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(r))return e.get(r);e.set(r,n)}function n(){return $r(r,arguments,Vr(this).constructor)}return n.prototype=Object.create(r.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),Lr(n,r)})(r)}function $r(r,e,t){return($r=Br()?Reflect.construct:function(r,e,t){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(r,n));return t&&Lr(o,t.prototype),o}).apply(null,arguments)}function Br(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(r){return!1}}function Lr(r,e){return(Lr=Object.setPrototypeOf||function(r,e){return r.__proto__=e,r})(r,e)}function Vr(r){return(Vr=Object.setPrototypeOf?Object.getPrototypeOf:function(r){return r.__proto__||Object.getPrototypeOf(r)})(r)}var Xr=function(r){!function(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&Lr(r,e)}(u,r);var e,t,n,o,i,a=(e=u,t=Br(),function(){var r,n=Vr(e);if(t){var o=Vr(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return Ur(this,r)});function u(r){var e;return function(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),(e=a.call(this,"invalid")).reason=r,e}return n=u,o&&Mr(n.prototype,o),i&&Mr(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n}(Rr(Error));function zr(r){if("string"==typeof r){var e=r;if(r=Number(r),String(r)!==e)throw new Xr("not_a_number")}if("number"!=typeof r)throw new Xr("not_a_number");if(isNaN(r))throw new Xr("invalid_number");if(!isFinite(r))throw new Xr("out_of_bounds");return r}function Gr(r){if("string"==typeof r)return r;if("number"==typeof r){if(isNaN(r))throw new Xr("invalid_number");if(!isFinite(r))throw new Xr("out_of_bounds");return String(r)}throw new Xr("not_a_string")}function Yr(r){if("boolean"==typeof r)return r;throw new Xr("not_a_boolean")}function qr(r,e){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null==t)return;var n,o,i=[],a=!0,u=!1;try{for(t=t.call(r);!(a=(n=t.next()).done)&&(i.push(n.value),!e||i.length!==e);a=!0);}catch(r){u=!0,o=r}finally{try{a||null==t.return||t.return()}finally{if(u)throw o}}return i}(r,e)||Hr(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wr(r){return(Wr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function Hr(r,e){if(r){if("string"==typeof r)return Jr(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Jr(r,e):void 0}}function Jr(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function Kr(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,n)}return t}function Qr(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?Kr(Object(t),!0).forEach((function(e){Zr(r,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):Kr(Object(t)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}))}return r}function Zr(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}var re={isColumnOriented:!1};function ee(r,e,t){var n=t=t?Qr(Qr({},re),t):re,o=n.isColumnOriented,i=n.rowMap,a=n.ignoreEmptyRows;!function(r){for(var e=0,t=Object.keys(r);e<t.length;e++){var n=t[e];if(!r[n].prop)throw new Error('"prop" not defined for schema entry "'.concat(n,'".'))}}(e),o&&(r=ae(r));for(var u=r[0],f=[],c=[],l=1;l<r.length;l++){var s=te(e,r[l],l,u,c,t);null===s&&!1!==a||f.push(s)}if(i)for(var p,y=function(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(t)return(t=t.call(r)).next.bind(t);if(Array.isArray(r)||(t=Hr(r))||e&&r&&"number"==typeof r.length){t&&(r=t);var n=0;return function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(c);!(p=y()).done;){var b=p.value;b.row=i[b.row-1]+1}return{rows:f,errors:c}}function te(r,e,t,n,o,i){for(var a={},u=!0,f=function(){var f=l[c],s=r[f],p="object"===Wr(s.type)&&!Array.isArray(s.type),y=e[n.indexOf(f)];void 0===y&&(y=null);var b=void 0,m=void 0,v=void 0;if(p)b=te(s.type,e,t,n,o,i);else if(null===y)b=null;else if(Array.isArray(s.type)){var d=!1,h=function(r){var e=[],t=0;for(;t<r.length;){var n=qr(ie(r,",",t),2),o=n[0];t+=n[1]+",".length,e.push(o.trim())}return e}(y).map((function(r){var e=ne(r,s,i);return e.error&&(b=r,m=e.error,v=e.reason),null!==e.value&&(d=!0),e.value}));m||(b=d?h:null)}else{var g=ne(y,s,i);m=g.error,v=g.reason,b=m?y:g.value}!m&&null===b&&s.required&&(m="required"),m?(m={error:m,row:t+1,column:f,value:b},v&&(m.reason=v),s.type&&(m.type=s.type),o.push(m)):(u&&null!==b&&(u=!1),(null!==b||i.includeNullValues)&&(a[s.prop]=b))},c=0,l=Object.keys(r);c<l.length;c++)f();return u?null:a}function ne(r,e,t){if(null===r)return{value:null};var n;if((n=e.parse?oe(r,e.parse):e.type?function(r,e,t){switch(e){case String:return oe(r,Gr);case Number:return oe(r,zr);case Date:return oe(r,(function(r){return function(r,e){var t=e.properties;if(r instanceof Date){if(isNaN(r.valueOf()))throw new Xr("out_of_bounds");return r}if("number"==typeof r){if(isNaN(r))throw new Xr("invalid_number");if(!isFinite(r))throw new Xr("out_of_bounds");var n=er(r,t);if(isNaN(n.valueOf()))throw new Xr("out_of_bounds");return n}throw new Xr("not_a_date")}(r,{properties:t.properties})}));case Boolean:return oe(r,Yr);default:if("function"==typeof e)return oe(r,e);throw new Error("Unsupported schema type: ".concat(e&&e.name||e))}}(r,Array.isArray(e.type)?e.type[0]:e.type,t):{value:r}).error)return n;if(null!==n.value){if(e.oneOf&&e.oneOf.indexOf(n.value)<0)return{error:"invalid",reason:"unknown"};if(e.validate)try{e.validate(n.value)}catch(r){return{error:r.message}}}return n}function oe(r,e){try{return void 0===(r=e(r))?{value:null}:{value:r}}catch(r){var t={error:r.message};return r.reason&&(t.reason=r.reason),t}}function ie(r,e,t){for(var n=0,o="";t+n<r.length;){var i=r[t+n];if(i===e)return[o,n];if('"'===i){var a=ie(r,'"',t+n+1);o+=a[0],n+='"'.length+a[1]+'"'.length}else o+=i,n++}return[o,n]}var ae=function(r){return r[0].map((function(e,t){return r.map((function(r){return r[t]}))}))};function ue(r){return(ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function fe(r){for(var e={},t=0,n=Object.keys(r);t<n.length;t++){var o=n[t],i=r[o],a=void 0;"object"===ue(i)&&(i=Object.keys(r[o])[0],a=fe(r[o][i])),e[o]={prop:i},a&&(e[o].type=a)}return e}var ce=["schema","map"];function le(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),t.push.apply(t,n)}return t}function se(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?le(Object(t),!0).forEach((function(e){pe(r,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):le(Object(t)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))}))}return r}function pe(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function ye(r,e){if(null==r)return{};var t,n,o=function(r,e){if(null==r)return{};var t,n,o={},i=Object.keys(r);for(n=0;n<i.length;n++)t=i[n],e.indexOf(t)>=0||(o[t]=r[t]);return o}(r,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(n=0;n<i.length;n++)t=i[n],e.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(r,t)&&(o[t]=r[t])}return o}function be(r,e,t){var n=t.schema,o=t.map,i=ye(t,ce);!n&&o&&(n=fe(o));var a=function(r,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t.sheet||(t=Dr({sheet:1},t));var n=function(e){if(!r[e])throw new Error('"'.concat(e,'" file not found inside the *.xlsx file zip archive'));return r[e]},o=Y(n("xl/_rels/workbook.xml.rels"),e),i=o.sharedStrings?rr(n(o.sharedStrings),e):[],a=o.styles?K(n(o.styles),e):{},u=G(n("xl/workbook.xml"),e);if(t.getSheets)return u.sheets.map((function(r){return{name:r.name}}));var f=Cr(t.sheet,u.sheets);if(!f||!o.sheets[f])throw _r(t.sheet,u.sheets);var c=Pr(gr(n(o.sheets[f]),e,i,a,u,t),t);return t.properties?{data:c,properties:u}:c}(r,e,se(se({},i),{},{properties:n||i.properties}));return n?ee(a.data,n,se(se({},i),{},{properties:a.properties})):a}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return M(e).then((function(e){return be(e,r,t)}))}}));
//# sourceMappingURL=read-excel-file.min.js.map
