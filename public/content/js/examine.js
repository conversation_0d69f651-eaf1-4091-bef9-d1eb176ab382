let dataExamine = {
    tab: 1,
    examine: {},
    consultation: {},
    page: 'create',
    id_examine: '',
    id_consultation: '',
    nutritionAdviceList: [],
    activeModeOfLivingList: [],
    medicineList: [],
    prescription: [],
    medicalTest: [],
    id_prescription: 1,
    // listMenu: [{id:1, name:"Thực đơn 1","detail":[],note: ''},{id:2, name:"Thực đơn 2","detail":[],note: ''},{id:3, name:"Thực đơn 3","detail":[], note: ''}],
    foodNameListSearch: [],
    phoneListSearch: [],
    listMenuTime: [],
    menuExamine: [],
    menuExample: [],
    isGetListMedicalTest: 0,
    isDetail: false,
    isChangeInputAge: false
};

// Biến lưu cấu hình hiển thị cột hiện tại
let currentDisplayConfig = {
    visible_columns: ['weight', 'energy', 'protein', 'fat', 'carbohydrate'],
    column_order: ['weight', 'energy', 'protein', 'fat', 'carbohydrate']
};

const availableColumns = {
    // Thông tin cơ bản
    'ten': { label: 'Tên tiếng Việt', group: 'basic', default: false },
    'code': { label: 'Mã thực phẩm', group: 'basic', default: false },
    'weight': { label: 'Khối lượng (g)', group: 'basic', default: true },
    'edible': { label: 'Phần ăn được (%)', group: 'basic', default: false },

    // Chất dinh dưỡng chính (đã gộp vào food_info)
    'energy': { label: 'Năng lượng (kcal)', group: 'main_nutrients', default: true },
    'water': { label: 'Nước (g)', group: 'main_nutrients', default: false },
    'protein': { label: 'Protein (g)', group: 'main_nutrients', default: true },
    'fat': { label: 'Chất béo (g)', group: 'main_nutrients', default: true },
    'carbohydrate': { label: 'Carbohydrate (g)', group: 'main_nutrients', default: true },
    'fiber': { label: 'Chất xơ (g)', group: 'main_nutrients', default: false },
    'ash': { label: 'Tro (g)', group: 'main_nutrients', default: false },

    // Khoáng chất
    'calci': { label: 'Canxi (mg)', group: 'minerals', default: false },
    'phosphorous': { label: 'Phospho (mg)', group: 'minerals', default: false },
    'fe': { label: 'Sắt (mg)', group: 'minerals', default: false },
    'zinc': { label: 'Kẽm (mg)', group: 'minerals', default: false },
    'sodium': { label: 'Natri (mg)', group: 'minerals', default: false },
    'potassium': { label: 'Kali (mg)', group: 'minerals', default: false },
    'magnesium': { label: 'Magie (mg)', group: 'minerals', default: false },
    'manganese': { label: 'Mangan (mg)', group: 'minerals', default: false },
    'copper': { label: 'Đồng (mg)', group: 'minerals', default: false },
    'selenium': { label: 'Selen (μg)', group: 'minerals', default: false },
    
    // Axit béo
    'total_saturated_fat': { label: 'Axit béo bão hòa (g)', group: 'fatty_acids', default: false },
    'mufa': { label: 'Axit béo không bão hòa đơn (g)', group: 'fatty_acids', default: false },
    'fufa': { label: 'Axit béo không bão hòa đa (g)', group: 'fatty_acids', default: false },
    'pufa': { label: 'PUFA - Axit béo không bão hòa đa (g)', group: 'fatty_acids', default: false },
    'oleic': { label: 'Oleic (g)', group: 'fatty_acids', default: false },
    'linoleic': { label: 'Linoleic (g)', group: 'fatty_acids', default: false },
    'linolenic': { label: 'Linolenic (g)', group: 'fatty_acids', default: false },
    'arachidonic': { label: 'Arachidonic (g)', group: 'fatty_acids', default: false },
    'trans_fatty_acids': { label: 'Trans fat (g)', group: 'fatty_acids', default: false },
    'epa': { label: 'EPA (g)', group: 'fatty_acids', default: false },
    'dha': { label: 'DHA (g)', group: 'fatty_acids', default: false },
    'cholesterol': { label: 'Cholesterol (mg)', group: 'fatty_acids', default: false },
    
    // Protein & Amino acid (food_info)
    'animal_protein': { label: 'Protein động vật (g)', group: 'amino_acids', default: false },
    'lysin': { label: 'Lysin (mg)', group: 'amino_acids', default: false },
    'methionin': { label: 'Methionin (mg)', group: 'amino_acids', default: false },
    'tryptophan': { label: 'Tryptophan (mg)', group: 'amino_acids', default: false },
    'phenylalanin': { label: 'Phenylalanin (mg)', group: 'amino_acids', default: false },
    'threonin': { label: 'Threonin (mg)', group: 'amino_acids', default: false },
    'isoleucine': { label: 'Isoleucine (mg)', group: 'amino_acids', default: false },
    'leucine': { label: 'Leucine (mg)', group: 'amino_acids', default: false },
    'valine': { label: 'Valine (mg)', group: 'amino_acids', default: false },
    'arginine': { label: 'Arginine (mg)', group: 'amino_acids', default: false },
    'histidine': { label: 'Histidine (mg)', group: 'amino_acids', default: false },
    'alanine': { label: 'Alanine (mg)', group: 'amino_acids', default: false },
    'aspartic_acid': { label: 'Axit aspartic (mg)', group: 'amino_acids', default: false },
    'glutamic_acid': { label: 'Axit glutamic (mg)', group: 'amino_acids', default: false },
    'glycine': { label: 'Glycine (mg)', group: 'amino_acids', default: false },
    'proline': { label: 'Proline (mg)', group: 'amino_acids', default: false },
    'serine': { label: 'Serine (mg)', group: 'amino_acids', default: false },
    'tyrosine': { label: 'Tyrosine (mg)', group: 'amino_acids', default: false },
    'cystine': { label: 'Cystine (mg)', group: 'amino_acids', default: false },
    
    // Vitamin
    'vitamin_a_rae': { label: 'Vitamin A (μg RAE)', group: 'vitamins', default: false },
    'vitamin_b6': { label: 'Vitamin B6 (mg)', group: 'vitamins', default: false },
    'vitamin_b12': { label: 'Vitamin B12 (μg)', group: 'vitamins', default: false },
    'vitamin_c': { label: 'Vitamin C (mg)', group: 'vitamins', default: false },
    'vitamin_e': { label: 'Vitamin E (mg)', group: 'vitamins', default: false },
    'vitamin_k': { label: 'Vitamin K (μg)', group: 'vitamins', default: false },
    'vitamin_d': { label: 'Vitamin D (μg)', group: 'vitamins', default: false },
    'niacin': { label: 'Niacin (mg)', group: 'vitamins', default: false },
    'pantothenic_acid': { label: 'Axit pantothenic (mg)', group: 'vitamins', default: false },
    'biotin': { label: 'Biotin (μg)', group: 'vitamins', default: false },
    'b_carotene': { label: 'Beta-carotene (μg)', group: 'vitamins', default: false },
    'a_carotene': { label: 'Alpha-carotene (μg)', group: 'vitamins', default: false },
    'b_cryptoxanthin': { label: 'Beta-cryptoxanthin (μg)', group: 'vitamins', default: false },
    
    // Đường
    'total_sugar': { label: 'Tổng đường (g)', group: 'sugars', default: false },
    'glucose': { label: 'Glucose (g)', group: 'sugars', default: false },
    'fructose': { label: 'Fructose (g)', group: 'sugars', default: false },
    'sucrose': { label: 'Sucrose (g)', group: 'sugars', default: false },
    'lactose': { label: 'Lactose (g)', group: 'sugars', default: false },
    'maltose': { label: 'Maltose (g)', group: 'sugars', default: false },
    'galactose': { label: 'Galactose (g)', group: 'sugars', default: false },

    // Vi lượng khác
    'fluoride': { label: 'Fluoride (mg)', group: 'minerals', default: false },
    'iodine': { label: 'Iod (μg)', group: 'minerals', default: false },
    
    // Carotenoid và chất chống oxy hóa
    'lycopene': { label: 'Lycopene (μg)', group: 'antioxidants', default: false },
    'lutein_zeaxanthin': { label: 'Lutein + Zeaxanthin (μg)', group: 'antioxidants', default: false },
    'caroten': { label: 'Carotenoid (μg)', group: 'antioxidants', default: false },
    
    // Isoflavone và phytoestrogen
    'total_isoflavone': { label: 'Tổng Isoflavone (mg)', group: 'phytonutrients', default: false },
    'daidzein': { label: 'Daidzein (mg)', group: 'phytonutrients', default: false },
    'genistein': { label: 'Genistein (mg)', group: 'phytonutrients', default: false },
    'glycetin': { label: 'Glycetin (mg)', group: 'phytonutrients', default: false },
    'phytosterol': { label: 'Phytosterol (mg)', group: 'phytonutrients', default: false },
    
    // Purine và chất chuyển hóa
    'purine': { label: 'Purine (mg)', group: 'metabolites', default: false },
    
    // Protein bổ sung
    'unanimal_protein': { label: 'Protein thực vật (g)', group: 'amino_acids', default: false },
    
    // Lipid bổ sung
    'animal_lipid': { label: 'Lipid động vật (g)', group: 'fatty_acids', default: false },
    'unanimal_lipid': { label: 'Lipid thực vật (g)', group: 'fatty_acids', default: false },
    
    // Axit béo bão hòa bổ sung
    'palmitic': { label: 'Axit Palmitic (g)', group: 'fatty_acids', default: false },
    'margaric': { label: 'Axit Margaric (g)', group: 'fatty_acids', default: false },
    'stearic': { label: 'Axit Stearic (g)', group: 'fatty_acids', default: false },
    'arachidic': { label: 'Axit Arachidic (g)', group: 'fatty_acids', default: false },
    'behenic': { label: 'Axit Behenic (g)', group: 'fatty_acids', default: false },
    'lignoceric': { label: 'Axit Lignoceric (g)', group: 'fatty_acids', default: false },
    
    // Axit béo không bão hòa đơn bổ sung
    'myristoleic': { label: 'Axit Myristoleic (g)', group: 'fatty_acids', default: false },
    'palmitoleic': { label: 'Axit Palmitoleic (g)', group: 'fatty_acids', default: false },
    
    // Vitamin bổ sung
    'riboflavin': { label: 'Riboflavin - B2 (mg)', group: 'vitamins', default: false },
    'thiamine': { label: 'Thiamine - B1 (mg)', group: 'vitamins', default: false },
    'folic_acid': { label: 'Axit Folic (μg)', group: 'vitamins', default: false }
};

const columnGroups = {
    'basic': 'Thông tin cơ bản',
    'main_nutrients': 'Chất dinh dưỡng chính',
    'minerals': 'Khoáng chất',
    'fatty_acids': 'Axit béo',
    'amino_acids': 'Protein & Amino acid',
    'vitamins': 'Vitamin',
    'sugars': 'Đường',
    'antioxidants': 'Chất chống oxy hóa',
    'phytonutrients': 'Phytonutrient',
    'metabolites': 'Chất chuyển hóa'
};

var analysisData = [];
let dataConsultation = {
    clinicalExam: [],
    nutritionTracking: [],
    subclinical: {
        data: [], // [{id, name, reference, values: [{dateId, value}]}]
        date: [] // [{id: 'date_1', date: '08-06-2025'}]
    },
    subclinicalOrther: [],
    medicine: {
        data: [], // [{id, name, values: [{dateId, value}]}]
        date: [] // [{id: 'date_1', date: '08-06-2025'}]
    },
    additional: [],
    additional_id_choose: 0
}

function generateDateId(type) {
    let id = 1;
    switch (type) {
        case 'subclinical':
            if(dataConsultation.subclinical.date.length > 0){
                id = dataConsultation.subclinical.date[dataConsultation.subclinical.date.length - 1].id + 1;
            }
            break ;
        case 'medicine':
            if(dataConsultation.medicine.date.length > 0){
                id = dataConsultation.medicine.date[dataConsultation.medicine.date.length - 1].id + 1;
            }
            break ;
    }
    return id
}

let dataFilter = {
    sort_id_count: '',
    sort_cus_phone: '',
    sort_time_examine: '',
    sort_time_consultation: '',
    page: 1,
    search_id_count: '',
    search_cus_name: '',
    search_cus_phone: '',
    search_cus_address: '',
    search_diagnostic: '',
    search_time_examine: '',
    search_time_consultation: '',
    status_examine_ids: []
};

const numberFormat = new Intl.NumberFormat();

toastr.options = {
    "closeButton": true,
    "debug": false,
    "newestOnTop": false,
    "progressBar": false,
    "positionClass": "toast-top-right",
    "preventDuplicates": false,
    "onclick": null,
    "showDuration": "300",
    "hideDuration": "1000",
    "timeOut": "20000",
    "extendedTimeOut": "20000",
    "showEasing": "swing",
    "hideEasing": "linear",
    "showMethod": "fadeIn",
    "hideMethod": "fadeOut"
}

function displayMessageToastr(message) {
    if (message != '') {
        toastr.success(message, 'Thông báo');
    }
}

function displayErrorToastr(message) {
    toastr.clear();
    if (message != '') {
        toastr.error(message, 'Thông báo');
    }
}

function displayMessage(message) {
    var confirmBox = `
    <div class="modal fade" id="modal_message_successful" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
      <div class="modal-content">
        <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="text-center mb-2">
          <svg class="iconsvg-confirm text-success fs-65px">
            <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
          </svg>
        </div>
        <h4 class="modal-title text-center mb-0">` + message + `</h4>
      </div>
    </div>
  </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_message_successful").modal('show');
}

function displayError(message) {
    var confirmBox = `
    <div class="modal fade" id="modal_message_error" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
      <div class="modal-content">
        <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="text-center mb-2">
          <svg class="iconsvg-alert-circle-lg text-danger fs-65px">
            <use xlink:href="/public/content/images/sprite.svg#alert-circle-lg"></use>
          </svg>
        </div>
        <h4 class="modal-title text-center mb-2">Lỗi</h4>
        <p class="mb-0 fw-5 text-body-2 text-center">` + message + `</p>
      </div>
    </div>
  </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_message_error").modal('show');
}

function ajax_call_error(jqXHR, exception) {
    var msg = '';
    if (jqXHR.status === 0) {
        msg = 'Mất kết nối mạng. Vui lòng kiểm tra kết nối và thử lại.';
    } else if (jqXHR.status == 404) {
        msg = 'Không tìm thấy trang được yêu cầu. [404]';
    } else if (jqXHR.status == 500) {
        msg = 'Lỗi máy chủ nội bộ [500].';
    } else if (exception === 'parsererror') {
        msg = 'Phân tích cú pháp JSON không thành công.';
    } else if (exception === 'timeout') {
        msg = 'Lỗi hết thời gian.';
    } else if (exception === 'abort') {
        msg = 'Yêu cầu Ajax đã bị hủy bỏ.';
    } else {
        msg = jqXHR.responseText;
    }
    displayError(msg);
}

function clearInput(id) {
    $(id).val('');
}

function returnList() {
    window.location.href = '/examine';
}

function returnList1() {
    window.location.href = '/consultation';
}

// Lưu phiếu tư vấn
function saveExamine(action, autoSave = 0, data = {}) {
    try {
        let status = parseInt($('#status_examine').val());
        if (status == 3 && data.hasOwnProperty('isExport') && data.isExport) {
            if (!dataExamine.id_examine && result.success && result.id_examine) {
                dataExamine.page = "edit";
                dataExamine.id_examine = result.id_examine;
            }
            let link = document.createElement('a');
            link.href = '/export/' + data.linkExport + ('?examine_id=' + dataExamine.id_examine) + (data.menu_id ? '&menu_id=' + data.menu_id : '');
            link.click();
            link.remove();
            return;
        }
        let loading = $("#loading-page");
        changeTabExamine(dataExamine.tab);
        if (autoSave == 0) {
            if (!dataExamine.examine.cus_name || !dataExamine.examine.cus_phone || !dataExamine.examine.cus_birthday || !dataExamine.examine.cus_gender) {
                if (!dataExamine.examine.cus_name) {
                    displayErrorToastr('Vui lòng nhập họ tên!');
                    return;
                }
                if (!dataExamine.examine.cus_phone) {
                    displayErrorToastr('Vui lòng nhập số điện thoại!');
                    return;
                }
                if (!dataExamine.examine.cus_birthday) {
                    displayErrorToastr('Vui lòng nhập ngày sinh!');
                    return;
                }
                if (!dataExamine.examine.cus_gender) {
                    displayErrorToastr('Vui lòng nhập giới tính!');
                    return;
                }
            }
            let birthday = moment(dataExamine.examine.cus_birthday, "DD-MM-YYYY");
            if (!birthday.isValid()) {
                displayErrorToastr('Ngày sinh sai định dạng ngày-tháng-năm!');
                return;
            }
        }
        // action 1 tiếp nhận 2 đang khám 3 hoàn thành 0 ko thay doi;
        if (action) dataExamine.examine['action'] = action;
        if (action == 0) dataExamine.examine['action'] = status;
        if (status == 2 && action == 1) dataExamine.examine['action'] = 2;
        if (status == 3) dataExamine.examine['action'] = 3;
        let url = '/examine/create';
        if (dataExamine.page == 'edit') url = '/examine/edit/' + dataExamine.id_examine;
        $.ajax({
            type: 'POST',
            url: url,
            data: dataExamine.examine,
            beforeSend: function () {
                if (autoSave == 0) {
                    loading.show();
                } else {
                    $('#loadingAutoSave').removeAttr('style');
                }
            },
            success: function (result) {
                if (autoSave == 0) {
                    loading.hide();
                    if (result.success) {
                        if (data.hasOwnProperty('isExport') && data.isExport) {
                            if (!dataExamine.id_examine && result.success && result.id_examine) {
                                dataExamine.page = "edit";
                                dataExamine.id_examine = result.id_examine;
                            }
                            let link = document.createElement('a');
                            link.href = '/export/' + data.linkExport + ('?examine_id=' + dataExamine.id_examine) + (data.menu_id ? '&menu_id=' + data.menu_id : '');
                            link.click();
                            link.remove();
                        } else {
                            displayMessage('Lưu thành công');
                            setTimeout(() => {
                                returnList();
                            }, 500);
                        }
                    } else {
                        displayError(result.message);
                    }
                } else {
                    if (result.success && result.id_examine && !dataExamine.id_examine) {
                        dataExamine.page = "edit";
                        dataExamine.id_examine = result.id_examine;
                    }
                    setTimeout(() => {
                        $('#loadingAutoSave').css('display', 'none');
                    }, 1000);
                }
            },
            error: function (jqXHR, exception) {
                if (autoSave == 0) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            }
        });
    } catch (error) {

    }
}

// Lưu phiếu hội chẩn
function saveConsultation(autoSave = 0, data = {}) {
    try {
        //Export
        if (data.hasOwnProperty('isExport') && data.isExport) {
            if (!dataExamine.id_consultation && result.success && result.id_consultation) {
                dataExamine.page = "edit";
                dataExamine.id_consultation = result.id_consultation;
            }
            let link = document.createElement('a');
            link.href = '/export/' + data.linkExport + ('?consultation_id=' + dataExamine.id_consultation);
            link.click();
            link.remove();
            return;
        }
        let loading = $("#loading-page");
        changeTabConsultation(dataExamine.tab);
        if (autoSave == 0) {
            if (!dataExamine.consultation.cus_name || !dataExamine.consultation.cus_phone || !dataExamine.consultation.cus_birthday || !dataExamine.consultation.cus_gender) {
                if (!dataExamine.consultation.cus_name) {
                    displayErrorToastr('Vui lòng nhập họ tên!');
                    return;
                }
                if (!dataExamine.consultation.cus_phone) {
                    displayErrorToastr('Vui lòng nhập số điện thoại!');
                    return;
                }
                if (!dataExamine.consultation.cus_birthday) {
                    displayErrorToastr('Vui lòng nhập ngày sinh!');
                    return;
                }
                if (!dataExamine.consultation.cus_gender) {
                    displayErrorToastr('Vui lòng nhập giới tính!');
                    return;
                }
            }
            let birthday = moment(dataExamine.consultation.cus_birthday, "DD-MM-YYYY");
            if (!birthday.isValid()) {
                displayErrorToastr('Ngày sinh sai định dạng ngày-tháng-năm!');
                return;
            }
        }
        let url = '/consultation/create';
        if (dataExamine.page == 'edit') url = '/consultation/edit/' + dataExamine.id_consultation;
        $.ajax({
            type: 'POST',
            url: url,
            data: dataExamine.consultation,
            beforeSend: function () {
                if (autoSave == 0) {
                    loading.show();
                } else {
                    $('#loadingAutoSave').removeAttr('style');
                }
            },
            success: function (result) {
                if (autoSave == 0) {
                    loading.hide();
                    if (result.success) {
                        if (data.hasOwnProperty('isExport') && data.isExport) {
                            if (!dataExamine.id_consultation && result.success && result.id_consultation) {
                                dataExamine.page = "edit";
                                dataExamine.id_consultation = result.id_consultation;
                            }
                            let link = document.createElement('a');
                            link.href = '/export/' + data.linkExport + ('?consultation_id=' + dataExamine.id_consultation);
                            link.click();
                            link.remove();
                        } else {
                            displayMessage('Lưu thành công');
                            setTimeout(() => {
                                returnList1();
                            }, 500);
                        }
                    } else {
                        displayError(result.message);
                    }
                } else {
                    if (result.success && result.id_consultation && !dataExamine.id_consultation) {
                        dataExamine.page = "edit";
                        dataExamine.id_consultation = result.id_consultation;
                    }
                    setTimeout(() => {
                        $('#loadingAutoSave').css('display', 'none');
                    }, 1000);
                }
            },
            error: function (jqXHR, exception) {
                if (autoSave == 0) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            }
        });
    } catch (error) {

    }
}

// Xuất thực đơn mẫu (giữ cấu trúc 3 cột như docs/menuExample.js)
async function exportMenuExample() {
    try {
        if (!dataExamine.menuExamine || dataExamine.menuExamine.length === 0) {
            toastr.error('Không có thực đơn nào để xuất!');
            return '';
        }
        if (!(typeof window !== 'undefined' && window.ExcelJS)) {
            toastr.error('Không tìm thấy ExcelJS trên trình duyệt!');
            return '';
        }

        const ExcelJS = window.ExcelJS;
        const wb = new ExcelJS.Workbook();

        dataExamine.menuExamine.forEach((menu, menuIndex) => {
            let sheetName = menu.name || `Thực đơn ${menuIndex + 1}`;
            sheetName = sheetName.substring(0, 31).replace(/[\\\/\?\*\[\]]/g, '');
            const ws = wb.addWorksheet(sheetName, {properties: {defaultRowHeight: 30}});

            // Tiêu đề
            ws.mergeCells(1, 1, 1, 3);
            const titleCell = ws.getCell(1, 1);
            titleCell.value = menu.name || 'THỰC ĐƠN';
            titleCell.alignment = {horizontal: 'center', vertical: 'middle'};
            titleCell.font = {bold: true, size: 18};

            // Header 3 cột
            ws.getRow(2).values = ['Giờ ăn', 'Thực phẩm', 'Khối lượng (g)'];
            ['A2', 'B2', 'C2'].forEach((addr) => {
                const cell = ws.getCell(addr);
                cell.font = {bold: true};
                cell.alignment = {horizontal: 'center', vertical: 'middle'};
            });

            // Dữ liệu chi tiết (courses)
            let rowIndex = 3;
            if (menu.detail && menu.detail.length > 0) {
                menu.detail.forEach(menuTime => {
                    // Migrate dữ liệu cũ
                    if (!Array.isArray(menuTime.courses) || menuTime.courses.length === 0) {
                        menuTime.courses = [{ id: 1, name: menuTime.name_course || '' }];
                        (menuTime.listFood || []).forEach(f => { if (f.course_id == null) f.course_id = 1; });
                    }

                    const validCourses = (menuTime.courses || []).filter(c => c && (c.id != null));
                    const listFood = menuTime.listFood || [];

                    const defaultCourse = { id: 0, name: 'Món ăn chung' };
                    const foodsByCourse = {};

                    listFood.forEach(food => {
                        let courseId = food.course_id;
                        if (courseId == null || !validCourses.some(c => c.id == courseId)) courseId = 0;
                        if (!foodsByCourse[courseId]) foodsByCourse[courseId] = [];
                        foodsByCourse[courseId].push(food);
                    });

                    let totalRows = 0;
                    Object.keys(foodsByCourse).forEach(courseId => {
                        const foods = foodsByCourse[courseId];
                        totalRows += 1 + foods.length; // 1 hàng tên món + n hàng thực phẩm
                    });

                    if (totalRows > 0) {
                        ws.mergeCells(rowIndex, 1, rowIndex + totalRows - 1, 1);
                        ws.getCell(rowIndex, 1).value = menuTime.name || '';
                        const aCell = ws.getCell(rowIndex, 1);
                        aCell.font = {bold: true};
                        aCell.alignment = {textRotation: 90, horizontal: 'center', vertical: 'middle'};
                    }

                    Object.keys(foodsByCourse).forEach(courseId => {
                        const course = courseId == 0 ? defaultCourse : validCourses.find(c => c.id == courseId);
                        const courseName = course ? course.name : 'Món ăn chung';
                        const foods = foodsByCourse[courseId];

                        ws.getCell(rowIndex, 2).value = courseName || '';
                        ws.getCell(rowIndex, 2).font = {bold: true};
                        ws.getCell(rowIndex, 2).alignment = {horizontal: 'left', vertical: 'middle'};
                        ws.getCell(rowIndex, 3).value = '';
                        rowIndex += 1;

                        foods.forEach(food => {
                            ws.getCell(rowIndex, 2).value = food.name || '';
                            ws.getCell(rowIndex, 3).value = food.weight || 0;
                            ws.getCell(rowIndex, 2).alignment = {horizontal: 'left', vertical: 'middle'};
                            ws.getCell(rowIndex, 3).alignment = {horizontal: 'center', vertical: 'middle'};
                            rowIndex += 1;
                        });
                    });
                });
            }

            // Chiều rộng cột
            ws.columns = [
                {width: 20},
                {width: 40},
                {width: 15}
            ];
        });

        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const fileName = `Thuc_Don_${timestamp}.xlsx`;
        const buffer = await wb.xlsx.writeBuffer();
        const blob = new Blob([buffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return 'success';
    } catch (error) {
        console.error('Error exporting menu to Excel:', error);
        toastr.error('Có lỗi xảy ra khi xuất file Excel: ' + error.message);
        return '';
    }
}

function changeTabExamine(tab) {
    switch (dataExamine.tab) {
        case 1:
            dataExamine.examine['cus_name'] = $('#cus_name').val();
            dataExamine.examine['cus_phone'] = $('#cus_phone').val();
            dataExamine.examine['cus_email'] = $('#cus_email').val();
            dataExamine.examine['cus_gender'] = $('#cus_gender').val();
            dataExamine.examine['cus_birthday'] = $('#cus_birthday').val();
            dataExamine.examine['cus_address'] = $('#cus_address').val();
            dataExamine.examine['cus_anamnesis'] = $('#cus_anamnesis').val();
            dataExamine.examine['cus_living_habits'] = $('#cus_living_habits').val();
            dataExamine.examine['diagnostic'] = $('#diagnostic').val();
            dataExamine.examine['cus_length'] = $('#cus_length').val();
            dataExamine.examine['cus_cctc'] = $('#cus_cctc').val(); // chiều cao tiêu chuẩn
            dataExamine.examine['cus_cntc'] = $('#cus_cntc').val(); // cân nặng tiêu chuẩn
            dataExamine.examine['cus_cnht'] = $('#cus_cnht').val(); // cân nặng hiện tại
            dataExamine.examine['cus_cnbt'] = $('#cus_cnbt').val(); // cân nặng thường có
            dataExamine.examine['cus_bmi'] = $('#cus_bmi').val();
            dataExamine.examine['cus_ncdd'] = $('#cus_ncdd').val();
            dataExamine.examine['cus_height_by_age'] = $('#cus_height_by_age').val();
            dataExamine.examine['cus_weight_by_age'] = $('#cus_weight_by_age').val();
            dataExamine.examine['cus_bmi_by_age'] = $('#cus_bmi_by_age').val();
            dataExamine.examine['cus_height_by_weight'] = $('#cus_height_by_weight').val();
            dataExamine.examine['clinical_examination'] = $('#clinical_examination').val();
            dataExamine.examine['erythrocytes'] = $('#erythrocytes').val();
            dataExamine.examine['cus_bc'] = $('#cus_bc').val();
            dataExamine.examine['cus_tc'] = $('#cus_tc').val();
            dataExamine.examine['cus_albumin'] = $('#cus_albumin').val();
            dataExamine.examine['cus_nakcl'] = $('#cus_nakcl').val();
            dataExamine.examine['cus_astaltggt'] = $('#cus_astaltggt').val();
            dataExamine.examine['cus_urecreatinin'] = $('#cus_urecreatinin').val();
            dataExamine.examine['cus_bilirubin'] = $('#cus_bilirubin').val();
            dataExamine.examine['exa_note'] = $('#exa_note').val();
            break;
        case 2:
            dataExamine.examine['cus_fat'] = $('#cus_fat').val();
            dataExamine.examine['cus_water'] = $('#cus_water').val();
            dataExamine.examine['cus_visceral_fat'] = $('#cus_visceral_fat').val();
            dataExamine.examine['cus_bone_weight'] = $('#cus_bone_weight').val();
            dataExamine.examine['cus_chcb'] = $('#cus_chcb').val();
            dataExamine.examine['cus_waist'] = $('#cus_waist').val();
            dataExamine.examine['cus_butt'] = $('#cus_butt').val();
            dataExamine.examine['cus_cseomong'] = $('#cus_cseomong').val();
            dataExamine.examine['active_mode_of_living'] = $('#active_mode_of_living').val();
            dataExamine.examine['active_mode_of_living_id'] = $('#active_mode_of_living_id').val();
            dataExamine.examine['glucid_should_use'] = $('#glucid_should_use').val();
            dataExamine.examine['glucid_limited_use'] = $('#glucid_limited_use').val();
            dataExamine.examine['glucid_should_not_use'] = $('#glucid_should_not_use').val();
            dataExamine.examine['protein_should_use'] = $('#protein_should_use').val();
            dataExamine.examine['protein_limited_use'] = $('#protein_limited_use').val();
            dataExamine.examine['protein_should_not_use'] = $('#protein_should_not_use').val();
            dataExamine.examine['lipid_should_use'] = $('#lipid_should_use').val();
            dataExamine.examine['lipid_limited_use'] = $('#lipid_limited_use').val();
            dataExamine.examine['lipid_should_not_use'] = $('#lipid_should_not_use').val();
            dataExamine.examine['vitamin_ck_should_use'] = $('#vitamin_ck_should_use').val();
            dataExamine.examine['vitamin_ck_limited_use'] = $('#vitamin_ck_limited_use').val();
            dataExamine.examine['vitamin_ck_should_not_use'] = $('#vitamin_ck_should_not_use').val();
            break;
        case 3:
            dataExamine.examine['menu_example'] = JSON.stringify(dataExamine.menuExamine);
            break;
        case 4:
            dataExamine.examine['prescription'] = JSON.stringify(dataExamine.prescription);
            break;
        case 5:
            dataExamine.examine['medical_test'] = JSON.stringify(dataExamine.medicalTest);
            break;
        default: break;
    }
    dataExamine.tab = tab;
    if (tab == 4 && dataExamine.medicineList.length == 0) {
        $('#medicine_type_id').trigger('change');
    }
    if (tab == 5) {
        if (dataExamine.isGetListMedicalTest == 0) {
            dataExamine.isGetListMedicalTest = 1;
            $('#medical_test_type').trigger('change');
        }
    }
    expandTextarea();
}

function changeTabConsultation(tab) {
    switch (dataExamine.tab) {
        case 1:
            dataExamine.consultation['cus_name'] = $('#cus_name').val();
            dataExamine.consultation['cus_phone'] = $('#cus_phone').val();
            dataExamine.consultation['cus_email'] = $('#cus_email').val();
            dataExamine.consultation['cus_gender'] = $('#cus_gender').val();
            dataExamine.consultation['cus_birthday'] = $('#cus_birthday').val();
            dataExamine.consultation['cus_address'] = $('#cus_address').val();
            dataExamine.consultation['cus_anamnesis'] = $('#cus_anamnesis').val();
            dataExamine.consultation['cus_living_habits'] = $('#cus_living_habits').val();
            dataExamine.consultation['diagnostic'] = $('#diagnostic').val();
            dataExamine.consultation['cus_length'] = $('#cus_length').val();
            dataExamine.consultation['cus_cctc'] = $('#cus_cctc').val(); // chiều cao tiêu chuẩn
            dataExamine.consultation['cus_cntc'] = $('#cus_cntc').val(); // cân nặng tiêu chuẩn
            dataExamine.consultation['cus_cnht'] = $('#cus_cnht').val(); // cân nặng hiện tại
            dataExamine.consultation['cus_cnbt'] = $('#cus_cnbt').val(); // cân nặng thường có
            dataExamine.consultation['cus_bmi'] = $('#cus_bmi').val();
            dataExamine.consultation['cus_ncdd'] = $('#cus_ncdd').val();
            dataExamine.consultation['cus_height_by_age'] = $('#cus_height_by_age').val();
            dataExamine.consultation['cus_weight_by_age'] = $('#cus_weight_by_age').val();
            dataExamine.consultation['cus_bmi_by_age'] = $('#cus_bmi_by_age').val();
            dataExamine.consultation['cus_height_by_weight'] = $('#cus_height_by_weight').val();
            break;
        case 2:
            dataExamine.consultation['clinical_exam'] = JSON.stringify(dataConsultation.clinicalExam);
            dataExamine.consultation['nutrition_tracking'] = JSON.stringify(dataConsultation.nutritionTracking);
            dataExamine.consultation['subclinical'] = JSON.stringify(dataConsultation.subclinical);
            dataExamine.consultation['subclinical_orther'] = JSON.stringify(dataConsultation.subclinicalOrther);
            dataExamine.consultation['medicine'] = JSON.stringify(dataConsultation.medicine);
            dataExamine.consultation['additional'] = JSON.stringify(dataConsultation.additional);
            break;
        default: break;
    }
    dataExamine.tab = tab;
    expandTextarea();
    console.log('changeTabConsultation', dataExamine);
}

function diff_years(dt2, dt1) {
    let diff = (dt2.getTime() - dt1.getTime()) / 1000;
    //ngày
    diff /= (60 * 60 * 24);
    let year_old = Math.abs(diff / 365.25);
    let type_year_old = 1;
    if (year_old >= 18) {
        year_old = Math.floor(year_old);
    } else if (year_old >= 5.5) {
        let year_round = Math.round(parseFloat(year_old.toFixed(1)));
        if (year_round !== year_old) {
            if (year_round > year_old) {
                year_old = year_round - 0.5;
            } else {
                year_old = year_round;
            }
        }
    } else if (year_old > 5) {
        year_old = 5.5;
    } else {
        type_year_old = 0;
        year_old = Math.floor(Math.abs(diff / 30.43));
    }
    $('label[for="cus_age"]').attr('data-type', type_year_old);
    if (type_year_old == 0) {
        $('label[for="cus_age"]').text("Tháng");
        if (year_old == 0) year_old = 1;
    } else {
        $('label[for="cus_age"]').text("Tuổi");
    }
    // return Math.floor(Math.abs(diff/365.25));
    return year_old;
}

function caculateYearOld(selectedDates, dateStr, instance) {
    let cus_birthday = new Date(dateStr.split("-").reverse().join("-"));
    let now = new Date();
    let yearOld = diff_years(now, cus_birthday);
    if (yearOld > 0 && dataExamine.isChangeInputAge == false) {
        $('#cus_age').val(yearOld);
    }
}

function checkStandardWeightHeight() {
    try {
        let year_old = $('#cus_age').val();
        let type_year_old = $('label[for="cus_age"]').text() == 'Tuổi' ? 1 : 0;
        let gender = parseInt($('#cus_gender').val());
        let cus_length = $('#cus_length').val();
        $('#cus_cctc').val('');
        $('#cus_cntc').val('');
        if (type_year_old == 1 && parseInt(year_old) > 18) {
            $('label[for="cus_cntc"]').text('CNKN (kg)');
        }
        // else{
        if ((gender == 1 || gender == 0) && year_old) {
            let loading = $("#loading-page");
            let url = '/examine/search/standard-weight-height';
            $.ajax({
                type: 'POST',
                url: url,
                data: { year_old: year_old, type_year_old: type_year_old, gender: gender, cus_length: cus_length },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success && result.data) {
                        if (result.data.height) $('#cus_cctc').val(parseFloat((parseFloat(result.data.height) / 100)).toFixed(4));
                        else $('#cus_cctc').val('');
                        if (result.data.weight) $('#cus_cntc').val(result.data.weight);
                        else $('#cus_cntc').val('');
                        if (result.data.contentNCDD) $('#cus_ncdd').val(result.data.contentNCDD);
                        else $('#cus_ncdd').val('');

                        if (result.data.height_min && result.data.height_max) $('#cus_height_by_age').val(result.data.height_min + ' - ' + result.data.height_max);
                        else $('#cus_height_by_age').val('');
                        if (result.data.weight_min && result.data.weight_max) $('#cus_weight_by_age').val(result.data.weight_min + ' - ' + result.data.weight_max);
                        else $('#cus_weight_by_age').val('');
                        if (result.data.bmi_min && result.data.bmi_max) $('#cus_bmi_by_age').val(result.data.bmi_min + ' - ' + result.data.bmi_max);
                        else $('#cus_bmi_by_age').val('');
                        if (result.data.weight_height_min && result.data.weight_height_max) $('#cus_height_by_weight').val(result.data.weight_height_min + ' - ' + result.data.weight_height_max);
                        else $('#cus_height_by_weight').val('');
                    }
                    if (type_year_old == 1 && parseInt(year_old) > 18) {
                        if ($('#cus_length').val() && !isNaN(parseFloat($('#cus_length').val()))) {
                            let ccht = parseFloat($('#cus_length').val());
                            let cnkn = ccht * ccht * 22;
                            $('#cus_cntc').val(parseInt(cnkn));
                        }
                    }
                    if (type_year_old == 1 && parseInt(year_old) > 20) {
                        $('#cus_ncdd').val('Theo công thức');
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        }
        // }
    } catch (error) {

    }
}

function caculateBMI() {
    try {
        let height = $('#cus_length').val();
        let weight = $('#cus_cnht').val();
        if (height && weight) {
            let bmi = weight / (height * height);
            $('#cus_bmi').val(bmi.toFixed(2));
        }
    } catch (error) {

    }
}

function addMedicine() {
    let medicine_id = parseInt($('#medicine_id').val());
    let medicine_name = $('#medicine_id').find(':selected').text();
    if (!medicine_id) {
        displayError('Chưa chọn thuốc!');
        return;
    }
    let medicine_total = $('#total_medinice').val();
    if (!medicine_total || medicine_total == 0 || isNaN(medicine_total)) {
        displayError('Thiếu số lượng!');
        return;
    }
    let medicine_unit = $('#unit_medinice').val();
    let medicine_note = $('#use_medinice').val();
    let prescriptionItem = {
        stt: dataExamine.id_prescription++,
        name: medicine_name,
        id: medicine_id,
        total: parseInt(medicine_total),
        unit: medicine_unit,
        note: medicine_note
    }
    if (dataExamine.prescription.length == 0) {
        dataExamine.prescription.push(prescriptionItem);
    } else {
        let isExist = false;
        for (let item of dataExamine.prescription) {
            if (item.id == prescriptionItem.id) {
                isExist = true;
                item.total = parseInt(item.total) + parseInt(prescriptionItem.total);
                item.note = prescriptionItem.note;
                break;
            }
        }
        if (!isExist) {
            dataExamine.prescription.push(prescriptionItem);
        }
    }

    if (dataExamine.prescription.length > 0) {
        $("#tb_prescription").show();
    } else {
        $("#tb_prescription").hide();
    }
    addHtmlPrescription(prescriptionItem);
}

function addHtmlPrescription(prescriptionItem) {
    let status = parseInt($('#status_examine').val());
    let isLockInput = (status == 4 || (status == 3 && dataExamine.isDetail == 'true')) ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr('id', 'tr_' + prescriptionItem.id);

    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    let td4 = document.createElement("td");

    $(td3).addClass("min-w-150px")
        .append($("<input/>")
            .attr({ "type": "text", "value": prescriptionItem.note, "readonly": isLockInput })
            .addClass("form-control form-control-title p-1 fs-13px")
            .data("medicine_id", prescriptionItem.id)
            .change(function () {
                let id = $(this).data('medicine_id');
                let value = $(this).val();
                changeMedicine(id, value, 'note');
            })
        );

    $(td4).append($("<input/>")
        .attr({ "type": "text", "value": prescriptionItem.total, "readonly": isLockInput })
        .addClass("form-control form-control-title p-1 fs-6 text-red")
        .data("medicine_id", prescriptionItem.id)
        .change(function () {
            let id = $(this).data('medicine_id');
            let value = parseInt($(this).val());
            if (isNaN(value)) {
                displayErrorToastr('Số lượng thuốc không đúng định dạng!')
            } else {
                changeMedicine(id, value, 'total');
            }
        })
    );

    let td5 = document.createElement("td");
    let td6 = document.createElement("td");

    $(td2).attr({ class: 'min-w-150px fs-6' });
    $(td5).attr({ class: 'fs-13px text-primary' });

    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });

    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);

    button.appendChild(svgElem);

    button.dataset.id = prescriptionItem.id;
    button.dataset.name = prescriptionItem.name;

    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteMedicine(id, name);
    });

    $(td1).text(prescriptionItem.stt);
    $(td2).text(prescriptionItem.name);
    $(td5).text(prescriptionItem.unit);

    div.append(button);
    td6.append(div);
    tr.append(td1);
    tr.append(td2);
    tr.append(td3);
    tr.append(td4);
    tr.append(td5);

    if (isLockInput) {
        $('#active_table_medicine').hide();
    } else {
        tr.append(td6);
    }
    $('#tb_prescription table tbody').append(tr);
}

function showConfirmDeleteMedicine(id, name) {
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_medicine" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteMedicine(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_medicine").modal('show');
}

function deleteMedicine(id) {
    try {
        if (id) {
            $('#tr_' + id).remove();
            removeItemArrayByIdObject(dataExamine.prescription, id);
        }
    } catch (error) {

    }
}

function changeMedicine(id, value, type) {
    try {
        if (id && value) {
            for (let item of dataExamine.prescription) {
                if (item.id == id) {
                    item[type] = value;
                }
            }
        }
    } catch (error) {

    }
}
// xóa phần từ trong mảng
function removeItemArray(arr, val) {
    var j = 0;
    for (var i = 0, l = arr.length; i < l; i++) {
        if (arr[i] !== val) {
            arr[j++] = arr[i];
        }
    }
    arr.length = j;
}

// xóa phần từ object trong mảng bằng id
function removeItemArrayByIdObject(arr, id) {
    var j = 0;
    for (var i = 0, l = arr.length; i < l; i++) {
        if (arr[i].id !== id) {
            arr[j++] = arr[i];
        }
    }
    arr.length = j;
}

function getMedicalTest(id) {
    if (id) {
        let isChecked = $(id).is(':checked');
        let id_medical_test = parseInt($(id).val());
        if (isChecked) {
            if (!dataExamine.medicalTest.includes(id_medical_test)) {
                dataExamine.medicalTest.push(id_medical_test);
            };
        } else {
            if (dataExamine.medicalTest.includes(id_medical_test)) {
                removeItemArray(dataExamine.medicalTest, id_medical_test);
            };
        }
    }
}

function addPrescriptionEdit() {
    if (dataExamine.prescription && dataExamine.prescription.length > 0) {
        $("#tb_prescription").show();
        for (let [i, item] of dataExamine.prescription.entries()) {
            addHtmlPrescription(item);
            if (i == (dataExamine.prescription.length - 1)) {
                dataExamine.id_prescription = item.stt + 1
            }
        }
    } else {
        $("#tb_prescription").hide();
    }
}

function generateFoodName(id) {
    $('#' + id).select2({
        minimumInputLength: 2,
        language: {
            inputTooShort: function () {
                return "Vui lòng nhập ít nhất 2 ký tự";
            },
            noResults: function () {
                return "Không có kết quả được tìm thấy";
            },
            searching: function () {
                return "Đang tìm kiếm...";
            }
        },
        escapeMarkup: function (markup) {
            return markup;
        },
        placeholder: 'Chọn thực phẩm',
        allowClear: true,
        ajax: {
            url: '/api/foods',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                    food_type: $('#food_type').val() || '',
                    food_year: $('#food_year').val() || '',
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                if (data.success) {
                    dataExamine.foodNameListSearch = data.data;
                }
                return {
                    results: $.map(data.data || [], function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                            weight: item.weight,
                            energy: item.energy,
                            protein: item.protein,
                            animal_protein: item.animal_protein,
                            lipid: item.lipid,
                            unanimal_lipid: item.unanimal_lipid,
                            carbohydrate: item.carbohydrate,
                            data: item
                        }
                    }),
                    pagination: {
                        more: (params.page * 30) < (data.total_count || 0)
                    }
                };
            },
            cache: true
        }
    });
}

// Hàm tạo dropdown món ăn
function generateDishName(id) {
    $('#' + id).select2({
        minimumInputLength: 2,
        language: {
            inputTooShort: function () {
                return "Vui lòng nhập ít nhất 2 ký tự";
            },
            noResults: function () {
                return "Không có kết quả được tìm thấy";
            },
            searching: function () {
                return "Đang tìm kiếm...";
            }
        },
        escapeMarkup: function (markup) {
            return markup;
        },
        placeholder: 'Chọn món ăn',
        allowClear: true,
        ajax: {
            url: '/api/dishes',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                try {
                    // Chấp nhận nhiều format: {data:[...]}, [...], hoặc {"0":{...}}
                    const raw = (data && (data.data != null ? data.data : data)) || [];
                    const arr = Array.isArray(raw) ? raw : Object.values(raw);
                    const results = arr.map(function(item){
                        // Chuẩn hóa field
                        const id = item.id != null ? item.id : item.value;
                        const text = item.name != null ? item.name : (item.label || '');
                        return { id: id, text: text, data: item };
                    }).filter(it => it.id != null && it.text !== '');

                    return {
                        results: results,
                        pagination: { more: false }
                    };
                } catch (e) {
                    return { results: [], pagination: { more: false } };
                }
            },
            cache: true
        }
    });
}

// Hàm thêm món ăn vào thực đơn
function addDishToMenu() {
    try {
        let menuTimeId = $('#dish_menuTime_id').val();
        let dishId = $('#dish_name').val();

        if (!menuTimeId) {
            toastr.error('Vui lòng chọn giờ ăn!');
            return;
        }

        if (!dishId) {
            toastr.error('Vui lòng chọn món ăn!');
            return;
        }

        // Lấy chi tiết thực phẩm của món ăn từ API
        $.ajax({
            url: `/api/dish-foods/${dishId}`,
            method: 'GET',
            success: function(response) {
                // Chuẩn hóa dữ liệu: hỗ trợ {data:[...]}, [...], hoặc object index
                const raw = (response && (response.data != null ? response.data : response)) || [];
                const dishFoods = Array.isArray(raw) ? raw : Object.values(raw);
                if (!dishFoods || dishFoods.length === 0) {
                    toastr.error('Không thể lấy thông tin chi tiết món ăn!');
                    return;
                }

                let menu_id = parseInt($('#menu_id').val());
                if (!(menu_id && dataExamine.menuExamine.length > 0)) {
                    toastr.error('Không tìm thấy thực đơn hiện tại!');
                    return;
                }

                for (let item of dataExamine.menuExamine) {
                    if (menu_id == item.id) {
                        for (let menu_time of item.detail) {
                            if (menu_time.id == menuTimeId) {
                                // Đảm bảo courses tồn tại
                                if (!Array.isArray(menu_time.courses) || menu_time.courses.length === 0) {
                                    menu_time.courses = [{ id: 1, name: '' }];
                                }
                                const currentMax = menu_time.courses.reduce((m, c) => Math.max(m, c.id || 0), 0);
                                const newCourseId = (currentMax || 0) + 1;
                                const dishText = ($('#dish_name').select2('data')[0] || {}).text || '';
                                menu_time.courses.push({ id: newCourseId, name: dishText });

                                // Thêm thực phẩm theo tỉ lệ actual_weight/weight
                                dishFoods.forEach(df => {
                                    const baseWeight = (df.weight != null ? parseFloat(df.weight) : 0) || 0;
                                    const actualWeight = (df.actual_weight != null ? parseFloat(df.actual_weight) : baseWeight) || 0;

                                    const nextId = menu_time.listFood && menu_time.listFood.length > 0
                                        ? menu_time.listFood[menu_time.listFood.length - 1].id + 1
                                        : 1;
                                    const baseFood = {
                                        ...df,
                                        id: nextId,
                                        id_food: df.food_info_id || df.food_id || 0,
                                        name: df.name || df.ten || '',
                                        course_id: newCourseId,
                                        weight: baseWeight
                                    };
                                    const calculated = caculateFoodInfo(baseFood, actualWeight);
                                    menu_time.listFood.push(calculated);
                                });

                                // Render lại bảng và tổng
                                updateMenuDisplayLegacy(item);
                                $('#dish_name').val('').trigger('change');
                                toastr.success('Đã thêm món "' + dishText + '" vào thực đơn!');
                                return;
                            }
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error getting dish foods:', error);
                toastr.error('Có lỗi xảy ra khi lấy thông tin món ăn!');
            }
        });

    } catch (error) {
        console.error('Error adding dish to menu:', error);
        toastr.error('Có lỗi xảy ra khi thêm món ăn!');
    }
}

function deleteFood(id_food, id_menu_time) {
    try {
        Swal.fire({
            title: 'Xóa thực phẩm?',
            text: 'Bạn có chắc muốn xóa thực phẩm này khỏi món?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
        }).then((result) => {
            if (!result.isConfirmed) return;

            let menu_id = parseInt($('#menu_id').val());
            for (let menu of dataExamine.menuExamine) {
                if (menu.id == menu_id) {
                    for (let menu_time of menu.detail) {
                        if (menu_time.id == id_menu_time) {
                            removeItemArrayByIdObject(menu_time.listFood, id_food);
                            break;
                        }
                    }
                }
            }
            $('#food_' + id_menu_time + "_" + id_food).remove();
            let rowspan = $('#menu_time_' + id_menu_time + ' td:first-child').attr("rowspan");
            $('#menu_time_' + id_menu_time + ' td:first-child').attr('rowspan', (rowspan - 1));

            let listFoodTotal = [];
            for (let menu of dataExamine.menuExamine) {
                if (menu.id == menu_id) {
                    for (let item of menu.detail) {
                        listFoodTotal.push(...item.listFood);
                    }
                    break;
                }
            }
            setTotalMenu(listFoodTotal);
            toastr.success('Đã xóa thực phẩm.');
        });
    } catch (error) {

    }
}

function caculateFoodInfo(food, weight) {
    try {
        if (!food) return food;
        const newWeight = isNaN(parseFloat(weight)) ? 0 : parseFloat(weight);
        if (newWeight <= 0) { food.weight = 0; return food; }

        const originalWeight = parseFloat(food.weight) || 0;
        const ratio = originalWeight > 0 ? newWeight / originalWeight : 0;

        // Tạo object mới
        const updated = { ...food };
        updated.weight = newWeight;

        const fieldsToSkip = new Set([
            'id','id_food','name','ten','code','type','type_year','active','created_by','note','created_at','updated_at','course_id'
        ]);

        Object.keys(food).forEach(key => {
            if (fieldsToSkip.has(key)) return;
            const val = food[key];
            if (typeof val === 'number' && val !== null && val !== undefined) {
                updated[key] = ratio > 0 ? Math.round((val * ratio) * 100) / 100 : val;
            }
        });

        // Đồng bộ fat/lipid nếu chỉ có một trong hai
        if (updated.fat == null && updated.lipid != null) updated.fat = updated.lipid;
        if (updated.lipid == null && updated.fat != null) updated.lipid = updated.fat;

        return updated;
    } catch (error) {
        return food;
    }
}

function changeWeightFood(id_food, menuTime_id, value) {
    try {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of dataExamine.menuExamine) {
            if (menu_id == menu.id) {
                let listFoodTotal = [];
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        for (let food of item.listFood) {
                            if (id_food == food.id) {
                                const updatedFood = caculateFoodInfo(food, value);
                                Object.assign(food, updatedFood);

                                // Cập nhật các cột hiển thị
                                currentDisplayConfig.visible_columns.forEach(column => {
                                    const $cell = $("#food_" + menuTime_id + "_" + food.id + "_" + column);
                                    if (!$cell.length) return;

                                    if (column === 'weight') {
                                        // Ô weight là input:
                                        const $input = $cell.find('input');
                                        if ($input.length) $input.val(food.weight || 0);
                                        return;
                                    }

                                    let displayValue = food[column];
                                    // Mapping lipid -> fat nếu cần
                                    if ((displayValue === undefined || displayValue === null) && column === 'fat') {
                                        displayValue = food.lipid;
                                    }
                                    if (displayValue !== null && displayValue !== undefined && displayValue !== '') {
                                        if (!isNaN(displayValue) && displayValue !== '') {
                                            displayValue = parseFloat(displayValue).toFixed(2);
                                            displayValue = parseFloat(displayValue).toString();
                                        }
                                        $cell.text(displayValue);
                                    } else {
                                        $cell.text('0');
                                    }
                                });
                                break;
                            }
                        }
                    }
                    listFoodTotal.push(...item.listFood);
                }
                setTotalMenu(listFoodTotal);
                break;
            }
        }
    } catch (error) {

    }
}

function changeCourse(menuTime_id) {
    try {
        let name_course = $('#menu_time_' + menuTime_id).find("input").val();
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of dataExamine.menuExamine) {
            if (menu_id == menu.id) {
                for (let item of menu.detail) {
                    if (menuTime_id == item.id) {
                        item.name_course = name_course;
                        break;
                    }
                }
                break;
            }
        }
    } catch (error) {

    }
}

function generateMenuExamine() {
    // if(dataExamine.menuExamine && dataExamine.menuExamine.length == 0){
    //     let menu = addMenuList();
    //     dataExamine.menuExamine.push(menu);
    // }
    if (dataExamine.menuExamine && dataExamine.menuExamine.length > 0) {
        for (let [i, item] of dataExamine.menuExamine.entries()) {
            let newOption = new Option(item.name, item.id, false, false);
            if (i == (dataExamine.menuExamine.length - 1)) {
                $('#menu_id').append(newOption).trigger('change');
            } else {
                $('#menu_id').append(newOption);
            }
            if (i == 0) {
                let listFoodTotal = [];
                for (let menuTime of item.detail) {
                    listFoodTotal.push(...menuTime.listFood);
                }
                setTotalMenu(listFoodTotal);
            }
        }
        let menu_id = parseInt($('#menu_id').val() ? $('#menu_id').val() : 0);
        generateTableMenu(menu_id);
    }
}

function addMenuList() {
    let id = 1;
    if (dataExamine.menuExamine.length > 0) {
        id = dataExamine.menuExamine[dataExamine.menuExamine.length - 1].id + 1;
    }
    let menu = {
        id: id,
        name: "Thực đơn " + id,
        detail: [],
        note: ''
    }
    for (let time of dataExamine.listMenuTime) {
        menu.detail.push({
            id: time.id,
            name: time.name,
            courses: [{ id: 1, name: '' }],
            listFood: []
        });
    }
    return menu;
}

function generateTableMenu(menu_id) {
    try {
        // let menu_id = parseInt($("#menu_id").val());
        if (menu_id) {
            if (dataExamine.menuExamine.length > 0) {
                for (let menu of dataExamine.menuExamine) {
                    if (menu.id == menu_id) {
                        $('#name_menu').val(menu.name);
                        $('#menu_example_note').val(menu.note);
                        migrateMenuTimesToCourses(menu.detail);
                        addTemplateListMenuTime(menu.detail);
                        break;
                    }
                }
            }
            $('#tb_menu').show();
        } else {
            $('#tb_menu').hide();
        }
    } catch (error) {

    }
}

function addTemplateListMenuTime(listMenuTime) {
    try {
        if (listMenuTime.length > 0) {
            let listFoodTotal = [];
            // Đảm bảo migrate dữ liệu cũ sang cấu trúc courses/course_id
            migrateMenuTimesToCourses(listMenuTime);
            for (let mt of listMenuTime) {
                const colspanCount = currentDisplayConfig.visible_columns.length + 1;
                const totalRows = computeMenuTimeRowspan(mt);

                const firstCourse = (mt.courses && mt.courses[0]) ? mt.courses[0] : { id: 1, name: '' };
                const $firstRow = $('<tr/>')
                    .attr('id', 'menu_time_' + mt.id)
                    .addClass('text-center');
                $firstRow.append($('<td/>')
                    .css({ "writing-mode": "vertical-rl", "vertical-align": "middle" })
                    .attr('rowspan', Math.max(totalRows, 1))
                    .text(mt.name)
                );
                $firstRow.append(createCourseHeaderCell(mt.id, firstCourse, colspanCount));
                // Cột thao tác luôn ở ngoài cùng
                $firstRow.append($('<td class="text-center"/>').append(
                    $('<button type="button" class="btn btn-sm btn-outline-danger" title="Xóa món"/>')
                        .data('menu_time_id', mt.id)
                        .data('course_id', firstCourse.id)
                        .text('X')
                        .click(function(){ deleteCourse($(this).data('menu_time_id'), $(this).data('course_id')); })
                ));
                $("#tb_menu").find('tbody').append($firstRow);

                const foodsCourse0 = (mt.listFood || []).filter(f => f.course_id == firstCourse.id);
                foodsCourse0.forEach(food => {
                    const $row = addFoodTemplate(food, mt.id);
                    $("#tb_menu").find('tbody').append($row);
                });
                listFoodTotal.push(...foodsCourse0);

                if (mt.courses && mt.courses.length > 1) {
                    for (let i = 1; i < mt.courses.length; i++) {
                        const course = mt.courses[i];
                        const $courseRow = $('<tr/>')
                            .attr('id', `course_${mt.id}_${course.id}`)
                            .addClass('text-center')
                            .append(createCourseHeaderCell(mt.id, course, colspanCount))
                            // Cột thao tác luôn ở ngoài cùng
                            .append($('<td class="text-center"/>').append(
                                $('<button type="button" class="btn btn-sm btn-outline-danger" title="Xóa món"/>')
                                    .data('menu_time_id', mt.id)
                                    .data('course_id', course.id)
                                    .text('X')
                                    .click(function(){ deleteCourse($(this).data('menu_time_id'), $(this).data('course_id')); })
                            ));
                        $("#tb_menu").find('tbody').append($courseRow);

                        const foodsByCourse = (mt.listFood || []).filter(f => f.course_id == course.id);
                        foodsByCourse.forEach(food => {
                            const $row = addFoodTemplate(food, mt.id);
                            $("#tb_menu").find('tbody').append($row);
                        });
                        listFoodTotal.push(...foodsByCourse);
                    }
                }
            }
            setTotalMenu(listFoodTotal);
        }
    } catch (error) {

    }
}

function addMenu() {
    try {
        //thêm menu trống
        let menuNew = addMenuList();
        dataExamine.menuExamine.push(menuNew);
        //thêm select menu
        let newOption = new Option(menuNew.name, menuNew.id, false, false);
        $('#menu_id').append(newOption).trigger('change');
        resetTemplateMenu();
        //tạo template menu
        generateTableMenu(menuNew.id);
        $('#tb_menu').show();
    } catch (error) {

    }
}

function resetTemplateMenu() {
    //Xóa template menu hiện tại
    $('#tb_menu').find('tbody').empty();
    $('#menu_example_note').val('');
    $('#total_energy').text('');
    $('#total_protein').text('');
    $('#total_animal_protein').text('');
    $('#total_lipid').text('');
    $('#total_unanimal_lipid').text('');
    $('#total_carbohydrate').text('');

    $('#total_protein_percent').text('');
    $('#total_lipid_percent').text('');
    $('#total_carbohydrate_percent').text('');
}
// Chọn thực đơn mẫu trong list
function chooseMenuExample() {
    try {
        let id = 1;
        // let isGenerate = true;
        // if($('#menu_id').val()){
        //     isGenerate = false;
        // }
        if (dataExamine.menuExamine.length > 0) {
            id = dataExamine.menuExamine[dataExamine.menuExamine.length - 1].id + 1;
        }
        let menu_example_id = parseInt($("#menuExample_id").val());
        if (menu_example_id) {
            for (let menu of dataExamine.menuExample) {
                if (menu.id == menu_example_id) {
                    let menuNew = {
                        id: id,
                        name: menu.name_menu,
                        detail: JSON.parse(menu.detail)
                    };
                    dataExamine.menuExamine.push(menuNew);
                    let newOption = new Option(menuNew.name, menuNew.id, false, true);
                    $('#menu_id').append(newOption).trigger('change');
                    break;
                }
            }
            // if(isGenerate){
            $('#tb_menu tbody').empty();
            $('#tb_menu').show();
            generateTableMenu(id);
            // }
        } else {
            displayError("Vui lòng chọn mẫu!");
        }
    } catch (error) {

    }
}
// Thêm thực phẩm vào thực đơn
function addFoodToMenu() {
    try {
        let menu_id = parseInt($('#menu_id').val());
        if (menu_id && dataExamine.menuExamine.length > 0) {
            for (let item of dataExamine.menuExamine) {
                if (menu_id == item.id) {
                    let menuTime_id = parseInt($('#menuTime_id').val());
                    if (menuTime_id) {
                        if (item.detail.length > 0) {
                            let listFoodTotal = [];
                            for (let menuTime of item.detail) {
                                if (menuTime_id == menuTime.id) {
                                    let id = menuTime.listFood.length == 0 ? 1 : menuTime.listFood[menuTime.listFood.length - 1].id + 1;
                                    if (!Array.isArray(menuTime.courses) || menuTime.courses.length === 0) {
                                        menuTime.courses = [{ id: 1, name: '' }];
                                    }
                                    const targetCourse = menuTime.courses.length === 1 ? menuTime.courses[0] : menuTime.courses[menuTime.courses.length - 1];
                                    let food = {
                                        "id": id,
                                        "id_food": parseInt($('#food_name').val()),
                                        "name": $('#food_name').find(':selected').text(),
                                        "weight": parseInt($('#weight_food').val()),
                                        "course_id": targetCourse.id,
                                        "energy": isNaN(parseInt($('#energy_food').val())) ? 0 : parseInt($('#energy_food').val()),
                                        "protein": isNaN(parseFloat($('#protein_food').val())) ? 0 : parseFloat($('#protein_food').val()),
                                        "animal_protein": isNaN(parseFloat($('#animal_protein').val())) ? 0 : parseFloat($('#animal_protein').val()),
                                        "fat": isNaN(parseFloat($('#lipid_food').val())) ? 0 : parseFloat($('#lipid_food').val()),
                                        "unanimal_lipid": isNaN(parseFloat($('#unanimal_lipid').val())) ? 0 : parseFloat($('#unanimal_lipid').val()),
                                        "carbohydrate": isNaN(parseFloat($('#carbohydrate').val())) ? 0 : parseFloat($('#carbohydrate').val())
                                    }
                                    menuTime.listFood.push(food);
                                    let foodTemplate = addFoodTemplate(food, menuTime_id);
                                    const $courseRows = $(`tr[id^="food_${menuTime_id}_"][data-course-id="${targetCourse.id}"]`);
                                    if ($courseRows.length > 0) {
                                        $courseRows.last().after(foodTemplate);
                                    } else {
                                        const headerSelector = `#course_${menuTime_id}_${targetCourse.id}`;
                                        if ($(headerSelector).length > 0) {
                                            $(headerSelector).after(foodTemplate);
                                        } else {
                                            $('#menu_time_' + menuTime_id).after(foodTemplate);
                                        }
                                    }
                                    const newRowspan = computeMenuTimeRowspan(menuTime);
                                    $('#menu_time_' + menuTime_id + ' td:first-child').attr('rowspan', Math.max(newRowspan, 1));
                                }
                                listFoodTotal.push(...menuTime.listFood);
                            }
                            setTotalMenu(listFoodTotal);
                        } else {
                            displayError('Chưa có dữ liệu giờ ăn!');
                        }
                    } else {
                        displayError('Bạn chưa chọn giờ ăn!');
                    }
                    break;
                }
            }
        } else {
            displayError('Tạo mới hoặc chọn menu mẫu!');
        }
    } catch (error) {

    }
}

// Hàm hiển thị modal cấu hình bảng
function showTableConfigModal() {
    try {
        const modalBody = $('#tableConfigModalBody');
        modalBody.empty(); // Clear previous content to rebuild

        const groupedColumns = {};
        for (const key in availableColumns) {
            const group = availableColumns[key].group || 'other';
            if (!groupedColumns[group]) {
                groupedColumns[group] = [];
            }
            groupedColumns[group].push(key);
        }

        let html = '<div class="row">';
        let col1Html = '<div class="col-md-6">';
        let col2Html = '<div class="col-md-6">';
        
        let i = 0;
        for (const groupKey in columnGroups) {
            if (groupedColumns[groupKey]) {
                let groupHtml = `<h6>${columnGroups[groupKey]}</h6>`;
                groupedColumns[groupKey].forEach(columnKey => {
                    const column = availableColumns[columnKey];
                    const isChecked = currentDisplayConfig.visible_columns.includes(columnKey) ? 'checked' : '';
                    groupHtml += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="col_${columnKey}" value="${columnKey}" ${isChecked}>
                            <label class="form-check-label" for="col_${columnKey}">${column.label}</label>
                        </div>
                    `;
                });

                if (i % 2 === 0) {
                    col1Html += groupHtml;
                } else {
                    col2Html += groupHtml;
                }
                i++;
            }
        }

        col1Html += '</div>';
        col2Html += '</div>';
        html += col1Html + col2Html + '</div>';

        modalBody.html(html);

        $('#modal-table-config').modal('show');
    } catch (error) {
        console.error('Error showing table config modal:', error);
    }
}

// Hàm áp dụng cấu hình bảng
function applyTableConfig() {
    try {
        const newVisibleColumns = [];

        // Lấy các cột được chọn
        Object.keys(availableColumns).forEach(column => {
            const checkbox = document.getElementById('col_' + column);
            if (checkbox && checkbox.checked) {
                newVisibleColumns.push(column);
            }
        });

        // Cập nhật cấu hình
        currentDisplayConfig.visible_columns = newVisibleColumns;

        // Cập nhật header bảng
        updateTableHeader();

        // Tạo lại bảng với cấu hình mới
        const menuId = parseInt($('#menu_id').val());
        if (menuId) {
            generateTableMenu(menuId);
        }

        // Lưu cấu hình sau khi áp dụng
        const payload = {
            visible_columns: currentDisplayConfig.visible_columns,
            column_order: currentDisplayConfig.column_order || currentDisplayConfig.visible_columns
        };
        try {
            localStorage.setItem('table_display_config', JSON.stringify(payload));
        } catch (_) {}

        $('#modal-table-config').modal('hide');
        toastr.success('Đã cập nhật cấu hình hiển thị bảng!');

    } catch (error) {
        console.error('Error applying table config:', error);
        toastr.error('Có lỗi xảy ra khi cập nhật cấu hình!');
    }
}

// Hàm cập nhật header bảng theo cấu hình
function updateTableHeader() {
    try {
        let headerHtml = `
            <tr>
                <td rowspan="2"><span>Giờ ăn</span></td>
                <td colspan="${currentDisplayConfig.visible_columns.length + 1}">
                    <input type="text" id="name_menu" class="form-control form-control-title p-1" style="text-align: center;" ${(parseInt($('#status_examine').val()) == 4 || (parseInt($('#status_examine').val()) == 3 && dataExamine.isDetail == 'true')) ? 'readonly' : ''}>
                </td>
            </tr>
            <tr>
                <td>Thực phẩm</td>
        `;

        // Thêm header cho các cột được chọn
        currentDisplayConfig.visible_columns.forEach(column => {
            const columnName = availableColumns[column].label;
            if (columnName) {
                headerHtml += `<td>${columnName}</td>`;
            }
        });

        // Cột thao tác luôn ở ngoài cùng
        headerHtml += `<td style="min-width: 40px;"></td></tr>`;

        // Cập nhật header
        $('#tb_menu thead').html(headerHtml);

    } catch (error) {
        console.error('Error updating table header:', error);
    }
}

// ================== Persist cấu hình hiển thị cột ==================
function getPatientIdFromUrl() {
    try {
        // Ưu tiên lấy từ input ẩn nếu có
        const $el = $('#patient_id');
        if ($el && $el.val()) return $el.val();

        // Thử parse từ URL theo /research/detail/:id hoặc /examine/detail/:id
        const parts = (window.location.pathname || '').split('/').filter(Boolean);
        const idxDetail = parts.indexOf('detail');
        if (idxDetail !== -1 && parts[idxDetail + 1]) return parts[idxDetail + 1];
        const idxExamine = parts.indexOf('examine');
        if (idxExamine !== -1 && parts[idxExamine + 1]) return parts[idxExamine + 1];
    } catch (_) {}
    return null;
}

function loadTableDisplayConfigFromLocal() {
    try {
        const raw = localStorage.getItem('table_display_config');
        if (!raw) return;
        const cfg = JSON.parse(raw);
        if (cfg && Array.isArray(cfg.visible_columns) && cfg.visible_columns.length > 0) {
            currentDisplayConfig.visible_columns = cfg.visible_columns;
            currentDisplayConfig.column_order = cfg.column_order || cfg.visible_columns;
            updateTableHeader();
            const menuId = parseInt($('#menu_id').val());
            if (menuId) generateTableMenu(menuId);
        }
    } catch (e) {
        console.error('loadTableDisplayConfigFromLocal error:', e);
    }
}

// Gọi load config khi trang sẵn sàng (sau khi header mặc định được build)
$(document).ready(function(){
    try { loadTableDisplayConfigFromLocal(); } catch (_) {}
});

// ================== Inline edit tên thực đơn (tương thích select2) ==================
function enableInlineMenuNameEdit() {
    try {
        const $title = $('#name_menu_text');
        if ($title.length === 0) return;
        $title.off('dblclick.inlineEdit');
        $title.on('dblclick.inlineEdit', function(){
            if ($('#name_menu_text_input').length > 0) return;
            const currentText = $title.text().trim();
            const $input = $('<input/>')
                .attr({ id: 'name_menu_text_input', type: 'text' })
                .addClass('form-control form-control-title p-1')
                .val(currentText)
                .css({ 'max-width': '420px' });
            $title.empty().append($input);
            $input.focus().select();

            const commit = () => {
                const newName = ($input.val() || '').trim() || currentText;
                $title.text(newName);
                $('#name_menu').val(newName);

                // Đồng bộ vào dataExamine.menuExamine và select2 #menu_id
                const menuId = parseInt($('#menu_id').val());
                if (!isNaN(menuId)) {
                    for (let m of dataExamine.menuExamine) {
                        if (m.id === menuId) { m.name = newName; break; }
                    }
                    const $opt = $('#menu_id option:selected');
                    if ($opt && $opt.length) { $opt.text(newName).trigger('change'); }
                }
            };

            $input.on('keydown', function(e){
                if (e.key === 'Enter') { e.preventDefault(); commit(); }
                else if (e.key === 'Escape') { e.preventDefault(); $title.text(currentText); }
            });
            $input.on('blur', function(){ commit(); });
        });
    } catch (e) {
        console.error('enableInlineMenuNameEdit error:', e);
    }
}

function addTemplateMenuTime(menuTime) {
    // Deprecated by multi-course rendering
    return $('<tr/>');
}

function createCourseHeaderCell(menuTimeId, course, colspanCount) {
    const status = parseInt($('#status_examine').val());
    const isLockInput = (status == 4 || (status == 3 && dataExamine.isDetail == 'true')) ? true : false;
    const $cell = $("<td/>").attr('colspan', colspanCount);
    const $wrapper = $('<div class="d-flex align-items-center gap-2 justify-content-center"></div>');
    const $input = $("<input/>")
        .attr({ type: 'text', value: course.name || '', placeholder: 'Nhập tên món ăn', readonly: isLockInput })
        .addClass('form-control form-control-title p-1')
        .css({ 'text-align': 'center' })
        .data('menu_time_id', menuTimeId)
        .data('course_id', course.id)
        .change(function () {
            const mtId = $(this).data('menu_time_id');
            const cId = $(this).data('course_id');
            changeCourseName(mtId, cId, $(this).val());
        });
    $wrapper.append($input);
    $cell.append($wrapper);
    return $cell;
}

function changeCourseName(menuTimeId, courseId, newName) {
    try {
        const menu_id = parseInt($('#menu_id').val());
        for (let menu of dataExamine.menuExamine) {
            if (menu_id == menu.id) {
                for (let mt of menu.detail) {
                    if (mt.id == menuTimeId && Array.isArray(mt.courses)) {
                        for (let c of mt.courses) {
                            if (c.id == courseId) {
                                c.name = newName || '';
                                return;
                            }
                        }
                    }
                }
            }
        }
    } catch (e) {
        console.error('changeCourseName error:', e);
    }
}

function deleteCourse(menuTimeId, courseId) {
    try {
        Swal.fire({
            title: 'Xóa món ăn?',
            text: 'Bạn có chắc muốn xóa món ăn này cùng các thực phẩm của nó?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Xóa',
            cancelButtonText: 'Hủy'
        }).then((result) => {
            if (!result.isConfirmed) return;

            const currentMenu = getCurrentMenuLegacy();
            if (!currentMenu) return;
            const mt = findMenuTimeByIdLegacy(currentMenu, menuTimeId);
            if (!mt) return;
            mt.listFood = (mt.listFood || []).filter(f => f.course_id != courseId);
            mt.courses = (mt.courses || []).filter(c => c.id != courseId);
            if (!mt.courses || mt.courses.length === 0) {
                mt.courses = [{ id: 1, name: '' }];
            }
            updateMenuDisplayLegacy(currentMenu);
            toastr.success('Đã xóa món.');
        });
    } catch (e) {
        console.error('deleteCourse error:', e);
        toastr.error('Không thể xóa món.');
    }
}

function computeMenuTimeRowspan(menuTimeObj) {
    try {
        const courses = Array.isArray(menuTimeObj.courses) ? menuTimeObj.courses : [];
        const listFood = menuTimeObj.listFood || [];
        const coursesLength = courses.length == 0 ? 1 : courses.length;
        const foodsLength = listFood.length;
        const total = coursesLength + foodsLength;
        return Math.max(total, 1);
    } catch (e) { return 1; }
}

function migrateMenuTimesToCourses(listMenuTime) {
    try {
        if (!Array.isArray(listMenuTime)) return;
        listMenuTime.forEach(mt => {
            if (!Array.isArray(mt.courses)) {
                const defaultCourseId = 1;
                mt.courses = [{ id: defaultCourseId, name: mt.name_course || '' }];
                if (Array.isArray(mt.listFood)) {
                    mt.listFood.forEach(f => { if (f && (f.course_id == null)) f.course_id = defaultCourseId; });
                }
            } else {
                const fallbackId = mt.courses.length > 0 ? mt.courses[0].id : 1;
                if (Array.isArray(mt.listFood)) {
                    mt.listFood.forEach(f => {
                        if (f && (f.course_id == null || !mt.courses.some(c => c.id == f.course_id))) {
                            f.course_id = fallbackId;
                        }
                    });
                }
            }
        });
    } catch (e) {
        console.error('migrateMenuTimesToCourses error:', e);
    }
}

function getCurrentMenuLegacy() {
    const menuId = parseInt($('#menu_id').val());
    if (isNaN(menuId)) return null;
    for (let m of dataExamine.menuExamine) { if (m.id === menuId) return m; }
    return null;
}

function findMenuTimeByIdLegacy(currentMenu, menuTimeId) {
    for (let menuTime of currentMenu.detail) {
        if (menuTimeId == menuTime.id) return menuTime;
    }
    return null;
}

function updateMenuDisplayLegacy(currentMenu) {
    $('#tb_menu').show();
    try { $('#tb_menu tbody').empty(); } catch (_) {}
    addTemplateListMenuTime(currentMenu.detail);
    let listFoodTotal = [];
    for (let menuTime of currentMenu.detail) {
        if (menuTime.listFood && menuTime.listFood.length > 0) {
            listFoodTotal.push(...menuTime.listFood);
        }
    }
    setTotalMenu(listFoodTotal);
}

function addFoodTemplate(food, menuTime_id) {
    try {
        let status = parseInt($('#status_examine').val());
        let isLockInput = (status == 4 || (status == 3 && dataExamine.isDetail == 'true')) ? true : false;
        let $row = $('<tr/>').attr("id", "food_" + menuTime_id + "_" + food.id).attr('data-course-id', food.course_id != null ? food.course_id : '');

        // Cột tên thực phẩm (luôn hiển thị)
        $row.append($("<td/>").text(food.name));

        // Thêm các cột theo cấu hình
        currentDisplayConfig.visible_columns.forEach(column => {
            let $cell = $("<td/>").attr("id", "food_" + menuTime_id + "_" + food.id + "_" + column);

            if (column === 'weight') {
                // Cột weight có input để chỉnh sửa
                $cell.append($("<input/>")
                    .attr({ "type": "number", "step": "0.01", "min": "0", "value": food[column] || 0, "readonly": isLockInput })
                    .addClass("form-control form-control-title p-1")
                    .data("food_id", food.id)
                    .data("menu_time_id", menuTime_id)
                    .change(function () {
                        let idFood = $(this).data('food_id');
                        let idMenuTime = $(this).data('menu_time_id');
                        let weight = parseFloat($(this).val()) || 0;
                        changeWeightFood(idFood, idMenuTime, weight);
                    })
                );
            } else {
                // Các cột khác chỉ hiển thị giá trị
                let value = food[column];
                if (value !== null && value !== undefined && value !== '') {
                    // Làm tròn số thập phân nếu là số
                    if (!isNaN(value) && value !== '') {
                        value = parseFloat(value).toFixed(2);
                        // Loại bỏ số 0 thừa ở cuối
                        value = parseFloat(value).toString();
                    }
                    $cell.text(value);
                } else {
                    $cell.text('0');
                }
            }

            $row.append($cell);
        });

        // Cột thao tác (luôn hiển thị)
        $row.append($("<td/>")
            .append($(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" width=".8rem" heigh=".8rem">
                    <path d="M310.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L160 210.7 54.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L114.7 256 9.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 301.3 265.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L205.3 256 310.6 150.6z"/>
                </svg>`)
                .css({ "display": isLockInput ? 'none' : 'block' }))
            .css({ "cursor": "pointer", "pointer-events": isLockInput ? 'none' : 'auto' })
            .data("food_id", food.id)
            .data("menu_time_id", menuTime_id)
            .click(function () {
                let idFood = $(this).data('food_id');
                let idMenuTime = $(this).data('menu_time_id');
                deleteFood(idFood, idMenuTime);
            })
        );

        return $row;
    } catch (error) {
        console.error('Error in addFoodTemplate:', error);
        return $('<tr/>'); // Trả về row trống nếu có lỗi
    }
}

function importExcelFile() {
    try {
        $('#file_input_excel').trigger('click');
    } catch (error) {

    }
}

function getFileExcel() {
    try {
        let dataFile = $('#file_input_excel').prop('files');
        readXlsxFile(dataFile[0]).then(function (rows) {
        });
    } catch (error) {

    }
}

function setTotalMenu(listFood) {
    try {
        if (listFood.length > 0) {
            let total_energy = 0, total_protein = 0, total_animal_protein = 0, total_lipid = 0,
                total_unanimal_lipid = 0, total_carbohydrate = 0;
            for (let food of listFood) {
                total_energy += food.energy;
                total_protein += food.protein;
                total_animal_protein += food.animal_protein;
                total_lipid += food.lipid;
                total_unanimal_lipid += food.unanimal_lipid;
                total_carbohydrate += food.carbohydrate;
            }
            let total_protein_percent = (total_protein * 400) / total_energy;
            let total_lipid_percent = (total_lipid * 900) / total_energy;
            let total_carbohydrate_percent = (total_carbohydrate * 400) / total_energy;

            $('#total_energy').text(String(total_energy));
            $('#total_protein').text(parseFloat(total_protein).toFixed(2));
            $('#total_animal_protein').text(parseFloat(total_animal_protein).toFixed(2));
            $('#total_lipid').text(parseFloat(total_lipid).toFixed(2));
            $('#total_unanimal_lipid').text(parseFloat(total_unanimal_lipid).toFixed(2));
            $('#total_carbohydrate').text(parseFloat(total_carbohydrate).toFixed(2));

            $('#total_protein_percent').text(total_protein_percent.toFixed(2));
            $('#total_lipid_percent').text(total_lipid_percent.toFixed(2));
            $('#total_carbohydrate_percent').text(total_carbohydrate_percent.toFixed(2));
        }
    } catch (error) {

    }
}

function saveMenu(isCreate) {
    try {
        let loading = $("#loading-page");
        let url = '/examine/save-menu';
        let menu_id = parseInt($('#menu_id').val());
        let data = { isCreate: isCreate, name: $("#name_menu").val() };
        for (let menu of dataExamine.menuExamine) {
            if (menu_id == menu.id) {
                data['detail'] = JSON.stringify(menu.detail);
            }
        }
        if (isCreate == 0) {
            data['menu_id'] = parseInt($('#menuExample_id').val());
            if (isNaN(data['menu_id']) || !data.menu_id) {
                displayMessageToastr('Chưa chọn menu mẫu');
            }
        }
        $.ajax({
            type: 'POST',
            url: url,
            data: data,
            beforeSend: function () {
                $('#modal-cf-save-menu').modal('hide');
                loading.show();
            },
            success: function (result) {
                loading.hide();
                if (result.success) {
                    displayMessageToastr('Lưu mẫu thành công');
                } else {
                    displayErrorToastr(result.message);
                }
            },
            error: function (jqXHR, exception) {
                loading.hide();
                ajax_call_error(jqXHR, exception);
            }
        });
    } catch (error) {

    }
}

function viewDetailExamine(id) {
    try {
        let loading = $("#loading-page");
        if (id) {
            let url = '/examine/detail-examine';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success && result.data) {
                        $("#modal-chi-tiet-phieu-kham").find('.table-responsive-inner').html(result.data);
                        $("#modal-chi-tiet-phieu-kham").find('.modal-header').html(
                            `<h3 class="modal-title fs-16px text-uppercase mb-0">Chi tiết phiếu khám</h3>`
                        );

                        $('#modal-chi-tiet-phieu-kham').find("#btn-detail-examine").html(`
                            <div class="col-sm-6 col-md-auto">
                                <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Đóng</button>
                            </div>`);

                        $('#modal-chi-tiet-phieu-kham').modal('show');
                    } else {
                        displayErrorToastr(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        }
    } catch (error) {

    }
}

function generateTableMenuSearch(id) {
    try {
        if (dataExamine.menuExamine.length > 0) {
            for (let menu of dataExamine.menuExamine) {
                if (id == menu.id) {
                    $('#menu_example_note').html(menu.note);
                    addTemplateListMenuTimeSearch(menu.detail);
                    break;
                }
            }
        }
    } catch (error) {

    }
}

function addTemplateListMenuTimeSearch(listMenuTime) {
    try {
        // if(listMenuTime.length > 0){
        //     for(let item of listMenuTime){
        //         let menuTime = addTemplateMenuTimeSearch(item);
        //         $("#tb_menu").find('tbody').append(menuTime);
        //         if(item.listFood.length > 0){
        //             for(let food of item.listFood){
        //                 let foodTemplate = addFoodTemplateSearch(food, item.id);
        //                 $("#tb_menu").find('tbody').append(foodTemplate);
        //             }
        //         }
        //     }
        // }
        if (listMenuTime.length > 0) {
            for (let item of listMenuTime) {
                let menuTime = addTemplateMenuTimeSearch2(item);
                $("#list_menu_example").append(menuTime);
                if (item.listFood.length > 0) {
                    for (let food of item.listFood) {
                        let foodTemplate = addFoodTemplateSearch2(food, item.name_course);
                        $("#menu_time_food_" + item.id).append(foodTemplate.food);
                        $("#menu_time_weight_" + item.id).append(foodTemplate.weight);
                    }
                }
            }
        }
    } catch (error) {

    }
}

// function addTemplateMenuTimeSearch(menuTime){
//     try {
//         let rowspan = menuTime.listFood.length + 1;
//         return menuTimeTemplate = $('<tr/>')
//             .attr("id", "menu_time_"+ menuTime.id)
//             .addClass("text-center")
//             .append($("<td/>")
//                 .css({"writing-mode": "vertical-rl"})
//                 .attr("rowspan", rowspan)
//                 .text(menuTime.name)
//             )
//             .append($("<td/>")
//                 .text(menuTime.name_course)
//                 .css({"text-align": "center"})
//                 .attr("colspan", 2)
//             );
//     } catch (error) {

//     }
// }

// function addFoodTemplateSearch(food, menuTime_id){
//     try {
//         return $('<tr/>')
//         .attr("id", "food_"+ menuTime_id + "_" + food.id)
//         .append($("<td/>")
//             .text(food.name)
//             .css({"text-align": "center"})
//         )
//         .append($("<td/>")
//             .attr("id", "food_"+ menuTime_id + "_" + food.id + "_weight")
//             .text(food.weight)
//             .css({"text-align": "center"})
//         );
//     } catch (error) {

//     }
// }

function addTemplateMenuTimeSearch2(menuTime) {
    try {
        return menuTimeTemplate = $('<div/>')
            .attr("id", "menu_time_" + menuTime.id)
            .addClass("row mt-0")
            .append($("<div/>")
                .addClass("col-2 px-2 py-2")
                .css({ "writing-mode": "vertical-rl", "display": "flex", "justify-content": "center", "align-items": "center" })
                .text(menuTime.name)
            )
            .append($("<div/>")
                .attr("id", "menu_time_food_" + menuTime.id)
                .addClass("col-7 text-center")
            )
            .append($("<div/>")
                .attr("id", "menu_time_weight_" + menuTime.id)
                .addClass("col-3")
            );
    } catch (error) {

    }
}

function addFoodTemplateSearch2(food, name_course) {
    try {
        let temp_food = $('<div/>')
            .text(food.name)
            .addClass("px-2 py-2");
        let weight = $('<div/>')
            .text(food.weight)
            .addClass("px-2 py-2");
        return { food: temp_food, weight: weight };
    } catch (error) {

    }
}

function showHistory() {
    try {
        let customer_id = parseInt($('#phone_search').val());
        if (customer_id) {
            let loading = $("#loading-page");
            let url = '/examine/table/history';
            $.ajax({
                type: 'POST',
                url: url,
                data: { cus_id: customer_id },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success && result.data) {
                        $('#table_history').html(result.data);
                    } else {
                        displayErrorToastr(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        } else {
            displayErrorToastr("Vui lòng nhập số điện thoại tìm kiếm");
        }
    } catch (error) {

    }
}

function showConfirmSaveMenu() {
    try {
        $('#modal-cf-save-menu').modal('show');
    } catch (error) {

    }
}

function encodeImageFileAsURL() {

    var filesSelected = document.getElementById("inputFileToLoad").files;
    if (filesSelected.length > 0) {
        var fileToLoad = filesSelected[0];

        var fileReader = new FileReader();

        fileReader.onload = function (fileLoadedEvent) {
            var srcData = fileLoadedEvent.target.result; // <--- data: base64
            $('#avatar').val(srcData);
            $('#imgTest').attr('src', srcData);
        }
        fileReader.readAsDataURL(fileToLoad);
    }
}

function cancelExamine(id, status) {
    try {
        if (id) {
            let loading = $("#loading-page");
            let url = '/examine/cancel';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id, status: status },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success) {
                        displayMessageToastr(result.message);
                        if (status && status == 4) {
                            $('#examine_' + id).remove();
                        } else {
                            window.location.href = '/examine';
                        }
                    } else {
                        displayErrorToastr(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        }
    } catch (error) {

    }
}

function cancelConsultation(id) {
    try {
        if (id) {
            let loading = $("#loading-page");
            let url = '/consultation/cancel';
            $.ajax({
                type: 'POST',
                url: url,
                data: { id: id },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success) {
                        displayMessageToastr(result.message);
                        window.location.href = '/consultation';
                    } else {
                        displayErrorToastr(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        }
    } catch (error) {

    }
}

function showModalCancelExamine(id, status) {
    try {
        var confirmBox = `
        <div class="modal fade" id="modal_cf_cancel_examine" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="text-center mb-2">
                <svg class="iconsvg-trash-lg text-tra-lai fs-65px">
                    <use xlink:href="/public/content/images/sprite.svg#trash-lg"></use>
                </svg>
                </div>
                <h4 class="modal-title text-center text-tra-lai mb-4">Huỷ phiếu khám</h4>
                <p class="text-body-2 fw-5 text-center mb-4">Bạn muốn hủy phiếu khám này không?</p>
                <div class="row g-2 justify-content-center">
                <div class="col-6">
                    <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
                </div>
                <div class="col-6">
                    <button onclick="cancelExamine(`+ id + `,` + status + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                    <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                    </svg>
                    Đồng ý
                    </button>
                </div>
                </div>
            </div>
            </div>
        </div>`;
        $("#modal_confirm_box").html(confirmBox);
        $("#modal_cf_cancel_examine").modal('show');
    } catch (error) {

    }
}

function showModalCancelConsultation(id) {
    try {
        var confirmBox = `
        <div class="modal fade" id="modal_cf_cancel_consultation" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="text-center mb-2">
                <svg class="iconsvg-trash-lg text-tra-lai fs-65px">
                    <use xlink:href="/public/content/images/sprite.svg#trash-lg"></use>
                </svg>
                </div>
                <h4 class="modal-title text-center text-tra-lai mb-4">Huỷ phiếu hội chẩn</h4>
                <p class="text-body-2 fw-5 text-center mb-4">Bạn muốn hủy phiếu hội chẩn này không?</p>
                <div class="row g-2 justify-content-center">
                <div class="col-6">
                    <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
                </div>
                <div class="col-6">
                    <button onclick="cancelConsultation(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                    <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                    </svg>
                    Đồng ý
                    </button>
                </div>
                </div>
            </div>
            </div>
        </div>`;
        $("#modal_confirm_box").html(confirmBox);
        $("#modal_cf_cancel_consultation").modal('show');
    } catch (error) {

    }
}

function showModalDeleteMenuExample(val) {
    try {
        var confirmBox = `
        <div class="modal fade" id="modal_cf_delete_example" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="text-center mb-2">
                <svg class="iconsvg-trash-lg text-tra-lai fs-65px">
                    <use xlink:href="/public/content/images/sprite.svg#trash-lg"></use>
                </svg>
                </div>
                <h4 class="modal-title text-center text-tra-lai mb-4">Bỏ thực đơn mẫu</h4>
                <p class="text-body-2 fw-5 text-center mb-4">Bạn muốn bỏ thực đơn mẫu này khỏi phiếu khám không?</p>
                <div class="row g-2 justify-content-center">
                <div class="col-6">
                    <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
                </div>
                <div class="col-6">
                    <button onclick="deleteMenuExample(`+ val + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                    <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                        <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                    </svg>
                    Đồng ý
                    </button>
                </div>
                </div>
            </div>
            </div>
        </div>`;
        $("#modal_confirm_box").html(confirmBox);
        $("#modal_cf_delete_example").modal('show');
    } catch (error) {

    }
}

function deleteMenuExample(id) {
    try {
        // Xóa thực đơn khỏi list
        removeItemArrayByIdObject(dataExamine.menuExamine, id);
        // Xóa menu template
        if ($('#menu_id').find("option[value='" + id + "']").length) {
            $('#menu_id').find("option[value='" + id + "']").remove();
        }
        // nếu có menu trong danh sách thì thêm vào template
        if (dataExamine.menuExamine.length > 0) {
            $('#tb_menu tbody').empty();
            $('#tb_menu').show();
            generateTableMenu(dataExamine.menuExamine[0].id);
        } else {
            $('#tb_menu tbody').empty();
            $('#tb_menu').hide();
            $('#menu_id').trigger('change');
        }
    } catch (error) {

    }
}

function getDataSort(isAscending, field, type, page = 0) {
    try {
        //type 1 examine 2 consultation

        // 1 Ascending
        // 0 Descending
        let currentState = $('#td_' + field + ' > div').hasClass('ascending') ? 1 : ($('#td_' + field + ' > div').hasClass('descending') ? 0 : -1);
        //reset data Sort
        if (page == 0) resetDataFilterSort(field);
        let status_sort = '';
        // Nếu sắp xếp giảm dần
        if (isAscending == 0) {
            if (currentState !== 0) {
                // Thêm class descending
                $('#td_' + field + ' > div').addClass('descending');
                status_sort = '0';
            }
        } else if (isAscending == 1) {
            // Nếu sắp xếp tăng dần
            if (currentState !== 1) {
                // Thêm class ascending
                $('#td_' + field + ' > div').addClass('ascending');
                status_sort = '1';
            }
        }

        dataFilter['sort_' + field] = status_sort;
        if (page !== 0) {
            dataFilter['page'] = page;
        } else {
            dataFilter['page'] = 1;
        }
        getListSortSearch(type)
    } catch (error) {

    }
}

let timeOutOnchange = 0;
function getDataSearch(type, field, dateStr = '') {
    try {
        let checkChange = false;
        let val = $('#search_' + field).val();
        if (['time_examine', 'time_consultation'].includes(field)) {
            val = dateStr;
        }
        if (dataFilter['search_' + field] !== val.trim()) {
            dataFilter['search_' + field] = val.trim();
            checkChange = true;
        }
        if (checkChange) {
            dataFilter['page'] = 1;
            if (timeOutOnchange !== 0) {
                clearTimeout(timeOutOnchange);
            }
            timeOutOnchange = setTimeout(() => {
                getListSortSearch(type);
            }, 1000);
        }
    } catch (error) {

    }
}

function getListSortSearch(type) {
    try {
        timeOutOnchange = 0;
        let loading = $("#loading-page");
        let url = '';
        switch (type) {
            case 1: url = "/examine/list-html"; break;
            case 2: url = "/consultation/list-html"; break;
            default: break
        }
        // Lấy từ khóa tìm kiếm và param url
        let keyword = $('#sfilter_keyword').val();
        const urlParams = new URLSearchParams(window.location.search);
        let hospital_ids = urlParams.get('hospital_ids');
        if (hospital_ids) dataFilter['hospital_ids'] = hospital_ids;
        if (keyword) dataFilter['keyword'] = keyword;
        $.ajax({
            type: "GET",
            data: dataFilter,
            url: url,
            success: function (result) {
                if (result.success == true) {
                    // set template table
                    switch (type) {
                        case 1: $('#table-tbody-examine').html(result.table_html); break;
                        case 2: $('#table-tbody-consultation').html(result.table_html); break;
                        default: break;
                    }
                    // set template paginate
                    $('.page-main > .container > .box > .box-header > .paginate').html(result.paginator_html);
                    $('.page-main > .container > .box > .box-body > .paginate').html(result.paginator_html);
                    $('.page-main > .container > .box > .box-header > .box-title > small').html('(' + result.totalItem + ')');

                } else {
                    displayErrorToastr(result.message, result);

                }
            },
            error: function (jqXHR, exception) {
                loading.hide();
                ajax_call_error(jqXHR, exception);
            }
        });
    } catch (error) {

    }
}

function deleteAllTagCheckBoxSearch(field) {
    if (field && field.length > 0) {
        //Xóa html
        $('#' + field + '_dropdown > .tag_selected').removeClass('tag');
        $('#' + field + '_dropdown > .tag_selected > .tag-body').empty();
        //Ẩn nút xóa
        $('#' + field + '_dropdown_clear_all').css('visibility', 'hidden');
        if (dataFilter[field + '_ids'].length > 0) {
            for (let item of dataFilter[field + '_ids']) {
                // Bỏ check all dropdown
                $('#' + field + '_check_' + item).prop('checked', false);
            }
        }
        dataFilter[field + '_ids'] = [];
        dataFilter[field + '_ids'].length = 0;
    }
    getListSortSearch(1);
}

function getValueSearchCheckBox(id, field, text_check_box, type) {
    if (id) {
        var isChecked = $(id).is(':checked'),
            val = isNaN(parseInt($(id).val())) ? 0 : parseInt($(id).val()),
            fieldObj = field + '_ids';

        $('#' + field + '_dropdown > .tag_selected').addClass('tag');
        if (isChecked) {
            //Tích checkbox
            if (!dataFilter[fieldObj].includes(val)) {
                dataFilter[fieldObj].push(val);
                switch (field) {
                    case 'status_examine':
                        $('#' + field + '_dropdown > .tag_selected > .tag-body').append(
                            $(text_check_box)
                                .attr('id', field + '_span_' + val)
                                .append(
                                    $('<button/>')
                                        .addClass('btn tag-item-close btn-link p-0 ms-2 flex-grow-1')
                                        .attr('type', 'button')
                                        .data("field", field)
                                        .data('valField', val)
                                        .click(function () {
                                            let nameField = $(this).data('field');
                                            let valField = $(this).data('valField');
                                            deleteTagItemDropdown(nameField, valField);
                                        })
                                        .append(
                                            $(`<svg class="iconsvg-close">
                                    <use xlink:href="/public/content/images/sprite.svg#close"></use>
                                  </svg>`)
                                        )
                                )
                        );
                        break;
                    default: break;
                }

                if (dataFilter[fieldObj].length > 0) {
                    $('#' + field + '_dropdown_clear_all').css('visibility', 'visible');
                }
            };
        } else {
            //Bỏ tích
            if (dataFilter[fieldObj].includes(val)) {
                removeItemArray(dataFilter[fieldObj], val);
                $('#' + field + '_span_' + val).remove();
                if (dataFilter[fieldObj].length == 0) {
                    $('#' + field + '_dropdown > .tag_selected').removeClass('tag');
                    $('#' + field + '_dropdown_clear_all').css('visibility', 'hidden');
                }
            };
        }
        dataFilter['page'] = 1;

        if (timeOutOnchange !== 0) {
            clearTimeout(timeOutOnchange);
        }
        timeOutOnchange = setTimeout(() => {
            getListSortSearch(type);
        }, 1000);
    }
}

function resetDataFilterSort() {
    dataFilter.sort_id_count = '';
    dataFilter.sort_cus_phone = '';
    dataFilter.sort_time_examine = '';
    $('td > div').removeClass('descending');
    $('td > div').removeClass('ascending');
}

function deleteTagItemDropdown(nameField, itemId) {
    $('#' + nameField + '_span_' + itemId).remove();
    $('#' + nameField + '_check_' + itemId).prop('checked', false);
    if (dataFilter[nameField + '_ids'].includes(itemId)) {
        removeItemArray(dataFilter[nameField + '_ids'], itemId);
    }
    if (dataFilter[nameField + '_ids'].length == 0) {
        //Ẩn nút xóa
        $('#' + nameField + '_dropdown_clear_all').css('visibility', 'hidden');
    }
    if (timeOutOnchange !== 0) {
        clearTimeout(timeOutOnchange);
    }
    timeOutOnchange = setTimeout(() => {
        getListSortSearch(1);
    }, 1000);
}

function expandTextarea() {
    $('textarea').each(function () {
        if (this.scrollHeight !== 0) {
            this.setAttribute('style', 'height:' + (this.scrollHeight + 8) + 'px;overflow-y:hidden;');
        } else {
            this.setAttribute('style', 'height:auto;');
        }
    }).on('input', function () {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight + 8) + 'px';
    });
}

$(document).ready(function () {
    let cus_birthday = $("#cus_birthday").flatpickr({
        dateFormat: "d-m-Y",
        maxDate: "today",
        allowInput: true,
        onChange: function (selectedDates, dateStr, instance) {
            if (dataExamine.isChangeInputAge == false) {
                caculateYearOld(selectedDates, dateStr, instance);
            }
            checkStandardWeightHeight();
        },
        onReady: function (selectedDates, dateStr, instance) {
            caculateYearOld(selectedDates, dateStr, instance);
            checkStandardWeightHeight();
        }
    });

    $('#cus_age').on('input', function (evt) {
        dataExamine.isChangeInputAge = true;
        let type_year_old = $('label[for="cus_age"]').attr('data-type');
        let val = evt.target.value;
        if (val == 0) {
            evt.target.value = 1;
            val = 1;
        }
        switch (type_year_old) {
            case '0': cus_birthday.setDate(moment().subtract(val, 'months').format("DD-MM-YYYY"), true);
                break;
            case '1': cus_birthday.setDate(moment().subtract(val, 'years').format("DD-MM-YYYY"), true);
                break;
            default: break;
        }
        setTimeout(() => {
            dataExamine.isChangeInputAge = false
        }, 1000)
    })

    if (document.querySelector("#search_time_examine")) {
        const search_time_art = document.querySelector("#search_time_examine")._flatpickr;
        search_time_art.config.onChange.push(function (selectedDates, dateStr, instance) {
            getDataSearch(1, 'time_examine', dateStr)
        });
    }
    if (document.querySelector("#search_time_consultation")) {
        const search_time_consultation = document.querySelector("#search_time_consultation")._flatpickr;
        search_time_consultation.config.onChange.push(function (selectedDates, dateStr, instance) {
            getDataSearch(2, 'time_consultation', dateStr)
        });
    }

    $("#nutrition_advice_id").on('select2:select', function (evt) {
        if (dataExamine.nutritionAdviceList.length > 0) {
            for (let item of dataExamine.nutritionAdviceList) {
                if (evt.params.data.id == item.id) {
                    $('#nutrition_advice textarea').each(function () {
                        this.setAttribute('style', 'height:auto;');
                    });
                    $("#glucid_should_use").text(item.glucid_should_use);
                    $("#glucid_limited_use").text(item.glucid_limited_use);
                    $("#glucid_should_not_use").text(item.glucid_should_not_use);

                    $("#protein_should_use").text(item.protein_should_use);
                    $("#protein_limited_use").text(item.protein_limited_use);
                    $("#protein_should_not_use").text(item.protein_should_not_use);

                    $("#lipid_should_use").text(item.lipid_should_use);
                    $("#lipid_limited_use").text(item.lipid_limited_use);
                    $("#lipid_should_not_use").text(item.lipid_should_not_use);

                    $("#vitamin_ck_should_use").text(item.vitamin_ck_should_use);
                    $("#vitamin_ck_limited_use").text(item.vitamin_ck_limited_use);
                    $("#vitamin_ck_should_not_use").text(item.vitamin_ck_should_not_use);

                    $('#nutrition_advice textarea').each(function () {
                        this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
                    });
                    break;
                }
            }
            expandTextarea();
        }
    });

    $("#active_mode_of_living_id").on('select2:select', function (evt) {
        if (dataExamine.activeModeOfLivingList.length > 0) {
            for (let item of dataExamine.activeModeOfLivingList) {
                if (evt.params.data.id == item.id) {
                    $("#active_mode_of_living").text(item.detail);
                    expandTextarea();
                    break;
                }
            }
        }
    });

    $("#medicine_type_id").on('change', function (evt) {
        let id = $("#medicine_type_id").val() ? $("#medicine_type_id").val() : '';
        if (!id) {
            displayErrorToastr('Thiếu Id phân loại');
            return
        }
        $('#medicine_id').html('');
        //get data medicine
        $.ajax({
            type: "GET",
            data: { type: id, is_treatment: 0 },
            url: "/examine/get-list-medicine",
            success: function (result) {
                if (result.success) {
                    if (result.data && result.data.length > 0) {
                        for (let [index, item] of result.data.entries()) {
                            $('#medicine_id').prepend(new Option(item.name, item.id, false, false));
                        }
                        $('#medicine_id').val('').trigger('change');
                        dataExamine.medicineList = result.data;
                    }
                }
            },
            error: function (jqXHR, exception) {

            }
        })
    });

    $("#type_medicine_select").on('change', function (evt) {
        let id = $("#type_medicine_select").val() ? $("#type_medicine_select").val() : '';
        if (!id) {
            displayErrorToastr('Thiếu Id phân loại');
            return
        }
        $('#medicine_select').html('');
        //get data medicine
        $.ajax({
            type: "GET",
            data: { type: id, is_treatment: 1},
            url: "/examine/get-list-medicine",
            success: function (result) {
                if (result.success) {
                    if (result.data && result.data.length > 0) {
                        for (let [index, item] of result.data.entries()) {
                            $('#medicine_select').prepend(new Option(item.name, item.id, false, false));
                        }
                        $('#medicine_select').val('').trigger('change');
                    }
                }
            },
            error: function (jqXHR, exception) {

            }
        })
    });

    $("#medicine_id").on('select2:select', function (evt) {
        if (dataExamine.medicineList.length > 0) {
            for (let item of dataExamine.medicineList) {
                if (evt.params.data.id == item.id) {
                    $("#unit_medinice").val(item.unit);
                    $("#use_medinice").val(item.description);
                    break;
                }
            }
        }
    });

    $("#food_name").on('select2:select', function (evt) {
        if (dataExamine.foodNameListSearch.length > 0) {
            for (let item of dataExamine.foodNameListSearch) {
                if (evt.params.data.id == item.id) {
                    $("#weight_food").val(item.weight);
                    $("#weight_food").data('initial-value', item.weight);
                    $("#energy_food").val(item.energy);
                    $("#protein_food").val(item.protein);
                    $("#animal_protein").val(item.animal_protein);
                    $("#lipid_food").val(item.lipid);
                    $("#unanimal_lipid").val(item.unanimal_lipid);
                    $("#carbohydrate").val(item.carbohydrate);
                    break;
                }
            }
        }
    });

    $("#menu_id").on('select2:select', function (evt) {
        $("tbody tr").remove();
        generateTableMenu(evt.params.data.id);
    }).on('select2:unselecting', function (e) {
        e.preventDefault();
        $('#menu_id').select2('close');
        let id_menu_example = parseInt(e.params.args.data.id);
        showModalDeleteMenuExample(id_menu_example);
    });

    $("#diagnostic_id").on('select2:select', function (evt) {
        if (dataExamine.diagnostic.length > 0) {
            for (let item of dataExamine.diagnostic) {
                if (evt.params.data.id == item.id) {
                    $("#diagnostic_suggest").text(item.detail);
                    expandTextarea();
                    break;
                }
            }
        }
    });

    $('#phone_search').select2({
        minimumInputLength: 3,
        language: {
            inputTooShort: function () {
                return "Vui lòng nhập ít nhất 3 ký tự";
            },
            noResults: function () {
                return "Không có kết quả được tìm thấy";
            },
            searching: function () {
                return "Đang tìm kiếm...";
            }
        },
        escapeMarkup: function (markup) {
            return markup;
        },
        placeholder: 'Nhập số điện thoại',
        allowClear: true,
        ajax: {
            url: function (params) {
                return '/examine/suggest/phone'
            },
            delay: 1000,
            dataType: 'json',
            processResults: function (data) {
                if (data.success) {
                    dataExamine.phoneListSearch = data.data;
                }
                return {
                    results: $.map(data.data, function (item) {
                        return {
                            text: item.cus_name + " - " + new Date(item.cus_birthday).toLocaleDateString('pt-PT') + " - " + (item.cus_gender == 0 ? "Nữ" : (item.cus_gender == 1 ? "Nam" : "Khác") + " - " + item.cus_address),
                            id: item.id
                        }
                    })
                };
            }
        }
    });

    $("#phone_search").on('select2:select', function (evt) {
        if (evt.params.data.id) $('#btn_show_history').show();
        if (dataExamine.phoneListSearch.length > 0) {
            for (let item of dataExamine.phoneListSearch) {
                if (evt.params.data.id == item.id) {
                    $('#cus_name').val(item.cus_name);
                    $('#cus_phone').val(item.cus_phone);
                    $('#cus_email').val(item.cus_email);
                    $('#cus_gender').val(item.cus_gender).trigger('change');
                    cus_birthday.setDate(moment(item.cus_birthday).format("DD-MM-YYYY"), true);
                    $('#cus_address').val(item.cus_address);
                    break;
                }
            }
        }
    });

    $('#weight_food').change(function (evt) {
        let menu_id = parseInt($('#menu_id').val());
        let food_id = parseInt($('#food_name').val());
        if (menu_id && food_id) {
            let oldValue = parseInt($("#weight_food").data('initial-value'));
            let currenValue = parseInt($("#weight_food").val());
            let energy = parseFloat($("#energy_food").val());
            let protein = parseFloat($("#protein_food").val());
            let animal_protein = parseFloat($("#animal_protein").val());
            let lipid = parseFloat($("#lipid_food").val());
            let unanimal_lipid = parseFloat($("#unanimal_lipid").val());
            let carbohydrate = parseFloat($("#carbohydrate").val());

            $("#energy_food").val(Math.round((energy * currenValue) / oldValue));
            $("#protein_food").val(Math.round(((protein * currenValue) / oldValue) * 100) / 100);
            $("#animal_protein").val(Math.round(((animal_protein * currenValue) / oldValue) * 100) / 100);
            $("#lipid_food").val(Math.round(((lipid * currenValue) / oldValue) * 100) / 100);
            $("#unanimal_lipid").val(Math.round(((unanimal_lipid * currenValue) / oldValue) * 100) / 100);
            $("#carbohydrate").val(Math.round(((carbohydrate * currenValue) / oldValue) * 100) / 100);
            $("#weight_food").data('initial-value', currenValue);
        }
    });

    $('#cus_gender').on('select2:select', function (evt) {
        checkStandardWeightHeight();
    });

    $('#cus_length').change(function (evt) {
        // let year_old = $('#cus_age').val();
        // let type_year_old = $('label[for="cus_age"]').text() == 'Tuổi' ? 1 : 0;
        // if(type_year_old == 1 && parseInt(year_old) > 18){
        checkStandardWeightHeight();
        // }
    });

    $('#cus_length, #cus_cnht').change(function (evt) {
        caculateBMI();
    });

    $('#medical_test_type').change(function (evt) {
        let status = parseInt($('#status_examine').val());
        let isLockInput = (status == 4 || (status == 3 && dataExamine.isDetail == 'true')) ? true : false;
        let id = $('#medical_test_type').val();
        if (!isNaN(parseInt(id))) {
            let loading = $("#loading-page");
            let url = '/examine/list/medical-test';
            $.ajax({
                type: 'POST',
                url: url,
                data: { type_id: id, type_name: $('#medical_test_type').find(':selected').text(), data: JSON.stringify(dataExamine.medicalTest), isLockInput: isLockInput },
                beforeSend: function () {
                    loading.show();
                },
                success: function (result) {
                    loading.hide();
                    if (result.success && result.data) {
                        $('#list_medical_test').html(result.data);
                    } else {
                        displayErrorToastr(result.message);
                    }
                },
                error: function (jqXHR, exception) {
                    loading.hide();
                    ajax_call_error(jqXHR, exception);
                }
            });
        } else {
            displayErrorToastr("Không có id loại xét nghiệm!");
        }
    });

    expandTextarea();

    $('#menu_example_note').change(function (evt) {
        let menu_id = parseInt($('#menu_id').val());
        for (let menu of dataExamine.menuExamine) {
            if (menu_id == menu.id) {
                menu.note = $('#menu_example_note').val().trim();
            }
        }
    });
    // Input chỉ nhận số dấu chấm dấu gạch ngang
    $('input[data-type="number"]').on('input', evt => {
        let value = evt.target.value;
        let value2 = value.replace(/[^0-9\.\s\/\-]/g, '');
        evt.target.value = value2;
    });
});

//Add table ClinicalExam

function addClinicalExam() {
    let date = $('#kham_lam_sang_ngay').val();
    console.log('addClinicalExam', date);
    if (!date) {
        displayError('Vui lòng chọn ngày!');
        return;
    }
    let content = $('#kham_lam_sang_noi_dung').val();
    let id = dataConsultation.clinicalExam.length == 0 ? 1 : dataConsultation.clinicalExam[(dataConsultation.clinicalExam.length - 1)].id + 1;
    let clinicalExamItem = {
        id: id,
        date: date,
        content: content
    }
    dataConsultation.clinicalExam.push(clinicalExamItem);

    if (dataConsultation.clinicalExam.length > 0) {
        $("#tb_kham_lam_sang").show();
    } else {
        $("#tb_kham_lam_sang").hide();
    }
    addClinicalExamHtml(clinicalExamItem);
    $('#modal_kham_lam_sang').modal('hide');
}

function addClinicalExamHtml(clinicalExamItem) {
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr('id', 'tr_ce_' + clinicalExamItem.id).addClass("fs-6");

    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    $(td1).addClass("w-08 py-1 text-primary cursor-pointer")
        .data("index", clinicalExamItem.id)
        .text(clinicalExamItem.date)
        .on("click", function(evt){
            let index = $(this).data('index');
            let text = $(this).text();
            showModalChangeDate('1', index, text);
        });

    $(td2).addClass("min-w-150px py-1")
        .append($("<textarea>")
            .attr({ "readonly": isLockInput })
            .addClass("form-control py-2 px-3")
            .text(clinicalExamItem.content)
            .data("clinical_exam_id", clinicalExamItem.id)
            .change(function () {
                console.log('changeClinicalExam', $(this).data('clinical_exam_id'), $(this).val());
                let id = $(this).data('clinical_exam_id');
                let value = $(this).val();
                changeClinicalExam(id, value, 'content');
            })
        );

    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px'});

    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);

    button.appendChild(svgElem);

    button.dataset.id = clinicalExamItem.id;
    button.dataset.name = clinicalExamItem.date;

    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteClinicalExam(id, name);
    });

    div.append(button);
    $(td3).addClass("py-1").append(div);
    tr.append(td1);
    tr.append(td2);
    tr.append(td3);

    if (isLockInput) {
        $('#btn_modal_kham_lam_sang').hide();
    }
    $('#tb_kham_lam_sang table tbody').append(tr);
    expandTextarea();
}

function changeClinicalExam(id, value, type) {
    if (id && value) {
        for (let item of dataConsultation.clinicalExam) {
            console.log('changeClinicalExam 1', item.id, id, item.id == id);
            if (item.id == id) {
                item[type] = value;
            }
        }
        console.log('changeClinicalExam', id, value, type);
    }
}

function showConfirmDeleteClinicalExam(id, name) {
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_clinical_exam" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteClinicalExam(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_clinical_exam").modal('show');
}

function deleteClinicalExam(id) {
    if (id) {
        $('#tr_ce_' + id).remove();
        removeItemArrayByIdObject(dataConsultation.clinicalExam, id);
    }
}

// Add tabel Nutrition Tracking

function addNutritionTracking() {
    let date = $('#theo_doi_dinh_duong_ngay').val();
    if (!date) {
        displayError('Vui lòng chọn ngày!');
        return;
    }
    let content = $('#theo_doi_dinh_duong_noi_dung').val();
    let id = dataConsultation.nutritionTracking.length == 0 ? 1 : dataConsultation.nutritionTracking[(dataConsultation.nutritionTracking.length - 1)].id + 1;
    let nutritionTrackingItem = {
        id: id,
        date: date,
        content: content
    }
    dataConsultation.nutritionTracking.push(nutritionTrackingItem);

    if (dataConsultation.nutritionTracking.length > 0) {
        $("#tb_theo_doi_dinh_duong").show();
    } else {
        $("#tb_theo_doi_dinh_duong").hide();
    }
    addNutritionTrackingHtml(nutritionTrackingItem);
    $('#modal_theo_doi_dinh_duong').modal('hide');
}

function addNutritionTrackingHtml(nutritionTrackingItem) {
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr('id', 'tr_nt_' + nutritionTrackingItem.id).addClass("fs-6");

    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    $(td1).addClass("w-08 py-1 text-primary cursor-pointer")
        .text(nutritionTrackingItem.date)
        .data("index", nutritionTrackingItem.id)
        .on("click", function(evt){
            let index = $(this).data('index');
            let text = $(this).text();
            showModalChangeDate('2', index, text);
        });

    $(td2).addClass("min-w-150px py-1")
        .append($("<textarea>")
            .attr({ "readonly": isLockInput })
            .addClass("form-control py-2 px-3")
            .text(nutritionTrackingItem.content)
            .data("nutrition_tracking_id", nutritionTrackingItem.id)
            .change(function () {
                let id = $(this).data('nutrition_tracking_id');
                let value = $(this).val();
                changeNutritionTracking(id, value, 'content');
            })
        );

    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });

    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);

    button.appendChild(svgElem);

    button.dataset.id = nutritionTrackingItem.id;
    button.dataset.name = nutritionTrackingItem.date;

    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteNutritionTracking(id, name);
    });

    div.append(button);
    $(td3).addClass("py-1").append(div);
    tr.append(td1);
    tr.append(td2);
    tr.append(td3);

    if (isLockInput) {
        $('#btn_modal_theo_doi_dinh_duong').hide();
    }
    $('#tb_theo_doi_dinh_duong table tbody').append(tr);
    expandTextarea();
}

function changeNutritionTracking(id, value, type) {
    if (id && value) {
        console.log('changeNutritionTracking', id, value, type);
        for (let item of dataConsultation.nutritionTracking) {
            if (item.id == id) {
                item[type] = value;
            }
        }
    }
}

function showConfirmDeleteNutritionTracking(id, name) {
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_nutrition_tracking" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteNutritionTracking(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_nutrition_tracking").modal('show');
}

function deleteNutritionTracking(id) {
    if (id) {
        $('#tr_nt_' + id).remove();
        removeItemArrayByIdObject(dataConsultation.nutritionTracking, id);
    }
}

// Cận lâm sàng
function openModalAnalysis(){
    $('#analysis').val('').trigger('change');
    $('#modal_can_lam_sang').modal('show');
}
function addAnalysis(){
    console.log('addAnalysis', $('#analysis').val());
    let ids = $('#analysis').val();
    if(ids.length > 0){
        for(let item of ids){
            addAnalysisItem(parseInt(item));
        }
    }
    $('#modal_can_lam_sang').modal('hide');
}
function addAnalysisItem(id){
    let name = $('#analysis option[value="' + id +'"]').text();
    let reference = $('#analysis option[value="' + id +'"]').data('reference');
    let max = $('#analysis option[value="' + id +'"]').data('max');
    let min = $('#analysis option[value="' + id +'"]').data('min');
    let index = dataConsultation.subclinical.data.findIndex(s => s.id == id);
    if(index == -1){
        let item = {
            id: id,
            name: name,
            reference: reference,
            max: parseFloat(max),
            min: parseFloat(min),
            values: []
        }
        console.log('dataConsultation.subclinical.date', dataConsultation.subclinical.date);
        if(dataConsultation.subclinical.date.length > 0){
            for (let dateObj of dataConsultation.subclinical.date) {
                item.values.push({ dateId: dateObj.id, value: '' });
            }
        }
        
        dataConsultation.subclinical.data.push(item);
        addAnalysisHtml(item);
    }else{
        displayMessageToastr('Xét nghiệm đã tồn tại');
        return
    }
}
function addAnalysisHtml(analysisItem){
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr({'id': 'tr_as_' + analysisItem.id, 'data-analysis_id': analysisItem.id, 'data-max': analysisItem.max, 'data-min': analysisItem.min}).addClass("fs-6 tr_can_lam_sang");
    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    $(td1).addClass("py-1 text-primary").text(analysisItem.name);
    $(td2).addClass("py-1").text(analysisItem.reference);
    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });
    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);
    button.appendChild(svgElem);
    button.dataset.id = analysisItem.id;
    button.dataset.name = analysisItem.name;
    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteAnalysis(id, name);
    });
    div.append(button);
    $(td3).addClass("py-1 btn_delete_analysis").append(div);
    tr.append(td1);
    tr.append(td2);
    // Render theo dateId
    for (let dateObj of dataConsultation.subclinical.date) {
        let valueObj = analysisItem.values.find(v => v.dateId === dateObj.id);
        tr.append(
            addInputAnalysisHtml(analysisItem.id, dateObj.id, valueObj ? valueObj.value : '', isLockInput, analysisItem.max, analysisItem.min)
        );
    }
    tr.append(td3);
    if (isLockInput) {
        $('#btn_modal_can_lam_sang').hide();
        $('#td_btn_add_date_subclinical button').hide();
    }
    $('#tb_can_lam_sang table tbody').append(tr);
}

function showConfirmDeleteAnalysis(id, name){
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_analysis" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteAnalysis(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_analysis").modal('show');
}

function deleteAnalysis(id){
    if (id) {
        $('#tr_as_' + id).remove();
        removeItemArrayByIdObject(dataConsultation.subclinical.data, parseInt(id));
    }
}

function changeAnalysisDate(id, value, date_id){
    console.log('changeAnalysisDate 2', id, value, date_id);
    setSubclinicalValue(id, date_id, value);
}

/**
 * Helper function to process analysis names for searching.
 * Removes text in parentheses, the word "máu", trims, and converts to lowercase.
 * @param {string} name The analysis name to process.
 * @returns {string} The processed name.
 */
function processAnalysisNameForSearch(name) {
    if (typeof name !== 'string') return '';
    let processed = name;

    const parenthesisIndex = processed.indexOf('(');
    if (parenthesisIndex !== -1) {
        processed = processed.substring(0, parenthesisIndex);
    }
    processed = processed.replace(/máu/gi, '');
    return processed.trim().toLowerCase();
}

function addAnalysisDate() {
    let date = $('#can_lam_sang_ngay').val();
    if (!date) {
        displayMessageToastr('Vui lòng chọn ngày');
        return;
    }
    // Tạo id duy nhất cho ngày
    const dateId = generateDateId('subclinical');
    if (dataConsultation.subclinical.date.find(d => d.date === date)) {
        displayMessageToastr('Đã tồn tại ngày ' + date);
        return;
    }
    dataConsultation.subclinical.date.push({ id: dateId, date: date});
    for (let item of dataConsultation.subclinical.data) {
        item.values.push({ dateId, value: '' });
    }
    addAnalysisDateHtml(date, dateId);
    $('#modal_can_lam_sang_them_ngay').modal('hide');
}

function addAnalysisDateHtml(date, dateId){
    $("#td_btn_add_date_subclinical").before(
        $("<td>").addClass("w-08 text-center h-02 p-0 cursor-pointer")
            .text(date)
            .attr({ 'data-date_id': dateId })
            .on("click", function(evt){
                let clickedDateId = $(this).data('date_id');
                let text = $(this).text();
                showModalChangeDate('4', clickedDateId, text);
            })
    );
    let arrTr = document.querySelectorAll('.tr_can_lam_sang');
    if (arrTr && arrTr.length > 0) {
        for (let trItem of arrTr) {
            let analysis_id = trItem.getAttribute('data-analysis_id');
            let max_val = trItem.getAttribute('data-max');
            let min_val = trItem.getAttribute('data-min');
            let trId = trItem.id;
            const analysisDataItem = dataConsultation.subclinical.data.find(d => d.id == parseInt(analysis_id));
            let valueForInput = '';
            if (analysisDataItem) {
                let v = analysisDataItem.values.find(val => val.dateId === dateId);
                valueForInput = v ? v.value : '';
            }
            $('#' + trId + ' .btn_delete_analysis').before(
                addInputAnalysisHtml(analysis_id, dateId, valueForInput, false, parseFloat(max_val), parseFloat(min_val))
            );
        }
    }
}

function addInputAnalysisHtml(analysis_id, dateId, value, isLockInput, max, min){
    let td = document.createElement("td");
    $(td).addClass("min-w-150px py-1")
        .attr('data-date_id', dateId)
        .append($("<input>")
            .attr({type:"number", value: value, readonly: isLockInput})
            .addClass("form-control form-control-title py-1 ps-2 pe-1")
            .data("analysis_id", analysis_id)
            .data('date_id', dateId)
            .change(function () {
                let id = $(this).data('analysis_id');
                let date_id = $(this).data('date_id');
                let value = $(this).val();
                let val = parseFloat(value);
                if(val >= min && val <= max){}else{
                    if(val < min) $(this).addClass('text-warning fw-bold fs-5');
                    if(val > max) $(this).addClass('text-danger fw-bold fs-5');
                }
                console.log('changeAnalysisDate 1', id, value, date_id);
                changeAnalysisDate(id, value, date_id);
            })
        )
    $(td).find('input').trigger('change');
    return td;
}

// Cận lâm sàng khác

function addAnalysisOrther() {
    let date = $('#can_lam_sang_khac_ngay').val();
    if (!date) {
        displayError('Vui lòng chọn ngày!');
        return;
    }
    let content = $('#can_lam_sang_khac_noi_dung').val();
    let id = dataConsultation.subclinicalOrther.length == 0 ? 1 : dataConsultation.subclinicalOrther[(dataConsultation.subclinicalOrther.length - 1)].id + 1;
    let subclinicalOrtherItem = {
        id: id,
        date: date,
        content: content
    }
    dataConsultation.subclinicalOrther.push(subclinicalOrtherItem);

    if (dataConsultation.subclinicalOrther.length > 0) {
        $("#tb_can_lam_sang_khac").show();
    } else {
        $("#tb_can_lam_sang_khac").hide();
    }
    addAnalysisOrtherHtml(subclinicalOrtherItem);
    $('#modal_can_lam_sang_khac').modal('hide');
}

function addAnalysisOrtherHtml(analysisOrtherItem) {
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr('id', 'tr_aso_' + analysisOrtherItem.id).addClass("fs-6");

    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    $(td1).addClass("w-08 py-1 text-primary cursor-pointer")
        .text(analysisOrtherItem.date)
        .data("index", analysisOrtherItem.id)
        .on("click", function(evt){
            let index = $(this).data('index');
            let text = $(this).text();
            showModalChangeDate('3', index, text);
        });

    $(td2).addClass("min-w-150px py-1")
        .append($("<textarea>")
            .attr({ "readonly": isLockInput })
            .addClass("form-control py-2 px-3")
            .text(analysisOrtherItem.content)
            .data("nutrition_tracking_id", analysisOrtherItem.id)
            .change(function () {
                let id = $(this).data('nutrition_tracking_id');
                let value = $(this).val();
                changeAnalysisOrther(id, value, 'content');
            })
        );

    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });

    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);

    button.appendChild(svgElem);

    button.dataset.id = analysisOrtherItem.id;
    button.dataset.name = analysisOrtherItem.date;

    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteAnalysisOrther(id, name);
    });

    div.append(button);
    $(td3).addClass("py-1").append(div);
    tr.append(td1);
    tr.append(td2);
    tr.append(td3);

    if (isLockInput) {
        $('#btn_modal_can_lam_sang_khac').hide();
    }
    $('#tb_can_lam_sang_khac table tbody').append(tr);
    expandTextarea();
}

function changeAnalysisOrther(id, value, type) {
    if (id && value) {
        console.log('changeAnalysisOrther', id, value, type);
        for (let item of dataConsultation.subclinicalOrther) {
            if (item.id == id) {
                item[type] = value;
            }
        }
    }
}

function showConfirmDeleteAnalysisOrther(id, name) {
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_analysis_orther" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteAnalysisOrther(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_analysis_orther").modal('show');
}

function deleteAnalysisOrther(id) {
    if (id) {
        $('#tr_aso_' + id).remove();
        removeItemArrayByIdObject(dataConsultation.subclinicalOrther, id);
    }
}

// Thuốc

function addMedicineConsultation(){
    let ids = $('#medicine_select').val();
    if (!ids || ids.length === 0) {
        displayErrorToastr('Chưa chọn thuốc!');
        return;
    }
    for (let i = 0; i < ids.length; i++) {
        let id = parseInt(ids[i]);
        let name = $('#medicine_select option[value="' + id +'"]').text();
        let index = dataConsultation.medicine.data.findIndex(s => s.id == id);
        if(index == -1){
            let item = {
                id: id,
                name: name,
                values: []
            }
            for (let dateObj of dataConsultation.medicine.date) {
                item.values.push({ dateId: dateObj.id, value: '' });
            }
            dataConsultation.medicine.data.push(item);
            addMedicineConsultationHtml(item);
        }else{
            displayMessageToastr('Thuốc đã tồn tại');
            return;
        }
    }
    $('#modal_medicine').modal('hide');
}

function addMedicineConsultationHtml(medicineItem){
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr({'id': 'tr_me_' + medicineItem.id, 'data-medicine_id': medicineItem.id}).addClass("fs-6 tr_medicine");
    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    $(td1).addClass("py-1 text-primary").text(medicineItem.name);
    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });
    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);
    button.appendChild(svgElem);
    button.dataset.id = medicineItem.id;
    button.dataset.name = medicineItem.name;
    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteMedicineConsultation(id, name);
    });
    div.append(button);
    $(td2).addClass("py-1 btn_delete_medicine").append(div);
    tr.append(td1);
    // Render theo dateId
    for (let dateObj of dataConsultation.medicine.date) {
        let valueObj = medicineItem.values.find(v => v.dateId === dateObj.id);
        let td = document.createElement("td");
        $(td).addClass("min-w-150px py-1")
            .attr({'data-date_id': dateObj.id})
            .append($("<input>")
                .attr({ 'data-date_id': dateObj.id, "data-medicine_id": medicineItem.id })
                .attr({type:"text", value: valueObj ? valueObj.value : '', readonly: isLockInput })
                .addClass("form-control form-control-title py-1 px-3")
                .change(function () {
                    let id = $(this).data('medicine_id');
                    let date_id = $(this).data('date_id');
                    let value = $(this).val();
                    setMedicineValue(parseInt(id), date_id, value);
                })
            );
        tr.append(td);
    }
    tr.append(td2);
    if (isLockInput) {
        $('#btn_modal_medicine').hide();
        $('#td_btn_add_date_medicine button').hide();
    }
    $('#tb_theo_doi_thuoc table tbody').append(tr);
}

function showConfirmDeleteMedicineConsultation(id, name){
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_medicine" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteMedicineConsultation(`+ id + `)" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_medicine").modal('show');
}

function deleteMedicineConsultation(id){
    if (id) {
        $('#tr_me_' + id).remove();
        removeItemArrayByIdObject(dataConsultation.medicine.data, parseInt(id));
    }
}

function changeMedicineConsultationDate(id, value, date_id){
    setMedicineValue(id, date_id, value);
}

function addMedicineConsultationDate(){
    let date = $('#medicine_date').val();
    if(!date){
        displayMessageToastr('Vui lòng chọn ngày')
        return;
    }
    const dateId = generateDateId('medicine');
    if(dataConsultation.medicine.date.find(d => d.date === date)){
        displayMessageToastr('Đã tồn tại ngày ' + date);
        return;
    }
    dataConsultation.medicine.date.push({ id: dateId, date: date});
    for(let item of dataConsultation.medicine.data){
        item.values.push({ dateId, value: '' });
    }
    addMedicineConsultationDateHtml(date, dateId)
    $('#modal_medicine_add_date').modal('hide');
}

function addMedicineConsultationDateHtml(date, dateId){
    $("#td_btn_add_date_medicine").before(
        $("<td>").addClass("w-08 text-center h-02 p-0 cursor-pointer")
            .text(date)
            .attr({'data-date_id': dateId})
            .on("click", function(evt){
                let dId = $(this).attr('data-date_id');
                let text = $(this).text();
                showModalChangeDate('5', dId, text);
            })
    );
    let arrTr = document.querySelectorAll('.tr_medicine');
    if(arrTr && arrTr.length > 0){
        for(let item of arrTr){
            let medicine_id = item.getAttribute('data-medicine_id');
            let id = item.id;
            let medicineData = dataConsultation.medicine.data.find(m => m.id == parseInt(medicine_id));
            let valueObj = medicineData ? medicineData.values.find(v => v.dateId === dateId) : null;
            $('#' + id + ' .btn_delete_medicine').before(
                $("<td>").addClass("min-w-150px py-1")
                    .attr({'data-date_id': dateId})
                    .append($("<input>")
                        .attr({type:"text", value: valueObj ? valueObj.value : ''})
                        .attr({ 'data-date_id': dateId, "data-medicine_id": medicine_id })
                        .addClass("form-control form-control-title py-1 px-3")
                        .change(function () {
                            let id = $(this).data('medicine_id');
                            let date_id = $(this).data('date_id');
                            let value = $(this).val();
                            setMedicineValue(parseInt(id), date_id, value);
                        })
            ))
        }
    }
}

function setMedicineValue(itemId, dateId, value) {
    let item = dataConsultation.medicine.data.find(i => String(i.id) === String(itemId));
    if (item) {
        let v = item.values.find(val => val.dateId === dateId);
        if (v) v.value = value;
        else item.values.push({ dateId, value });
    }
}

// Bổ xung
function addMedicineConsultationTableModal(){
    let date = $('#bo_xung_ngay').val();
    let medicine_id = parseInt($('#medicine_id').val());
    let medicine_name = $('#medicine_id').find(':selected').text();
    if (!medicine_id) {
        displayErrorToastr('Chưa chọn thuốc!');
        return;
    }
    let medicine_total = $('#total_medinice').val();
    if (!medicine_total || medicine_total == 0 || isNaN(medicine_total)) {
        displayErrorToastr('Thiếu số lượng!');
        return;
    }
    let medicine_unit = $('#unit_medinice').val();
    let medicine_note = $('#use_medinice').val();
    let prescriptionItem = {
        stt: dataExamine.id_prescription++,
        name: medicine_name,
        id: medicine_id,
        total: parseInt(medicine_total),
        unit: medicine_unit,
        note: medicine_note
    }
    let isAddHtml = true;
    if(dataConsultation.additional.length == 0){
        dataConsultation.additional.push({
            id: 1,
            date: date,
            data:[prescriptionItem]
        });
        dataConsultation.additional_id_choose = 1;
    }else{
        if(dataConsultation.additional_id_choose){
            for(let item of dataConsultation.additional){
                if(item.id == dataConsultation.additional_id_choose){
                    if(item.data.length == 0){
                        item.data.push(prescriptionItem);
                    }else{
                        let index = item.data.findIndex(s => s.id == medicine_id);
                        console.log('index', index, medicine_id);
                        if(index == -1){
                            // Nếu chưa tồn tại thuốc
                            item.data.push(prescriptionItem)
                        }else{
                            item.data[index].total = parseInt(item.total) + prescriptionItem.total;
                            item.data[index].note = prescriptionItem.note;
                            isAddHtml = false;
                        }
                    }
                }
            }
        }else{
            dataConsultation.additional_id_choose = dataConsultation.additional[dataConsultation.additional.length - 1].id + 1;
            dataConsultation.additional.push({
                id: dataConsultation.additional_id_choose,
                date: date,
                data:[prescriptionItem]
            });
        }
    }

    $("#tb_prescription").show();
    if(isAddHtml) addHtmlPrescription2(prescriptionItem, true);
    else displayMessageToastr('Thuốc đã tồn tại')
}

function addHtmlPrescription2(prescriptionItem, isModal) {
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tr = document.createElement("tr");
    $(tr).attr('id', 'tr_pr' + (isModal ? 'm_' : '_') + prescriptionItem.id);

    let td1 = document.createElement("td");
    let td2 = document.createElement("td");
    let td3 = document.createElement("td");
    let td4 = document.createElement("td");

    $(td3).addClass("min-w-150px")
        .append($("<input/>")
            .attr({ "type": "text", "value": prescriptionItem.note, "readonly": isLockInput })
            .addClass("form-control form-control-title p-1 fs-13px")
            .data("medicine_id", prescriptionItem.id)
            .change(function () {
                let id = $(this).data('medicine_id');
                let value = $(this).val();
                changeMedicine2(id, value, 'note');
            })
        );

    $(td4).append($("<input/>")
        .attr({ "type": "number", "value": prescriptionItem.total, "readonly": isLockInput })
        .addClass("form-control form-control-title p-1 fs-6 text-red")
        .data("medicine_id", prescriptionItem.id)
        .change(function () {
            let id = $(this).data('medicine_id');
            let value = parseInt($(this).val());
            if (isNaN(value)) {
                displayErrorToastr('Số lượng thuốc không đúng định dạng!')
            } else {
                changeMedicine2(id, value, 'total');
            }
        })
    );

    let td5 = document.createElement("td");
    let td6 = document.createElement("td");

    $(td2).attr({ class: 'min-w-150px fs-6' });
    $(td5).attr({ class: 'fs-13px text-primary' });

    let button = document.createElement("button");
    let div = document.createElement("div");
    $(div).attr({ class: 'flex-center-x gap-10px' });

    var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem).attr({ class: 'iconsvg-trash' });
    useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem.append(useElem);

    button.appendChild(svgElem);

    button.dataset.id = prescriptionItem.id;
    button.dataset.name = prescriptionItem.name;

    $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button).click(function () {
        let id = $(this).data('id');
        let name = $(this).data('name');
        showConfirmDeleteMedicineModal(id, name, isModal);
    });

    $(td1).text(prescriptionItem.stt);
    $(td2).text(prescriptionItem.name);
    $(td5).text(prescriptionItem.unit);

    div.append(button);
    td6.append(div);
    tr.append(td1);
    tr.append(td2);
    tr.append(td3);
    tr.append(td4);
    tr.append(td5);

    if (isLockInput) {
        $('#active_table_medicine').hide();
    } else {
        tr.append(td6);
    }
    $('#tb_prescription table tbody').append(tr);
}

function changeMedicine2(id, value, type) {
    try {
        if(dataConsultation.additional_id_choose && dataConsultation.additional.length > 0){
            for(let item of dataConsultation.additional){
                if(item.id == dataConsultation.additional_id_choose){
                    if(item.data.length > 0){
                        for(let med of item.data){
                            if (med.id == id) {
                                med[type] = value;
                            }
                        }
                        break;
                    }
                    break
                }
            }
        }
    } catch (error) {

    }
}

function showConfirmDeleteMedicineModal(id, name, isModal) {
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_medicine" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteMedicineModal(${id}, ${isModal})" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_medicine").modal('show');
}

function deleteMedicineModal(id, isModal) {
    try {
        if (id) {
            $('#tr_pr' + (isModal ? 'm_' : '_') + id).remove();
            if(dataConsultation.additional_id_choose && dataConsultation.additional.length > 0){
                for(let item of dataConsultation.additional){
                    if(item.id == dataConsultation.additional_id_choose){
                        removeItemArrayByIdObject(item.data, id);
                        break;
                    }
                }
            }
        }
    } catch (error) {

    }
}

function addAdditional(){
    let date = $('#bo_xung_ngay').val();
    console.log(dataConsultation.additional);
    if(dataConsultation.additional_id_choose){
        if (!date) {
            displayErrorToastr('Chưa chọn ngày!');
            return;
        }
        for(let item of dataConsultation.additional){
            if(item.id == dataConsultation.additional_id_choose){
                item.date = date;
                addAdditionalHtml(item)
            }
        }
        $('#modal_additional').modal('hide');
        dataConsultation.additional_id_choose = 0;
    }
}

function addAdditionalHtml(additionalItem){
    let isLockInput = (dataExamine.isDetail == 'true') ? true : false;
    let tbody = document.createElement("tbody");
    $(tbody).attr({id: 'tbody_add_' + additionalItem.id});
    let tr1 = document.createElement("tr");
    $(tr1).append(
        $('<td>').attr('colspan', '5')
            .addClass('text-center h-02 bg-f1 fs-6 cursor-pointer')
            .text(`Lần ${additionalItem.id} - ${additionalItem.date}`)
            .data('date', additionalItem.date)
            .data('index', additionalItem.id)
            .on("click", function(evt){
                let index = $(this).data('index');
                let text = $(this).data('date');
                showModalChangeDate('6', index, text);
            })
    );
    // Button delete group additional
    let button1 = document.createElement("button");
    let div1 = document.createElement("div");
    $(div1).attr({ class: 'flex-center-x gap-10px' });

    var svgElem1 = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    $(svgElem1).attr({ class: 'iconsvg-trash' });
    let useElem1 = document.createElementNS('http://www.w3.org/2000/svg', 'use');
    useElem1.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
    svgElem1.append(useElem1);

    button1.appendChild(svgElem1);

    button1.dataset.id = additionalItem.id;
    button1.dataset.date = additionalItem.date;

    $(button1).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
    $(button1).click(function () {
        let id = $(this).data('id');
        let date = $(this).data('date');
        showConfirmDeleteAdditionDate(id, date);
    });
    div1.append(button1);
    $(tr1).append($('<td>').append(div1));
    $(tbody).append(tr1);
    if(additionalItem.data && additionalItem.data.length > 0){
        for(let addi of additionalItem.data){
            let tr = document.createElement("tr");
            $(tr).attr('id', 'tr_pre_' + additionalItem.id + '_' + addi.id).addClass('fs-6');

            let td1 = document.createElement("td");
            let td2 = document.createElement("td");
            let td3 = document.createElement("td");
            let td4 = document.createElement("td");

            $(td3).addClass("min-w-150px")
                .append($("<input/>")
                    .attr({ "type": "text", "value": addi.note, "readonly": isLockInput })
                    .addClass("form-control form-control-title p-1 fs-13px")
                    .data("additional_id", additionalItem.id)
                    .data("item_id", addi.id)
                    .change(function () {
                        let id = $(this).data('additional_id');
                        let item_id = $(this).data('item_id');
                        let value = $(this).val();
                        changeMedicine3(id, value, item_id, 'note');
                    })
                );

            $(td4).append($("<input/>")
                .attr({ "type": "number", "value": addi.total, "readonly": isLockInput })
                .addClass("form-control form-control-title p-1 fs-6 text-red")
                .data("additional_id", additionalItem.id)
                .data("item_id", addi.id)
                .change(function () {
                    let id = $(this).data('additional_id');
                    let item_id = $(this).data('item_id');
                    let value = parseInt($(this).val());
                    if (isNaN(value)) {
                        displayErrorToastr('Số lượng thuốc không đúng định dạng!')
                    } else {
                        changeMedicine3(id, value, item_id, 'total');
                    }
                })
            );

            let td5 = document.createElement("td");
            let td6 = document.createElement("td");

            $(td2).attr({ class: 'min-w-150px' });
            $(td5).attr({ class: 'text-primary min-w-150px' });

            let button = document.createElement("button");
            let div = document.createElement("div");
            $(div).attr({ class: 'flex-center-x gap-10px' });

            var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            $(svgElem).attr({ class: 'iconsvg-trash' });
            useElem = document.createElementNS('http://www.w3.org/2000/svg', 'use');
            useElem.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', '/public/content/images/sprite.svg#trash');
            svgElem.append(useElem);

            button.appendChild(svgElem);

            button.dataset.id = additionalItem.id;
            button.dataset.item_id = addi.id
            button.dataset.name = addi.name;

            $(button).attr({ class: 'btn btn-action btn-action-cancel', type: 'button' });
            $(button).click(function () {
                let id = $(this).data('id');
                let item_id = $(this).data('item_id');
                let name = $(this).data('name');
                showConfirmDeleteMedicineNotModal(id, item_id, name);
            });

            $(td1).text(addi.stt);
            $(td2).text(addi.name);
            $(td5).text(addi.unit);

            div.append(button);
            td6.append(div);
            tr.append(td1);
            tr.append(td2);
            tr.append(td3);
            tr.append(td4);
            tr.append(td5);

            if (isLockInput) {
                $('#active_table_medicine').hide();
            } else {
                tr.append(td6);
            }
            tbody.append(tr);
        }
    }
    $('#tb_theo_doi_bo_xung table').append(tbody);
}

function changeMedicine3(additional_id, item_id, value, type){
    if(additional_id && dataConsultation.additional.length > 0){
        for(let item of dataConsultation.additional){
            if(item.id == additional_id){
                if(item.data.length > 0){
                    for(let med of item.data){
                        if (med.id == item_id) {
                            med[type] = value;
                        }
                    }
                    break;
                }
                break
            }
        }
    }
}

function showConfirmDeleteMedicineNotModal(id, item_id, name){
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_medicine_not_modal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteMedicineNotModal(${id}, ${item_id})" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_medicine_not_modal").modal('show');
}

function deleteMedicineNotModal(additional_id, item_id){
    $('#tr_pre_' + additional_id + '_' + item_id).remove();
    if (additional_id && dataConsultation.additional.length > 0) {
        for(let addi of dataConsultation.additional){
            if(addi.id == additional_id){
                if(addi.data && addi.data.length > 0){
                    removeItemArrayByIdObject(addi.data, item_id);
                }
                break
            }
        }
    }
}

function showConfirmDeleteAdditionDate(id, name){
    var confirmBox = `
    <div class="modal fade" id="modal_cf_delete_addition_date" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <button class="modal-btn-close btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
          <h4 class="modal-title text-center mb-4">Bạn muốn xóa <span class="text-tra-lai">`+ name + `</span> khỏi danh sách không?</h4>
          
          <div class="row g-2 justify-content-center">
            <div class="col-6">
              <button class="btn btn-cancel box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">Không</button>
            </div>
            <div class="col-6">
              <button onclick="deleteAdditionalDate(${id})" class="btn btn-primary box-btn w-100 text-uppercase" type="button" data-bs-dismiss="modal">
                <svg class="iconsvg-confirm flex-shrink-0 fs-16px me-2">
                  <use xlink:href="/public/content/images/sprite.svg#confirm"></use>
                </svg>
                Đồng ý
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>`;
    $("#modal_confirm_box").html(confirmBox);
    $("#modal_cf_delete_addition_date").modal('show');
}

function deleteAdditionalDate(id){
    $('#tbody_add_' + id).remove();
    if (id && dataConsultation.additional.length > 0) {
        removeItemArrayByIdObject(dataConsultation.additional, id);
    }
}

// Change date
function showModalChangeDate(type, index, text){
    console.log('showModalChangeDate', type, index, text);
    $('#date_change').data('type', type);
    $('#date_change').data('index', index);
    date_change.setDate(moment(text, 'DD-MM-YYYY').format('DD-MM-YYYY'), false);
    $('#modal_change_date').modal('show');
};

function changeDate(){
    let type = $('#date_change').data('type');
    let index = $('#date_change').data('index');
    let date = $('#date_change').val();
    let dataArr = [];
    let isIndex = false;
    let id_element = '';
    let value_element = date;
    console.log('changeDate', type, index, date);
    switch(type){
        case '1':
            // id
            dataArr = dataConsultation.clinicalExam;
            id_element = '#tr_ce_' + index +' td:first-child';
            break;
        case '2':
            // id
            dataArr = dataConsultation.nutritionTracking;
            id_element = '#tr_nt_' + index +' td:first-child';
            break;
        case '3':
            // id
            dataArr = dataConsultation.subclinicalOrther;
            id_element = '#tr_aso_' + index +' td:first-child';
            break;
        case '4':
            // index
            isIndex = true;
            dataArr = dataConsultation.subclinical.date;
            id_element = '#tb_can_lam_sang_head td[data-date_id="'+ index +'"]';
            break;
        case '5':
            // index
            isIndex = true;
            dataArr = dataConsultation.medicine.date;
            id_element = '#tb_theo_doi_thuoc_head td[data-date_id="'+ index +'"]';
            break;
        case '6':
            // id
            dataArr = dataConsultation.additional;
            id_element = '#tbody_add_' + index +' tr:first-child td:first-child';
            value_element = 'Lần ' + index + ' - ' + date;
            break;
        default: break;
    }
    if(isIndex){
        let indexInArray = dataArr.findIndex(s => s.date == date);
        if(indexInArray !== -1){
            displayMessageToastr('Ngày đã tồn tại');
            return;
        }
        for(let item of dataArr){
            if(item.id == index){
                item.date = date;
                break;
            }
        }
    }else{
        for(let item of dataArr){
            if(item.id == index){
                item.date = date;
                break;
            }
        }
    }
    $(id_element).text(value_element);
    $('#modal_change_date').modal('hide');
}


function deleteDate() {
    let type = $('#date_change').data('type');
    let index = $('#date_change').data('index');
    index = parseInt(index);
    switch(type) {
        case '4':
            // Xóa cột trong bảng
            $('#tb_can_lam_sang_head td[data-date_id="' + index + '"]').remove();
            $('.tr_can_lam_sang td[data-date_id="' + index + '"]').remove();
            break;
        case '5':
            // Xóa cột trong bảng
            $('#tb_theo_doi_thuoc_head td[data-date_id="' + index + '"]').remove();
            $('.tr_medicine td[data-date_id="' + index + '"]').remove();
            break;
        default: break;
    }

    // Xóa ngày khỏi mảng
    console.log('deleteDate', index);
    if(type == '4'){
        for(let item of dataConsultation.subclinical.data){
            item.values = item.values.filter(v => v.dateId !== index);
        }
        dataConsultation.subclinical.date = dataConsultation.subclinical.date.filter(d => d.id !== index);
    }
    if(type == '5'){
        for(let item of dataConsultation.medicine.data){
            item.values = item.values.filter(v => v.dateId !== index);
        }
        dataConsultation.medicine.date = dataConsultation.medicine.date.filter(d => d.id !== index);
    }
    
    $('#modal_change_date').modal('hide');
}

// Refactor các hàm liên quan đến subclinical và medicine để dùng dateId thay vì index
// Ví dụ: khi thêm ngày mới cho subclinical
function addSubclinicalDate(newDate) {
    // Tạo id duy nhất cho ngày
    const dateId = generateDateId('subclinical');
    dataConsultation.subclinical.date.push({ id: dateId, date: newDate});
    // Thêm giá trị rỗng cho mỗi item trong data
    for (let item of dataConsultation.subclinical.data) {
        if (!item.values) item.values = [];
        item.values.push({ dateId, value: '' });
    }
}
// Khi xóa ngày
function deleteSubclinicalDate(dateId) {
    // Xóa ngày khỏi mảng date
    dataConsultation.subclinical.date = dataConsultation.subclinical.date.filter(d => d.id !== dateId);
    // Xóa value tương ứng trong từng item
    for (let item of dataConsultation.subclinical.data) {
        if (item.values) {
            item.values = item.values.filter(v => v.dateId !== dateId);
        }
    }
}
// Khi thêm giá trị cho 1 item theo ngày
function setSubclinicalValue(itemId, dateId, value) {
    console.log('setSubclinicalValue', itemId, dateId, value);
    let item = dataConsultation.subclinical.data.find(i => String(i.id) === String(itemId));
    if (item) {
        let v = item.values.find(val => val.dateId === dateId);
        if (v) v.value = value;
        else item.values.push({ dateId, value });
    }
}
// Tương tự cho medicine
function addMedicineDate(newDate) {
    const dateId = generateDateId('medicine');
    dataConsultation.medicine.date.push({ id: dateId, date: newDate});
    for (let item of dataConsultation.medicine.data) {
        if (!item.values) item.values = [];
        item.values.push({ dateId, value: '' });
    }
}
function deleteMedicineDate(dateId) {
    dataConsultation.medicine.date = dataConsultation.medicine.date.filter(d => d.id !== dateId);
    for (let item of dataConsultation.medicine.data) {
        if (item.values) {
            item.values = item.values.filter(v => v.dateId !== dateId);
        }
    }
}
function setMedicineValue(itemId, dateId, value) {
    let item = dataConsultation.medicine.data.find(i => String(i.id) === String(itemId));
    if (item) {
        let v = item.values.find(val => val.dateId === dateId);
        if (v) v.value = value;
        else item.values.push({ dateId, value });
    }
}

// Khởi tạo các dropdown khi trang load
$(document).ready(function() {
    try {
        // Khởi tạo dropdown thực phẩm
        if ($('#food_name').length > 0) {
            generateFoodName('food_name');
        }

        // Khởi tạo dropdown món ăn
        if ($('#dish_name').length > 0) {
            generateDishName('dish_name');
        }

        // Khởi tạo header bảng với cấu hình mặc định
        if ($('#tb_menu').length > 0) {
            updateTableHeader();
        }

        // Event listener cho filter thực phẩm
        $('#food_type, #food_year').on('change', function() {
            // Reset dropdown thực phẩm khi thay đổi filter
            $('#food_name').val(null).trigger('change');
        });

        console.log('Menu functionality initialized successfully');

    } catch (error) {
        console.error('Error initializing menu functionality:', error);
    }
});
