@charset "UTF-8";
:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #f2564c;
  --bs-orange: #ff6d19;
  --bs-yellow: #ffc107;
  --bs-green: #5bcd8f;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #000;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #4a69d2;
  --bs-secondary: #a4adbd;
  --bs-success: #0c8809;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #f2564c;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 74, 105, 210;
  --bs-secondary-rgb: 164, 173, 189;
  --bs-success-rgb: 12, 136, 9;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 242, 86, 76;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 20, 23, 53;
  --bs-body-bg-rgb: 247, 248, 250;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto,
    "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(
    180deg,
    hsla(0, 0%, 100%, 0.15),
    hsla(0, 0%, 100%, 0)
  );
  --bs-body-font-family: Nunito, sans-serif;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.375;
  --bs-body-color: #141735;
  --bs-body-bg: #f7f8fa;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #dee4f0;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-2xl: 2rem;
  --bs-border-radius-pill: 50rem;
  --bs-link-color: #141735;
  --bs-link-hover-color: #415ec0;
  --bs-code-color: #d63384;
  --bs-highlight-bg: #fff3cd;
}
*,
:after,
:before {
  box-sizing: border-box;
}
@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}
body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  background-color: var(--bs-body-bg);
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  margin: 0;
  text-align: var(--bs-body-text-align);
}
hr {
  border: 0;
  border-top: 1px solid;
  color: inherit;
  margin: 1rem 0;
  opacity: 0.1;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  margin-top: 0;
}
.h1,
h1 {
  font-size: calc(1.34375rem + 1.125vw);
}
@media (min-width: 1200px) {
  .h1,
  h1 {
    font-size: 2.1875rem;
  }
}
.h2,
h2 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  .h2,
  h2 {
    font-size: 1.75rem;
  }
}
.h3,
h3 {
  font-size: calc(1.27813rem + 0.3375vw);
}
@media (min-width: 1200px) {
  .h3,
  h3 {
    font-size: 1.53125rem;
  }
}
.h4,
h4 {
  font-size: calc(1.25625rem + 0.075vw);
}
@media (min-width: 1200px) {
  .h4,
  h4 {
    font-size: 1.3125rem;
  }
}
.h5,
h5 {
  font-size: 1.09375rem;
}
.h6,
h6 {
  font-size: 0.875rem;
}
p {
  margin-bottom: 1rem;
  margin-top: 0;
}
abbr[title] {
  cursor: help;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}
address {
  font-style: normal;
  line-height: inherit;
  margin-bottom: 1rem;
}
ol,
ul {
  padding-left: 2rem;
}
dl,
ol,
ul {
  margin-bottom: 1rem;
  margin-top: 0;
}
ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
b,
strong {
  font-weight: bolder;
}
.small,
small {
  font-size: 0.875em;
}
.mark,
mark {
  background-color: var(--bs-highlight-bg);
  padding: 0.1875em;
}
sub,
sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: var(--bs-link-color);
  text-decoration: none;
}
a:hover {
  color: var(--bs-link-hover-color);
}
a:not([href]):not([class]),
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}
code,
kbd,
pre,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}
pre {
  display: block;
  font-size: 0.875em;
  margin-bottom: 1rem;
  margin-top: 0;
  overflow: auto;
}
pre code {
  color: inherit;
  font-size: inherit;
  word-break: normal;
}
code {
  word-wrap: break-word;
  color: var(--bs-code-color);
  font-size: 0.875em;
}
a > code {
  color: inherit;
}
kbd {
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
  color: var(--bs-body-bg);
  font-size: 0.875em;
  padding: 0.1875rem 0.375rem;
}
kbd kbd {
  font-size: 1em;
  padding: 0;
}
figure {
  margin: 0 0 1rem;
}
img,
svg {
  vertical-align: middle;
}
table {
  border-collapse: collapse;
  caption-side: bottom;
}
caption {
  color: #bdbdbd;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
  text-align: left;
}
th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}
tbody,
td,
tfoot,
th,
thead,
tr {
  border: 0 solid;
  border-color: inherit;
}
label {
  display: inline-block;
}
button {
  border-radius: 0;
}
button:focus:not(:focus-visible) {
  outline: 0;
}
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}
button,
select {
  text-transform: none;
}
[role="button"] {
  cursor: pointer;
}
select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}
[list]:not([type="date"]):not([type="datetime-local"]):not([type="month"]):not(
    [type="week"]
  ):not([type="time"])::-webkit-calendar-picker-indicator {
  display: none !important;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled),
button:not(:disabled) {
  cursor: pointer;
}
::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
textarea {
  resize: vertical;
}
fieldset {
  border: 0;
  margin: 0;
  min-width: 0;
  padding: 0;
}
legend {
  float: left;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
  margin-bottom: 0.5rem;
  padding: 0;
  width: 100%;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-year-field {
  padding: 0;
}
::-webkit-inner-spin-button {
  height: auto;
}
[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-color-swatch-wrapper {
  padding: 0;
}
::file-selector-button {
  -webkit-appearance: button;
  font: inherit;
}
output {
  display: inline-block;
}
iframe {
  border: 0;
}
summary {
  cursor: pointer;
  display: list-item;
}
progress {
  vertical-align: baseline;
}
[hidden] {
  display: none !important;
}
.lead {
  font-size: 1.09375rem;
  font-weight: 300;
}
.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}
.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}
.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}
.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}
.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}
.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}
.list-inline,
.list-unstyled {
  list-style: none;
  padding-left: 0;
}
.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}
.blockquote {
  font-size: 1.09375rem;
  margin-bottom: 1rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}
.blockquote-footer {
  color: #6c757d;
  font-size: 0.875em;
  margin-bottom: 1rem;
  margin-top: -1rem;
}
.blockquote-footer:before {
  content: "— ";
}
.img-fluid,
.img-thumbnail {
  height: auto;
  max-width: 100%;
}
.img-thumbnail {
  background-color: #f7f8fa;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  padding: 0.25rem;
}
.figure {
  display: inline-block;
}
.figure-img {
  line-height: 1;
  margin-bottom: 0.5rem;
}
.figure-caption {
  color: #6c757d;
  font-size: 0.875em;
}
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  margin-left: auto;
  margin-right: auto;
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  width: 100%;
}
@media (min-width: 576px) {
  .container,
  .container-sm {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container,
  .container-md,
  .container-sm {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1140px;
  }
}
@media (min-width: 1440px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1420px;
  }
}
.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(var(--bs-gutter-x) * -0.5);
  margin-right: calc(var(--bs-gutter-x) * -0.5);
  margin-top: calc(var(--bs-gutter-y) * -1);
}
.row > * {
  flex-shrink: 0;
  margin-top: var(--bs-gutter-y);
  max-width: 100%;
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  width: 100%;
}
.col {
  flex: 1 0 0%;
}
.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}
.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}
.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}
.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}
.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}
.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}
.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}
.col-auto {
  flex: 0 0 auto;
  width: auto;
}
.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}
.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}
.col-3 {
  flex: 0 0 auto;
  width: 25%;
}
.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}
.col-6 {
  flex: 0 0 auto;
  width: 50%;
}
.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}
.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}
.col-9 {
  flex: 0 0 auto;
  width: 75%;
}
.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}
.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}
.col-12 {
  flex: 0 0 auto;
  width: 100%;
}
.offset-1 {
  margin-left: 8.33333333%;
}
.offset-2 {
  margin-left: 16.66666667%;
}
.offset-3 {
  margin-left: 25%;
}
.offset-4 {
  margin-left: 33.33333333%;
}
.offset-5 {
  margin-left: 41.66666667%;
}
.offset-6 {
  margin-left: 50%;
}
.offset-7 {
  margin-left: 58.33333333%;
}
.offset-8 {
  margin-left: 66.66666667%;
}
.offset-9 {
  margin-left: 75%;
}
.offset-10 {
  margin-left: 83.33333333%;
}
.offset-11 {
  margin-left: 91.66666667%;
}
.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}
.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}
.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}
.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem;
}
.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}
.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem;
}
.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}
.g-3,
.gy-3 {
  --bs-gutter-y: 1rem;
}
.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem;
}
.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem;
}
.g-5,
.gx-5 {
  --bs-gutter-x: 3rem;
}
.g-5,
.gy-5 {
  --bs-gutter-y: 3rem;
}
@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1440px) {
  .col-xxl {
    flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
}
.table {
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
  margin-bottom: 1rem;
  vertical-align: top;
  width: 100%;
}
.table > :not(caption) > * > * {
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
  padding: 0.5rem;
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}
.table-group-divider {
  border-top: 2px solid;
}
.caption-top {
  caption-side: top;
}
.table-sm > :not(caption) > * > * {
  padding: 0.25rem;
}
.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}
.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}
.table-striped-columns > :not(caption) > tr > :nth-child(2n),
.table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}
.table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color);
}
.table-hover > tbody > tr:hover > * {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color);
}
.table-primary {
  --bs-table-color: #fff;
  --bs-table-bg: #4a69d2;
  --bs-table-border-color: #5c78d7;
  --bs-table-striped-bg: #5371d4;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #5c78d7;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #5874d5;
  --bs-table-hover-color: #000;
}
.table-primary,
.table-secondary {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-secondary {
  --bs-table-color: #000;
  --bs-table-bg: #a4adbd;
  --bs-table-border-color: #949caa;
  --bs-table-striped-bg: #9ca4b4;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #949caa;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #98a0af;
  --bs-table-hover-color: #000;
}
.table-success {
  --bs-table-color: #fff;
  --bs-table-bg: #0c8809;
  --bs-table-border-color: #249422;
  --bs-table-striped-bg: #188e15;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #249422;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #1e911b;
  --bs-table-hover-color: #000;
}
.table-info,
.table-success {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-info {
  --bs-table-color: #000;
  --bs-table-bg: #0dcaf0;
  --bs-table-border-color: #0cb6d8;
  --bs-table-striped-bg: #0cc0e4;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #0cb6d8;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #0cbbde;
  --bs-table-hover-color: #000;
}
.table-warning {
  --bs-table-color: #000;
  --bs-table-bg: #ffc107;
  --bs-table-border-color: #e6ae06;
  --bs-table-striped-bg: #f2b707;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e6ae06;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #ecb306;
  --bs-table-hover-color: #000;
}
.table-danger,
.table-warning {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-danger {
  --bs-table-color: #000;
  --bs-table-bg: #f2564c;
  --bs-table-border-color: #da4d44;
  --bs-table-striped-bg: #e65248;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #da4d44;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e05046;
  --bs-table-hover-color: #000;
}
.table-light {
  --bs-table-color: #000;
  --bs-table-bg: #f8f9fa;
  --bs-table-border-color: #dfe0e1;
  --bs-table-striped-bg: #ecedee;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #dfe0e1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e5e6e7;
  --bs-table-hover-color: #000;
}
.table-dark,
.table-light {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-dark {
  --bs-table-color: #fff;
  --bs-table-bg: #212529;
  --bs-table-border-color: #373b3e;
  --bs-table-striped-bg: #2c3034;
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: #373b3e;
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: #323539;
  --bs-table-hover-color: #fff;
}
.table-responsive {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
}
@media (max-width: 575.98px) {
  .table-responsive-sm {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
}
@media (max-width: 1439.98px) {
  .table-responsive-xxl {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
}
.form-label {
  margin-bottom: 0.5rem;
}
.col-form-label {
  font-size: inherit;
  line-height: 1.375;
  margin-bottom: 0;
  padding-bottom: calc(0.5625rem + 1px);
  padding-top: calc(0.5625rem + 1px);
}
.col-form-label-lg {
  font-size: 1.125rem;
  padding-bottom: calc(0.5rem + 1px);
  padding-top: calc(0.5rem + 1px);
}
.col-form-label-sm {
  font-size: 0.75rem;
  padding-bottom: calc(0.25rem + 1px);
  padding-top: calc(0.25rem + 1px);
}
.form-text {
  color: #bdbdbd;
  font-size: 0.875em;
  margin-top: 0.25rem;
}
.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-clip: padding-box;
  background-color: var(--bs-white);
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  color: #141735;
  display: block;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375;
  padding: 0.5625rem 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type="file"] {
  overflow: hidden;
}
.form-control[type="file"]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  background-color: var(--bs-white);
  border-color: #a5b4e9;
  box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  color: #141735;
  outline: 0;
}
.form-control::-webkit-date-and-time-value {
  height: 1.375em;
}
.form-control::-moz-placeholder {
  color: #566171;
  opacity: 1;
}
.form-control::placeholder {
  color: #566171;
  opacity: 1;
}
.form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}
.form-control::file-selector-button {
  -webkit-margin-end: 1rem;
  background-color: #e9ecef;
  border: 0 solid;
  border-color: inherit;
  border-inline-end-width: 1px;
  border-radius: 0;
  color: #141735;
  margin: -0.5625rem -1rem;
  margin-inline-end: 1rem;
  padding: 0.5625rem 1rem;
  pointer-events: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: #dde0e3;
}
.form-control-plaintext {
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
  color: #141735;
  display: block;
  line-height: 1.375;
  margin-bottom: 0;
  padding: 0.5625rem 0;
  width: 100%;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
  padding-left: 0;
  padding-right: 0;
}
.form-control-sm {
  border-radius: 0.25rem;
  font-size: 0.75rem;
  min-height: calc(1.375em + 0.5rem + 2px);
  padding: 0.25rem 0.75rem;
}
.form-control-sm::file-selector-button {
  -webkit-margin-end: 0.75rem;
  margin: -0.25rem -0.75rem;
  margin-inline-end: 0.75rem;
  padding: 0.25rem 0.75rem;
}
.form-control-lg {
  border-radius: 0.5rem;
  font-size: 1.125rem;
  min-height: calc(1.375em + 1rem + 2px);
  padding: 0.5rem 1rem;
}
.form-control-lg::file-selector-button {
  -webkit-margin-end: 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
  padding: 0.5rem 1rem;
}
textarea.form-control {
  min-height: calc(1.375em + 1.125rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.375em + 0.5rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.375em + 1rem + 2px);
}
.form-control-color {
  height: calc(1.375em + 1.125rem + 2px);
  padding: 0.5625rem;
  width: 3rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: 0.375rem;
}
.form-control-color::-webkit-color-swatch {
  border-radius: 0.375rem;
}
.form-control-color.form-control-sm {
  height: calc(1.375em + 0.5rem + 2px);
}
.form-control-color.form-control-lg {
  height: calc(1.375em + 1rem + 2px);
}
.form-select {
  -moz-padding-start: calc(1rem - 3px);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: var(--bs-white);
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 10px 10px;
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  color: #141735;
  display: block;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375;
  padding: 0.5625rem 3rem 0.5625rem 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: #a5b4e9;
  box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  outline: 0;
}
.form-select[multiple],
.form-select[size]:not([size="1"]) {
  background-image: none;
  padding-right: 1rem;
}
.form-select:disabled {
  background-color: #e9ecef;
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #141735;
}
.form-select-sm {
  border-radius: 0.25rem;
  font-size: 0.75rem;
  padding-bottom: 0.25rem;
  padding-left: 0.75rem;
  padding-top: 0.25rem;
}
.form-select-lg {
  border-radius: 0.5rem;
  font-size: 1.125rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-top: 0.5rem;
}
.form-check {
  display: block;
  margin-bottom: 0.125rem;
  min-height: 1.203125rem;
  padding-left: 1.5em;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}
.form-check-reverse {
  padding-left: 0;
  padding-right: 1.5em;
  text-align: right;
}
.form-check-reverse .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.5em;
}
.form-check-input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: var(--bs-white);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: contain;
  border: 1px solid #cbd5e1;
  height: 1em;
  margin-top: 0.1875em;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  vertical-align: top;
  width: 1em;
}
.form-check-input[type="checkbox"] {
  border-radius: 0.25em;
}
.form-check-input[type="radio"] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #a5b4e9;
  box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  outline: 0;
}
.form-check-input:checked {
  background-color: #4a69d2;
  border-color: #4a69d2;
}
.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3E%3C/svg%3E");
}
.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='2' fill='%23fff'/%3E%3C/svg%3E");
}
.form-check-input[type="checkbox"]:indeterminate {
  background-color: #4a69d2;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3E%3C/svg%3E");
  border-color: #4a69d2;
}
.form-check-input:disabled {
  filter: none;
  opacity: 0.5;
  pointer-events: none;
}
.form-check-input:disabled ~ .form-check-label,
.form-check-input[disabled] ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}
.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-3 -3 6 6'%3E%3Ccircle r='3' fill='rgba(0, 0, 0, 0.25)'/%3E%3C/svg%3E");
  background-position: 0;
  border-radius: 2em;
  margin-left: -2.5em;
  transition: background-position 0.15s ease-in-out;
  width: 2em;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-3 -3 6 6'%3E%3Ccircle r='3' fill='rgba(74, 105, 210, 0.25)'/%3E%3C/svg%3E");
}
.form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-3 -3 6 6'%3E%3Ccircle r='3' fill='%234A69D2'/%3E%3C/svg%3E");
  background-position: 100%;
}
.form-switch.form-check-reverse {
  padding-left: 0;
  padding-right: 2.5em;
}
.form-switch.form-check-reverse .form-check-input {
  margin-left: 0;
  margin-right: -2.5em;
}
.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}
.btn-check {
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
  position: absolute;
}
.btn-check:disabled + .btn,
.btn-check[disabled] + .btn {
  filter: none;
  opacity: 0.35;
  pointer-events: none;
}
.form-range {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
  height: 1rem;
  padding: 0;
  width: 100%;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #f7f8fa, 0 0 0 0 rgba(74, 105, 210, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #f7f8fa, 0 0 0 0 rgba(74, 105, 210, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background-color: #4a69d2;
  border: 0;
  border-radius: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  -webkit-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  width: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #c9d2f2;
}
.form-range::-webkit-slider-runnable-track {
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
  color: transparent;
  cursor: pointer;
  height: 0.5rem;
  width: 100%;
}
.form-range::-moz-range-thumb {
  -moz-appearance: none;
  appearance: none;
  background-color: #4a69d2;
  border: 0;
  border-radius: 1rem;
  height: 1rem;
  -moz-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  width: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #c9d2f2;
}
.form-range::-moz-range-track {
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
  color: transparent;
  cursor: pointer;
  height: 0.5rem;
  width: 100%;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: 40px;
  line-height: 1.25;
}
.form-floating > label {
  border: 1px solid transparent;
  height: 100%;
  left: 0;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  text-align: start;
  text-overflow: ellipsis;
  top: 0;
  transform-origin: 0 0;
  transition: none;
  white-space: nowrap;
  width: 100%;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > label {
  padding: 0.5625rem 1rem;
}
.form-floating > .form-control-plaintext::-moz-placeholder,
.form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control-plaintext:not(:-moz-placeholder-shown),
.form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-bottom: 0.5625rem;
  padding-top: 0.5625rem;
}
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown),
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-bottom: 0.5625rem;
  padding-top: 0.5625rem;
}
.form-floating > .form-control-plaintext:-webkit-autofill,
.form-floating > .form-control:-webkit-autofill {
  padding-bottom: 0.5625rem;
  padding-top: 0.5625rem;
}
.form-floating > .form-select {
  padding-bottom: 0.5625rem;
  padding-top: 0.5625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0;
  transform: none;
}
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0;
  transform: none;
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0;
  transform: none;
}
.form-floating > .form-control-plaintext ~ label {
  border-width: 1px 0;
}
.input-group {
  align-items: stretch;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-floating,
.input-group > .form-select {
  flex: 1 1 auto;
  min-width: 0;
  position: relative;
  width: 1%;
}
.input-group > .form-control:focus,
.input-group > .form-floating:focus-within,
.input-group > .form-select:focus {
  z-index: 5;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 5;
}
.input-group-text {
  align-items: center;
  background-color: #e9ecef;
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  color: #141735;
  display: flex;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375;
  padding: 0.5625rem 1rem;
  text-align: center;
  white-space: nowrap;
}
.input-group-lg > .btn,
.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text {
  border-radius: 0.5rem;
  font-size: 1.125rem;
  padding: 0.5rem 1rem;
}
.input-group-sm > .btn,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
  border-radius: 0.25rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}
.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 4rem;
}
.input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4),
.input-group.has-validation
  > .form-floating:nth-last-child(n + 3)
  > .form-control,
.input-group.has-validation
  > .form-floating:nth-last-child(n + 3)
  > .form-select,
.input-group.has-validation
  > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(
    .form-floating
  ),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3),
.input-group:not(.has-validation)
  > .form-floating:not(:last-child)
  > .form-control,
.input-group:not(.has-validation)
  > .form-floating:not(:last-child)
  > .form-select,
.input-group:not(.has-validation)
  > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(
    .form-floating
  ) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group
  > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
    .valid-feedback
  ):not(.invalid-tooltip):not(.invalid-feedback) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  margin-left: -1px;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.valid-feedback {
  color: #0c8809;
  display: none;
  font-size: 0.875em;
  margin-top: 0.25rem;
  width: 100%;
}
.valid-tooltip {
  background-color: rgba(12, 136, 9, 0.9);
  border-radius: 0.375rem;
  color: #fff;
  display: none;
  font-size: 0.75rem;
  margin-top: 0.1rem;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  position: absolute;
  top: 100%;
  z-index: 5;
}
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip,
.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip {
  display: block;
}
.form-control.is-valid,
.was-validated .form-control:valid {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%230C8809' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E");
  background-position: right calc(0.34375em + 0.28125rem) center;
  background-repeat: no-repeat;
  background-size: calc(0.6875em + 0.5625rem) calc(0.6875em + 0.5625rem);
  border-color: #0c8809;
  padding-right: calc(1.375em + 1.125rem);
}
.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
  border-color: #0c8809;
  box-shadow: 0 0 0 0 rgba(12, 136, 9, 0.25);
}
.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
  background-position: top calc(0.34375em + 0.28125rem) right
    calc(0.34375em + 0.28125rem);
  padding-right: calc(1.375em + 1.125rem);
}
.form-select.is-valid,
.was-validated .form-select:valid {
  border-color: #0c8809;
}
.form-select.is-valid:not([multiple]):not([size]),
.form-select.is-valid:not([multiple])[size="1"],
.was-validated .form-select:valid:not([multiple]):not([size]),
.was-validated .form-select:valid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E"),
    url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%230C8809' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E");
  background-position: right 1rem center, center right 3rem;
  background-size: 10px 10px,
    calc(0.6875em + 0.5625rem) calc(0.6875em + 0.5625rem);
  padding-right: 5.5rem;
}
.form-select.is-valid:focus,
.was-validated .form-select:valid:focus {
  border-color: #0c8809;
  box-shadow: 0 0 0 0 rgba(12, 136, 9, 0.25);
}
.form-control-color.is-valid,
.was-validated .form-control-color:valid {
  width: calc(4.125rem + 1.375em);
}
.form-check-input.is-valid,
.was-validated .form-check-input:valid {
  border-color: #0c8809;
}
.form-check-input.is-valid:checked,
.was-validated .form-check-input:valid:checked {
  background-color: #0c8809;
}
.form-check-input.is-valid:focus,
.was-validated .form-check-input:valid:focus {
  box-shadow: 0 0 0 0 rgba(12, 136, 9, 0.25);
}
.form-check-input.is-valid ~ .form-check-label,
.was-validated .form-check-input:valid ~ .form-check-label {
  color: #0c8809;
}
.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
.input-group > .form-control:not(:focus).is-valid,
.input-group > .form-floating:not(:focus-within).is-valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-control:not(:focus):valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.was-validated .input-group > .form-select:not(:focus):valid {
  z-index: 3;
}
.invalid-feedback {
  color: #f2564c;
  display: none;
  font-size: 0.875em;
  margin-top: 0.25rem;
  width: 100%;
}
.invalid-tooltip {
  background-color: rgba(242, 86, 76, 0.9);
  border-radius: 0.375rem;
  color: #000;
  display: none;
  font-size: 0.75rem;
  margin-top: 0.1rem;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  position: absolute;
  top: 100%;
  z-index: 5;
}
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip,
.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip {
  display: block;
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23F2564C'%3E%3Ccircle cx='6' cy='6' r='4.5'/%3E%3Cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3E%3Ccircle cx='6' cy='8.2' r='.6' fill='%23F2564C' stroke='none'/%3E%3C/svg%3E");
  background-position: right calc(0.34375em + 0.28125rem) center;
  background-repeat: no-repeat;
  background-size: calc(0.6875em + 0.5625rem) calc(0.6875em + 0.5625rem);
  border-color: #f2564c;
  padding-right: calc(1.375em + 1.125rem);
}
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: #f2564c;
  box-shadow: 0 0 0 0 rgba(242, 86, 76, 0.25);
}
.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
  background-position: top calc(0.34375em + 0.28125rem) right
    calc(0.34375em + 0.28125rem);
  padding-right: calc(1.375em + 1.125rem);
}
.form-select.is-invalid,
.was-validated .form-select:invalid {
  border-color: #f2564c;
}
.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"],
.was-validated .form-select:invalid:not([multiple]):not([size]),
.was-validated .form-select:invalid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E"),
    url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23F2564C'%3E%3Ccircle cx='6' cy='6' r='4.5'/%3E%3Cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3E%3Ccircle cx='6' cy='8.2' r='.6' fill='%23F2564C' stroke='none'/%3E%3C/svg%3E");
  background-position: right 1rem center, center right 3rem;
  background-size: 10px 10px,
    calc(0.6875em + 0.5625rem) calc(0.6875em + 0.5625rem);
  padding-right: 5.5rem;
}
.form-select.is-invalid:focus,
.was-validated .form-select:invalid:focus {
  border-color: #f2564c;
  box-shadow: 0 0 0 0 rgba(242, 86, 76, 0.25);
}
.form-control-color.is-invalid,
.was-validated .form-control-color:invalid {
  width: calc(4.125rem + 1.375em);
}
.form-check-input.is-invalid,
.was-validated .form-check-input:invalid {
  border-color: #f2564c;
}
.form-check-input.is-invalid:checked,
.was-validated .form-check-input:invalid:checked {
  background-color: #f2564c;
}
.form-check-input.is-invalid:focus,
.was-validated .form-check-input:invalid:focus {
  box-shadow: 0 0 0 0 rgba(242, 86, 76, 0.25);
}
.form-check-input.is-invalid ~ .form-check-label,
.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #f2564c;
}
.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
.input-group > .form-control:not(:focus).is-invalid,
.input-group > .form-floating:not(:focus-within).is-invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-control:not(:focus):invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.was-validated .input-group > .form-select:not(:focus):invalid {
  z-index: 4;
}
.btn {
  --bs-btn-padding-x: 1rem;
  --bs-btn-padding-y: 0.5625rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-font-weight: 700;
  --bs-btn-line-height: 1.375;
  --bs-btn-color: #141735;
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.375rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, 0.15),
    0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.35;
  --bs-btn-focus-box-shadow: 0 0 0 0 rgba(var(--bs-btn-focus-shadow-rgb), 0.5);
  background-color: var(--bs-btn-bg);
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  color: var(--bs-btn-color);
  cursor: pointer;
  display: inline-block;
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  text-align: center;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  vertical-align: middle;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  color: var(--bs-btn-hover-color);
}
.btn-check + .btn:hover {
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
  color: var(--bs-btn-color);
}
.btn:focus-visible {
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  box-shadow: var(--bs-btn-focus-box-shadow);
  color: var(--bs-btn-hover-color);
  outline: 0;
}
.btn-check:focus-visible + .btn {
  border-color: var(--bs-btn-hover-border-color);
  box-shadow: var(--bs-btn-focus-box-shadow);
  outline: 0;
}
.btn-check:checked + .btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check) + .btn:active {
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
  color: var(--bs-btn-active-color);
}
.btn-check:checked + .btn:focus-visible,
.btn.active:focus-visible,
.btn.show:focus-visible,
.btn:first-child:active:focus-visible,
:not(.btn-check) + .btn:active:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn.disabled,
.btn:disabled,
fieldset:disabled .btn {
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  color: var(--bs-btn-disabled-color);
  opacity: var(--bs-btn-disabled-opacity);
  pointer-events: none;
}
.btn-primary {
  --bs-btn-hover-bg: #3f59b3;
  --bs-btn-hover-border-color: #3b54a8;
  --bs-btn-active-bg: #3b54a8;
  --bs-btn-active-border-color: #384f9e;
}
.btn-secondary {
  --bs-btn-color: #000;
  --bs-btn-bg: #a4adbd;
  --bs-btn-border-color: #a4adbd;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #b2b9c7;
  --bs-btn-hover-border-color: #adb5c4;
  --bs-btn-focus-shadow-rgb: 139, 147, 161;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #b6bdca;
  --bs-btn-active-border-color: #adb5c4;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #a4adbd;
  --bs-btn-disabled-border-color: #a4adbd;
}
.btn-success {
  --bs-btn-color: #fff;
  --bs-btn-bg: #0c8809;
  --bs-btn-border-color: #0c8809;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0a7408;
  --bs-btn-hover-border-color: #0a6d07;
  --bs-btn-focus-shadow-rgb: 48, 154, 46;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0a6d07;
  --bs-btn-active-border-color: #096607;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #0c8809;
  --bs-btn-disabled-border-color: #0c8809;
}
.btn-info {
  --bs-btn-color: #000;
  --bs-btn-bg: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #31d2f2;
  --bs-btn-hover-border-color: #25cff2;
  --bs-btn-focus-shadow-rgb: 11, 172, 204;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #3dd5f3;
  --bs-btn-active-border-color: #25cff2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #0dcaf0;
  --bs-btn-disabled-border-color: #0dcaf0;
}
.btn-warning {
  --bs-btn-color: #000;
  --bs-btn-bg: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffca2c;
  --bs-btn-hover-border-color: #ffc720;
  --bs-btn-focus-shadow-rgb: 217, 164, 6;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffcd39;
  --bs-btn-active-border-color: #ffc720;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ffc107;
  --bs-btn-disabled-border-color: #ffc107;
}
.btn-danger {
  --bs-btn-color: #000;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #f46f67;
  --bs-btn-hover-border-color: #f3675e;
  --bs-btn-focus-shadow-rgb: 206, 73, 65;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #f57870;
  --bs-btn-active-border-color: #f3675e;
}
.btn-light {
  --bs-btn-color: #000;
  --bs-btn-bg: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #d3d4d5;
  --bs-btn-hover-border-color: #c6c7c8;
  --bs-btn-focus-shadow-rgb: 211, 212, 213;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #c6c7c8;
  --bs-btn-active-border-color: #babbbc;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #f8f9fa;
  --bs-btn-disabled-border-color: #f8f9fa;
}
.btn-dark {
  --bs-btn-color: #fff;
  --bs-btn-bg: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #424649;
  --bs-btn-hover-border-color: #373b3e;
  --bs-btn-focus-shadow-rgb: 66, 70, 73;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4d5154;
  --bs-btn-active-border-color: #373b3e;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #212529;
  --bs-btn-disabled-border-color: #212529;
}
.btn-outline-primary {
  --bs-btn-color: #4a69d2;
  --bs-btn-border-color: #4a69d2;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #4a69d2;
  --bs-btn-hover-border-color: #4a69d2;
  --bs-btn-focus-shadow-rgb: 74, 105, 210;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4a69d2;
  --bs-btn-active-border-color: #4a69d2;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #4a69d2;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #4a69d2;
  --bs-gradient: none;
}
.btn-outline-secondary {
  --bs-btn-color: #a4adbd;
  --bs-btn-border-color: #a4adbd;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #a4adbd;
  --bs-btn-hover-border-color: #a4adbd;
  --bs-btn-focus-shadow-rgb: 164, 173, 189;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #a4adbd;
  --bs-btn-active-border-color: #a4adbd;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #a4adbd;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #a4adbd;
  --bs-gradient: none;
}
.btn-outline-success {
  --bs-btn-color: #0c8809;
  --bs-btn-border-color: #0c8809;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0c8809;
  --bs-btn-hover-border-color: #0c8809;
  --bs-btn-focus-shadow-rgb: 12, 136, 9;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0c8809;
  --bs-btn-active-border-color: #0c8809;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0c8809;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0c8809;
  --bs-gradient: none;
}
.btn-outline-info {
  --bs-btn-color: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #0dcaf0;
  --bs-btn-hover-border-color: #0dcaf0;
  --bs-btn-focus-shadow-rgb: 13, 202, 240;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #0dcaf0;
  --bs-btn-active-border-color: #0dcaf0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0dcaf0;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0dcaf0;
  --bs-gradient: none;
}
.btn-outline-warning {
  --bs-btn-color: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffc107;
  --bs-btn-hover-border-color: #ffc107;
  --bs-btn-focus-shadow-rgb: 255, 193, 7;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffc107;
  --bs-btn-active-border-color: #ffc107;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffc107;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffc107;
  --bs-gradient: none;
}
.btn-outline-danger {
  --bs-btn-color: #f2564c;
  --bs-btn-border-color: #f2564c;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #f2564c;
  --bs-btn-hover-border-color: #f2564c;
  --bs-btn-focus-shadow-rgb: 242, 86, 76;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #f2564c;
  --bs-btn-active-border-color: #f2564c;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #f2564c;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #f2564c;
  --bs-gradient: none;
}
.btn-outline-light {
  --bs-btn-color: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #f8f9fa;
  --bs-btn-hover-border-color: #f8f9fa;
  --bs-btn-focus-shadow-rgb: 248, 249, 250;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #f8f9fa;
  --bs-btn-active-border-color: #f8f9fa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #f8f9fa;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #f8f9fa;
  --bs-gradient: none;
}
.btn-outline-dark {
  --bs-btn-color: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #212529;
  --bs-btn-hover-border-color: #212529;
  --bs-btn-focus-shadow-rgb: 33, 37, 41;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #212529;
  --bs-btn-active-border-color: #212529;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #212529;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #212529;
  --bs-gradient: none;
}
.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: none;
  --bs-btn-focus-shadow-rgb: 101, 128, 217;
  text-decoration: none;
}
.btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.btn-link:hover {
  color: var(--bs-btn-hover-color);
}
.btn-group-lg > .btn,
.btn-lg {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size: 1.125rem;
  --bs-btn-border-radius: 0.5rem;
}
.btn-group-sm > .btn,
.btn-sm {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.75rem;
  --bs-btn-font-size: 0.75rem;
  --bs-btn-border-radius: 0.25rem;
}
.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}
.collapse:not(.show) {
  display: none;
}
.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  height: auto;
  transition: width 0.35s ease;
  width: 0;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}
.dropdown,
.dropdown-center,
.dropend,
.dropstart,
.dropup,
.dropup-center {
  position: relative;
}
.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle:after {
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  border-right: 0.3em solid transparent;
  border-top: 0.3em solid;
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
}
.dropdown-toggle:empty:after {
  margin-left: 0;
}
.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0.625rem;
  --bs-dropdown-padding-y: 0.625rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 0.875rem;
  --bs-dropdown-color: #141735;
  --bs-dropdown-bg: #fff;
  --bs-dropdown-border-color: #dfe7fc;
  --bs-dropdown-border-radius: 0.375rem;
  --bs-dropdown-border-width: 0;
  --bs-dropdown-inner-border-radius: 0.375rem;
  --bs-dropdown-divider-bg: #dfe7fc;
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0px 4px 12px rgba(51, 51, 51, 0.12);
  --bs-dropdown-link-color: #566171;
  --bs-dropdown-link-hover-color: #141735;
  --bs-dropdown-link-hover-bg: #edf5ff;
  --bs-dropdown-link-active-color: #141735;
  --bs-dropdown-link-active-bg: #e3efff;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-item-padding-x: 0.625rem;
  --bs-dropdown-item-padding-y: 0.625rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 0.625rem;
  --bs-dropdown-header-padding-y: 0.625rem;
  background-clip: padding-box;
  background-color: var(--bs-dropdown-bg);
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
  color: var(--bs-dropdown-color);
  display: none;
  font-size: var(--bs-dropdown-font-size);
  list-style: none;
  margin: 0;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  position: absolute;
  text-align: left;
  z-index: var(--bs-dropdown-zindex);
}
.dropdown-menu[data-bs-popper] {
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
  top: 100%;
}
.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  left: 0;
  right: auto;
}
.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  left: auto;
  right: 0;
}
@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    left: 0;
    right: auto;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    left: auto;
    right: 0;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    left: 0;
    right: auto;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    left: auto;
    right: 0;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    left: 0;
    right: auto;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    left: auto;
    right: 0;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    left: 0;
    right: auto;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    left: auto;
    right: 0;
  }
}
@media (min-width: 1440px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    left: 0;
    right: auto;
  }
  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    left: auto;
    right: 0;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  bottom: 100%;
  margin-bottom: var(--bs-dropdown-spacer);
  margin-top: 0;
  top: auto;
}
.dropup .dropdown-toggle:after {
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
  border-right: 0.3em solid transparent;
  border-top: 0;
  content: "";
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
}
.dropup .dropdown-toggle:empty:after {
  margin-left: 0;
}
.dropend .dropdown-menu[data-bs-popper] {
  left: 100%;
  margin-left: var(--bs-dropdown-spacer);
  margin-top: 0;
  right: auto;
  top: 0;
}
.dropend .dropdown-toggle:after {
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
  border-right: 0;
  border-top: 0.3em solid transparent;
  content: "";
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
}
.dropend .dropdown-toggle:empty:after {
  margin-left: 0;
}
.dropend .dropdown-toggle:after {
  vertical-align: 0;
}
.dropstart .dropdown-menu[data-bs-popper] {
  left: auto;
  margin-right: var(--bs-dropdown-spacer);
  margin-top: 0;
  right: 100%;
  top: 0;
}
.dropstart .dropdown-toggle:after {
  content: "";
  display: inline-block;
  display: none;
  margin-left: 0.255em;
  vertical-align: 0.255em;
}
.dropstart .dropdown-toggle:before {
  border-bottom: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-top: 0.3em solid transparent;
  content: "";
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
}
.dropstart .dropdown-toggle:empty:after {
  margin-left: 0;
}
.dropstart .dropdown-toggle:before {
  vertical-align: 0;
}
.dropdown-divider {
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  opacity: 1;
  overflow: hidden;
}
.dropdown-item {
  background-color: transparent;
  border: 0;
  clear: both;
  color: var(--bs-dropdown-link-color);
  display: block;
  font-weight: 400;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  text-align: inherit;
  white-space: nowrap;
  width: 100%;
}
.dropdown-item:focus,
.dropdown-item:hover {
  background-color: var(--bs-dropdown-link-hover-bg);
  color: var(--bs-dropdown-link-hover-color);
}
.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--bs-dropdown-link-active-bg);
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
}
.dropdown-item.disabled,
.dropdown-item:disabled {
  background-color: transparent;
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
}
.dropdown-menu.show {
  display: block;
}
.dropdown-header {
  color: var(--bs-dropdown-header-color);
  display: block;
  font-size: 0.75rem;
  margin-bottom: 0;
  padding: var(--bs-dropdown-header-padding-y)
    var(--bs-dropdown-header-padding-x);
  white-space: nowrap;
}
.dropdown-item-text {
  color: var(--bs-dropdown-link-color);
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
}
.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: #dfe7fc;
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #fff;
  --bs-dropdown-divider-bg: #dfe7fc;
  --bs-dropdown-link-hover-bg: hsla(0, 0%, 100%, 0.15);
  --bs-dropdown-link-active-color: #141735;
  --bs-dropdown-link-active-bg: #e3efff;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}
.btn-group,
.btn-group-vertical {
  display: inline-flex;
  position: relative;
  vertical-align: middle;
}
.btn-group-vertical > .btn,
.btn-group > .btn {
  flex: 1 1 auto;
  position: relative;
}
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:hover,
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus,
.btn-group > .btn:hover {
  z-index: 1;
}
.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}
.btn-group {
  border-radius: 0.375rem;
}
.btn-group > .btn-group:not(:first-child),
.btn-group > :not(.btn-check:first-child) + .btn {
  margin-left: -1px;
}
.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn.dropdown-toggle-split:first-child,
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:nth-child(n + 3),
.btn-group > :not(.btn-check) + .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.dropdown-toggle-split {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.dropdown-toggle-split:after,
.dropend .dropdown-toggle-split:after,
.dropup .dropdown-toggle-split:after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split:before {
  margin-right: 0;
}
.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-left: 0.5625rem;
  padding-right: 0.5625rem;
}
.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.btn-group-vertical {
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn-group:not(:first-child),
.btn-group-vertical > .btn:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn ~ .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin-bottom: 0;
  padding-left: 0;
}
.nav-link {
  color: var(--bs-nav-link-color);
  display: block;
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:focus,
.nav-link:hover {
  color: var(--bs-nav-link-hover-color);
}
.nav-link.disabled {
  color: var(--bs-nav-link-disabled-color);
  cursor: default;
  pointer-events: none;
}
.nav-tabs {
  --bs-nav-tabs-border-width: 1px;
  --bs-nav-tabs-border-color: #dee2e6;
  --bs-nav-tabs-link-hover-border-color: #e9ecef #e9ecef #dee2e6;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #f7f8fa;
  --bs-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 #f7f8fa;
  border-bottom: var(--bs-nav-tabs-border-width) solid
    var(--bs-nav-tabs-border-color);
}
.nav-tabs .nav-link {
  background: none;
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
  margin-bottom: calc(var(--bs-nav-tabs-border-width) * -1);
}
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: var(--bs-nav-tabs-link-hover-border-color);
  isolation: isolate;
}
.nav-tabs .nav-link.disabled,
.nav-tabs .nav-link:disabled {
  background-color: transparent;
  border-color: transparent;
  color: var(--bs-nav-link-disabled-color);
}
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
  color: var(--bs-nav-tabs-link-active-color);
}
.nav-tabs .dropdown-menu {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-top: calc(var(--bs-nav-tabs-border-width) * -1);
}
.nav-pills {
  --bs-nav-pills-border-radius: 0.375rem;
  --bs-nav-pills-link-active-color: #fff;
  --bs-nav-pills-link-active-bg: #4a69d2;
}
.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: var(--bs-nav-pills-border-radius);
}
.nav-pills .nav-link:disabled {
  background-color: transparent;
  border-color: transparent;
  color: var(--bs-nav-link-disabled-color);
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: var(--bs-nav-pills-link-active-bg);
  color: var(--bs-nav-pills-link-active-color);
}
.nav-fill .nav-item,
.nav-fill > .nav-link {
  flex: 1 1 auto;
  text-align: center;
}
.nav-justified .nav-item,
.nav-justified > .nav-link {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}
.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(0, 0, 0, 0.55);
  --bs-navbar-hover-color: rgba(0, 0, 0, 0.7);
  --bs-navbar-disabled-color: rgba(0, 0, 0, 0.3);
  --bs-navbar-active-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-padding-y: 0.328125rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.125rem;
  --bs-navbar-brand-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-hover-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.125rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(0, 0, 0, 0.55)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
  --bs-navbar-toggler-border-color: rgba(0, 0, 0, 0.1);
  --bs-navbar-toggler-border-radius: 0.375rem;
  --bs-navbar-toggler-focus-width: 0;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
  position: relative;
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-lg,
.navbar > .container-md,
.navbar > .container-sm,
.navbar > .container-xl,
.navbar > .container-xxl {
  align-items: center;
  display: flex;
  flex-wrap: inherit;
  justify-content: space-between;
}
.navbar-brand {
  color: var(--bs-navbar-brand-color);
  font-size: var(--bs-navbar-brand-font-size);
  margin-right: var(--bs-navbar-brand-margin-end);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  padding-top: var(--bs-navbar-brand-padding-y);
  white-space: nowrap;
}
.navbar-brand:focus,
.navbar-brand:hover {
  color: var(--bs-navbar-brand-hover-color);
}
.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  list-style: none;
  margin-bottom: 0;
  padding-left: 0;
}
.navbar-nav .nav-link.active,
.navbar-nav .show > .nav-link {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}
.navbar-text {
  color: var(--bs-navbar-color);
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
.navbar-text a,
.navbar-text a:focus,
.navbar-text a:hover {
  color: var(--bs-navbar-active-color);
}
.navbar-collapse {
  align-items: center;
  flex-basis: 100%;
  flex-grow: 1;
}
.navbar-toggler {
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  color: var(--bs-navbar-color);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
  outline: 0;
  text-decoration: none;
}
.navbar-toggler-icon {
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100%;
  display: inline-block;
  height: 1.5em;
  vertical-align: middle;
  width: 1.5em;
}
.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}
@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-left: var(--bs-navbar-nav-link-padding-x);
    padding-right: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    background-color: transparent !important;
    border: 0 !important;
    flex-grow: 1;
    height: auto !important;
    position: static;
    transform: none !important;
    transition: none;
    visibility: visible !important;
    width: auto !important;
    z-index: auto;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-left: var(--bs-navbar-nav-link-padding-x);
    padding-right: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    background-color: transparent !important;
    border: 0 !important;
    flex-grow: 1;
    height: auto !important;
    position: static;
    transform: none !important;
    transition: none;
    visibility: visible !important;
    width: auto !important;
    z-index: auto;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-left: var(--bs-navbar-nav-link-padding-x);
    padding-right: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    background-color: transparent !important;
    border: 0 !important;
    flex-grow: 1;
    height: auto !important;
    position: static;
    transform: none !important;
    transition: none;
    visibility: visible !important;
    width: auto !important;
    z-index: auto;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-left: var(--bs-navbar-nav-link-padding-x);
    padding-right: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    background-color: transparent !important;
    border: 0 !important;
    flex-grow: 1;
    height: auto !important;
    position: static;
    transform: none !important;
    transition: none;
    visibility: visible !important;
    width: auto !important;
    z-index: auto;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (min-width: 1440px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-left: var(--bs-navbar-nav-link-padding-x);
    padding-right: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    background-color: transparent !important;
    border: 0 !important;
    flex-grow: 1;
    height: auto !important;
    position: static;
    transform: none !important;
    transition: none;
    visibility: visible !important;
    width: auto !important;
    z-index: auto;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-left: var(--bs-navbar-nav-link-padding-x);
  padding-right: var(--bs-navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  background-color: transparent !important;
  border: 0 !important;
  flex-grow: 1;
  height: auto !important;
  position: static;
  transform: none !important;
  transition: none;
  visibility: visible !important;
  width: auto !important;
  z-index: auto;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  overflow-y: visible;
  padding: 0;
}
.navbar-dark {
  --bs-navbar-color: hsla(0, 0%, 100%, 0.55);
  --bs-navbar-hover-color: hsla(0, 0%, 100%, 0.75);
  --bs-navbar-disabled-color: hsla(0, 0%, 100%, 0.25);
  --bs-navbar-active-color: #fff;
  --bs-navbar-brand-color: #fff;
  --bs-navbar-brand-hover-color: #fff;
  --bs-navbar-toggler-border-color: hsla(0, 0%, 100%, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(255, 255, 255, 0.55)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}
.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-border-width: 1px;
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: 0;
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: -1px;
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(0, 0, 0, 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: #fff;
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  word-wrap: break-word;
  background-clip: border-box;
  background-color: var(--bs-card-bg);
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
  display: flex;
  flex-direction: column;
  height: var(--bs-card-height);
  min-width: 0;
  position: relative;
}
.card > hr {
  margin-left: 0;
  margin-right: 0;
}
.card > .list-group {
  border-bottom: inherit;
  border-top: inherit;
}
.card > .list-group:first-child {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
  border-top-width: 0;
}
.card > .list-group:last-child {
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-width: 0;
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}
.card-body {
  color: var(--bs-card-color);
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
}
.card-title {
  margin-bottom: var(--bs-card-title-spacer-y);
}
.card-subtitle {
  margin-top: calc(var(--bs-card-title-spacer-y) * -0.5);
}
.card-subtitle,
.card-text:last-child {
  margin-bottom: 0;
}
.card-link + .card-link {
  margin-left: var(--bs-card-spacer-x);
}
.card-header {
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
  color: var(--bs-card-cap-color);
  margin-bottom: 0;
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
}
.card-header:first-child {
  border-radius: var(--bs-card-inner-border-radius)
    var(--bs-card-inner-border-radius) 0 0;
}
.card-footer {
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
  color: var(--bs-card-cap-color);
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
}
.card-footer:last-child {
  border-radius: 0 0 var(--bs-card-inner-border-radius)
    var(--bs-card-inner-border-radius);
}
.card-header-tabs {
  border-bottom: 0;
  margin-bottom: calc(var(--bs-card-cap-padding-y) * -1);
  margin-left: calc(var(--bs-card-cap-padding-x) * -0.5);
  margin-right: calc(var(--bs-card-cap-padding-x) * -0.5);
}
.card-header-tabs .nav-link.active {
  background-color: var(--bs-card-bg);
  border-bottom-color: var(--bs-card-bg);
}
.card-header-pills {
  margin-left: calc(var(--bs-card-cap-padding-x) * -0.5);
  margin-right: calc(var(--bs-card-cap-padding-x) * -0.5);
}
.card-img-overlay {
  border-radius: var(--bs-card-inner-border-radius);
  bottom: 0;
  left: 0;
  padding: var(--bs-card-img-overlay-padding);
  position: absolute;
  right: 0;
  top: 0;
}
.card-img,
.card-img-bottom,
.card-img-top {
  width: 100%;
}
.card-img,
.card-img-top {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.card-img,
.card-img-bottom {
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
}
.card-group > .card {
  margin-bottom: var(--bs-card-group-margin);
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    border-left: 0;
    margin-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-header,
  .card-group > .card:not(:last-child) .card-img-top {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-footer,
  .card-group > .card:not(:last-child) .card-img-bottom {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-header,
  .card-group > .card:not(:first-child) .card-img-top {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-footer,
  .card-group > .card:not(:first-child) .card-img-bottom {
    border-bottom-left-radius: 0;
  }
}
.accordion {
  --bs-accordion-color: #141735;
  --bs-accordion-bg: #f7f8fa;
  --bs-accordion-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 1px;
  --bs-accordion-border-radius: 0.375rem;
  --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: #141735;
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 2.09 8.82.91 5 4.731 1.18.911 0 2.09l5 5 5-5Z' fill='%23A4ADBD'/%3E%3C/svg%3E");
  --bs-accordion-btn-icon-width: 0.5rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 2.09 8.82.91 5 4.731 1.18.911 0 2.09l5 5 5-5Z' fill='%23A4ADBD'/%3E%3C/svg%3E");
  --bs-accordion-btn-focus-border-color: #a5b4e9;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: #435fbd;
  --bs-accordion-active-bg: #edf0fb;
}
.accordion-button {
  align-items: center;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  color: var(--bs-accordion-btn-color);
  display: flex;
  font-size: 0.875rem;
  overflow-anchor: none;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  position: relative;
  text-align: left;
  transition: var(--bs-accordion-transition);
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(var(--bs-accordion-border-width) * -1) 0
    var(--bs-accordion-border-color);
  color: var(--bs-accordion-active-color);
}
.accordion-button:not(.collapsed):after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.accordion-button:after {
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  content: "";
  flex-shrink: 0;
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  transition: var(--bs-accordion-btn-icon-transition);
  width: var(--bs-accordion-btn-icon-width);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button:after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  border-color: var(--bs-accordion-btn-focus-border-color);
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
  outline: 0;
  z-index: 3;
}
.accordion-header {
  margin-bottom: 0;
}
.accordion-item {
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid
    var(--bs-accordion-border-color);
  color: var(--bs-accordion-color);
}
.accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-left-radius: var(--bs-accordion-border-radius);
  border-bottom-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-left-radius: var(--bs-accordion-border-radius);
  border-bottom-right-radius: var(--bs-accordion-border-radius);
}
.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}
.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-left: 0;
  border-radius: 0;
  border-right: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button,
.accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}
.breadcrumb {
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-bg: ;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: #6c757d;
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: #4a69d2;
  background-color: var(--bs-breadcrumb-bg);
  border-radius: var(--bs-breadcrumb-border-radius);
  display: flex;
  flex-wrap: wrap;
  font-size: var(--bs-breadcrumb-font-size);
  list-style: none;
  margin-bottom: var(--bs-breadcrumb-margin-bottom);
  padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
}
.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--bs-breadcrumb-item-padding-x);
}
.breadcrumb-item + .breadcrumb-item:before {
  color: var(--bs-breadcrumb-divider-color);
  content: var(--bs-breadcrumb-divider, "/");
  float: left;
  padding-right: var(--bs-breadcrumb-item-padding-x);
}
.breadcrumb-item.active {
  color: var(--bs-breadcrumb-item-active-color);
}
.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-color: var(--bs-link-color);
  --bs-pagination-bg: #fff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: var(--bs-link-hover-color);
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #dee2e6;
  --bs-pagination-focus-color: var(--bs-link-hover-color);
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #4a69d2;
  --bs-pagination-active-border-color: #4a69d2;
  --bs-pagination-disabled-color: #6c757d;
  --bs-pagination-disabled-bg: #fff;
  --bs-pagination-disabled-border-color: #dee2e6;
  display: flex;
  list-style: none;
  padding-left: 0;
}
.page-link {
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid
    var(--bs-pagination-border-color);
  color: var(--bs-pagination-color);
  display: block;
  font-size: var(--bs-pagination-font-size);
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  position: relative;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  background-color: var(--bs-pagination-hover-bg);
  border-color: var(--bs-pagination-hover-border-color);
  color: var(--bs-pagination-hover-color);
  z-index: 2;
}
.page-link:focus {
  background-color: var(--bs-pagination-focus-bg);
  box-shadow: var(--bs-pagination-focus-box-shadow);
  color: var(--bs-pagination-focus-color);
  outline: 0;
  z-index: 3;
}
.active > .page-link,
.page-link.active {
  background-color: var(--bs-pagination-active-bg);
  border-color: var(--bs-pagination-active-border-color);
  color: var(--bs-pagination-active-color);
  z-index: 3;
}
.disabled > .page-link,
.page-link.disabled {
  background-color: var(--bs-pagination-disabled-bg);
  border-color: var(--bs-pagination-disabled-border-color);
  color: var(--bs-pagination-disabled-color);
  pointer-events: none;
}
.page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.page-item:first-child .page-link {
  border-bottom-left-radius: var(--bs-pagination-border-radius);
  border-top-left-radius: var(--bs-pagination-border-radius);
}
.page-item:last-child .page-link {
  border-bottom-right-radius: var(--bs-pagination-border-radius);
  border-top-right-radius: var(--bs-pagination-border-radius);
}
.pagination-lg {
  --bs-pagination-padding-x: 1.5rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1.125rem;
  --bs-pagination-border-radius: 0.5rem;
}
.pagination-sm {
  --bs-pagination-padding-x: 0.5rem;
  --bs-pagination-padding-y: 0.25rem;
  --bs-pagination-font-size: 0.75rem;
  --bs-pagination-border-radius: 0.25rem;
}
.badge {
  --bs-badge-padding-x: 0.8333333333em;
  --bs-badge-padding-y: 0.5em;
  --bs-badge-font-size: 0.75rem;
  --bs-badge-font-weight: 600;
  --bs-badge-color: #fff;
  --bs-badge-border-radius: 3.125rem;
  border-radius: var(--bs-badge-border-radius);
  color: var(--bs-badge-color);
  display: inline-block;
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  text-align: center;
  vertical-align: baseline;
  white-space: nowrap;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: 1px solid var(--bs-alert-border-color);
  --bs-alert-border-radius: 0.375rem;
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
  color: var(--bs-alert-color);
  margin-bottom: var(--bs-alert-margin-bottom);
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  position: relative;
}
.alert-heading {
  color: inherit;
}
.alert-link {
  font-weight: 700;
}
.alert-dismissible {
  padding-right: 3rem;
}
.alert-dismissible .btn-close {
  padding: 1.25rem 1rem;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
.alert-primary {
  --bs-alert-color: #2c3f7e;
  --bs-alert-bg: #dbe1f6;
  --bs-alert-border-color: #c9d2f2;
}
.alert-primary .alert-link {
  color: #233265;
}
.alert-secondary {
  --bs-alert-color: #626871;
  --bs-alert-bg: #edeff2;
  --bs-alert-border-color: #e4e6eb;
}
.alert-secondary .alert-link {
  color: #4e535a;
}
.alert-success {
  --bs-alert-color: #075205;
  --bs-alert-bg: #cee7ce;
  --bs-alert-border-color: #b6dbb5;
}
.alert-success .alert-link {
  color: #064204;
}
.alert-info {
  --bs-alert-color: #055160;
  --bs-alert-bg: #cff4fc;
  --bs-alert-border-color: #b6effb;
}
.alert-info .alert-link {
  color: #04414d;
}
.alert-warning {
  --bs-alert-color: #664d03;
  --bs-alert-bg: #fff3cd;
  --bs-alert-border-color: #ffecb5;
}
.alert-warning .alert-link {
  color: #523e02;
}
.alert-danger {
  --bs-alert-color: #91342e;
  --bs-alert-bg: #fcdddb;
  --bs-alert-border-color: #fbccc9;
}
.alert-danger .alert-link {
  color: #742a25;
}
.alert-light {
  --bs-alert-color: #636464;
  --bs-alert-bg: #fefefe;
  --bs-alert-border-color: #fdfdfe;
}
.alert-light .alert-link {
  color: #4f5050;
}
.alert-dark {
  --bs-alert-color: #141619;
  --bs-alert-bg: #d3d3d4;
  --bs-alert-border-color: #bcbebf;
}
.alert-dark .alert-link {
  color: #101214;
}
@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.65625rem;
  --bs-progress-bg: #e9ecef;
  --bs-progress-border-radius: 0.375rem;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #fff;
  --bs-progress-bar-bg: #4a69d2;
  --bs-progress-bar-transition: width 0.6s ease;
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
  font-size: var(--bs-progress-font-size);
  height: var(--bs-progress-height);
}
.progress,
.progress-bar {
  display: flex;
  overflow: hidden;
}
.progress-bar {
  background-color: var(--bs-progress-bar-bg);
  color: var(--bs-progress-bar-color);
  flex-direction: column;
  justify-content: center;
  text-align: center;
  transition: var(--bs-progress-bar-transition);
  white-space: nowrap;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}
.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    hsla(0, 0%, 100%, 0.15) 25%,
    transparent 0,
    transparent 50%,
    hsla(0, 0%, 100%, 0.15) 0,
    hsla(0, 0%, 100%, 0.15) 75%,
    transparent 0,
    transparent
  );
  background-size: var(--bs-progress-height) var(--bs-progress-height);
}
.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}
.list-group {
  --bs-list-group-color: #212529;
  --bs-list-group-bg: #fff;
  --bs-list-group-border-color: rgba(0, 0, 0, 0.125);
  --bs-list-group-border-width: 1px;
  --bs-list-group-border-radius: 0.375rem;
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: #141735;
  --bs-list-group-action-hover-color: #141735;
  --bs-list-group-action-hover-bg: #f8f9fa;
  --bs-list-group-action-active-color: #141735;
  --bs-list-group-action-active-bg: #e9ecef;
  --bs-list-group-disabled-color: #6c757d;
  --bs-list-group-disabled-bg: #fff;
  --bs-list-group-active-color: #fff;
  --bs-list-group-active-bg: #4a69d2;
  --bs-list-group-active-border-color: #4a69d2;
  border-radius: var(--bs-list-group-border-radius);
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  padding-left: 0;
}
.list-group-numbered {
  counter-reset: section;
  list-style-type: none;
}
.list-group-numbered > .list-group-item:before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}
.list-group-item-action {
  color: var(--bs-list-group-action-color);
  text-align: inherit;
  width: 100%;
}
.list-group-item-action:focus,
.list-group-item-action:hover {
  background-color: var(--bs-list-group-action-hover-bg);
  color: var(--bs-list-group-action-hover-color);
  text-decoration: none;
  z-index: 1;
}
.list-group-item-action:active {
  background-color: var(--bs-list-group-action-active-bg);
  color: var(--bs-list-group-action-active-color);
}
.list-group-item {
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid
    var(--bs-list-group-border-color);
  color: var(--bs-list-group-color);
  display: block;
  padding: var(--bs-list-group-item-padding-y)
    var(--bs-list-group-item-padding-x);
  position: relative;
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}
.list-group-item.disabled,
.list-group-item:disabled {
  background-color: var(--bs-list-group-disabled-bg);
  color: var(--bs-list-group-disabled-color);
  pointer-events: none;
}
.list-group-item.active {
  background-color: var(--bs-list-group-active-bg);
  border-color: var(--bs-list-group-active-border-color);
  color: var(--bs-list-group-active-color);
  z-index: 2;
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  border-top-width: var(--bs-list-group-border-width);
  margin-top: calc(var(--bs-list-group-border-width) * -1);
}
.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child:not(:last-child) {
  border-bottom-left-radius: var(--bs-list-group-border-radius);
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--bs-list-group-border-radius);
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-left-width: 0;
  border-top-width: var(--bs-list-group-border-width);
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  border-left-width: var(--bs-list-group-border-width);
  margin-left: calc(var(--bs-list-group-border-width) * -1);
}
@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-list-group-border-radius);
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-left-width: 0;
    border-top-width: var(--bs-list-group-border-width);
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    border-left-width: var(--bs-list-group-border-width);
    margin-left: calc(var(--bs-list-group-border-width) * -1);
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-list-group-border-radius);
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-left-width: 0;
    border-top-width: var(--bs-list-group-border-width);
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    border-left-width: var(--bs-list-group-border-width);
    margin-left: calc(var(--bs-list-group-border-width) * -1);
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-list-group-border-radius);
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-left-width: 0;
    border-top-width: var(--bs-list-group-border-width);
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    border-left-width: var(--bs-list-group-border-width);
    margin-left: calc(var(--bs-list-group-border-width) * -1);
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-list-group-border-radius);
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-left-width: 0;
    border-top-width: var(--bs-list-group-border-width);
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    border-left-width: var(--bs-list-group-border-width);
    margin-left: calc(var(--bs-list-group-border-width) * -1);
  }
}
@media (min-width: 1440px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-list-group-border-radius);
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-left-width: 0;
    border-top-width: var(--bs-list-group-border-width);
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    border-left-width: var(--bs-list-group-border-width);
    margin-left: calc(var(--bs-list-group-border-width) * -1);
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 var(--bs-list-group-border-width);
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}
.list-group-item-primary {
  background-color: #dbe1f6;
  color: #2c3f7e;
}
.list-group-item-primary.list-group-item-action:focus,
.list-group-item-primary.list-group-item-action:hover {
  background-color: #c5cbdd;
  color: #2c3f7e;
}
.list-group-item-primary.list-group-item-action.active {
  background-color: #2c3f7e;
  border-color: #2c3f7e;
  color: #fff;
}
.list-group-item-secondary {
  background-color: #edeff2;
  color: #626871;
}
.list-group-item-secondary.list-group-item-action:focus,
.list-group-item-secondary.list-group-item-action:hover {
  background-color: #d5d7da;
  color: #626871;
}
.list-group-item-secondary.list-group-item-action.active {
  background-color: #626871;
  border-color: #626871;
  color: #fff;
}
.list-group-item-success {
  background-color: #cee7ce;
  color: #075205;
}
.list-group-item-success.list-group-item-action:focus,
.list-group-item-success.list-group-item-action:hover {
  background-color: #b9d0b9;
  color: #075205;
}
.list-group-item-success.list-group-item-action.active {
  background-color: #075205;
  border-color: #075205;
  color: #fff;
}
.list-group-item-info {
  background-color: #cff4fc;
  color: #055160;
}
.list-group-item-info.list-group-item-action:focus,
.list-group-item-info.list-group-item-action:hover {
  background-color: #badce3;
  color: #055160;
}
.list-group-item-info.list-group-item-action.active {
  background-color: #055160;
  border-color: #055160;
  color: #fff;
}
.list-group-item-warning {
  background-color: #fff3cd;
  color: #664d03;
}
.list-group-item-warning.list-group-item-action:focus,
.list-group-item-warning.list-group-item-action:hover {
  background-color: #e6dbb9;
  color: #664d03;
}
.list-group-item-warning.list-group-item-action.active {
  background-color: #664d03;
  border-color: #664d03;
  color: #fff;
}
.list-group-item-danger {
  background-color: #fcdddb;
  color: #91342e;
}
.list-group-item-danger.list-group-item-action:focus,
.list-group-item-danger.list-group-item-action:hover {
  background-color: #e3c7c5;
  color: #91342e;
}
.list-group-item-danger.list-group-item-action.active {
  background-color: #91342e;
  border-color: #91342e;
  color: #fff;
}
.list-group-item-light {
  background-color: #fefefe;
  color: #636464;
}
.list-group-item-light.list-group-item-action:focus,
.list-group-item-light.list-group-item-action:hover {
  background-color: #e5e5e5;
  color: #636464;
}
.list-group-item-light.list-group-item-action.active {
  background-color: #636464;
  border-color: #636464;
  color: #fff;
}
.list-group-item-dark {
  background-color: #d3d3d4;
  color: #141619;
}
.list-group-item-dark.list-group-item-action:focus,
.list-group-item-dark.list-group-item-action:hover {
  background-color: #bebebf;
  color: #141619;
}
.list-group-item-dark.list-group-item-action.active {
  background-color: #141619;
  border-color: #141619;
  color: #fff;
}
.btn-close {
  background: transparent
    url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23637fa6'%3E%3Cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3E%3C/svg%3E")
    50%/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  box-sizing: content-box;
  color: #637fa6;
  height: 1em;
  opacity: 0.5;
  padding: 0.25em;
  width: 1em;
}
.btn-close:hover {
  color: #637fa6;
  opacity: 0.75;
  text-decoration: none;
}
.btn-close:focus {
  box-shadow: 0 0 0 0 rgba(74, 105, 210, 0.25);
  opacity: 1;
  outline: 0;
}
.btn-close.disabled,
.btn-close:disabled {
  opacity: 0.25;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}
/* .toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 1.5rem;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: ;
  --bs-toast-bg: hsla(0, 0%, 100%, 0.85);
  --bs-toast-border-width: 1px;
  --bs-toast-border-color: var(--bs-border-color-translucent);
  --bs-toast-border-radius: 0.375rem;
  --bs-toast-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-toast-header-color: #6c757d;
  --bs-toast-header-bg: hsla(0, 0%, 100%, 0.85);
  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);
  background-clip: padding-box;
  background-color: var(--bs-toast-bg);
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  border-radius: var(--bs-toast-border-radius);
  box-shadow: var(--bs-toast-box-shadow);
  color: var(--bs-toast-color);
  font-size: var(--bs-toast-font-size);
  max-width: 100%;
  pointer-events: auto;
  width: var(--bs-toast-max-width);
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}
.toast-container {
  --bs-toast-zindex: 1090;
  max-width: 100%;
  pointer-events: none;
  position: absolute;
  width: -moz-max-content;
  width: max-content;
  z-index: var(--bs-toast-zindex);
}
.toast-container > :not(:last-child) {
  margin-bottom: var(--bs-toast-spacing);
}
.toast-header {
  align-items: center;
  background-clip: padding-box;
  background-color: var(--bs-toast-header-bg);
  border-bottom: var(--bs-toast-border-width) solid
    var(--bs-toast-header-border-color);
  border-top-left-radius: calc(
    var(--bs-toast-border-radius) - var(--bs-toast-border-width)
  );
  border-top-right-radius: calc(
    var(--bs-toast-border-radius) - var(--bs-toast-border-width)
  );
  color: var(--bs-toast-header-color);
  display: flex;
  padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x);
}
.toast-header .btn-close {
  margin-left: var(--bs-toast-padding-x);
  margin-right: calc(var(--bs-toast-padding-x) * -0.5);
}
.toast-body {
  word-wrap: break-word;
  padding: var(--bs-toast-padding-x);
} */
.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 30rem;
  --bs-modal-padding: 1rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: #fff;
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 1px;
  --bs-modal-border-radius: 0.75rem;
  --bs-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-modal-inner-border-radius: calc(0.75rem - 1px);
  --bs-modal-header-padding-x: 1rem;
  --bs-modal-header-padding-y: 1rem;
  --bs-modal-header-padding: 1rem 1rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 1px;
  --bs-modal-title-line-height: 1.375;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 1px;
  display: none;
  height: 100%;
  left: 0;
  outline: 0;
  overflow-x: hidden;
  overflow-y: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: var(--bs-modal-zindex);
}
.modal-dialog {
  margin: var(--bs-modal-margin);
  pointer-events: none;
  position: relative;
  width: auto;
}
.modal.fade .modal-dialog {
  transform: scale(0.8);
  transition: transform 0.2s ease-out;
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}
.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.modal-dialog-centered {
  align-items: center;
  display: flex;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-content {
  background-clip: padding-box;
  background-color: var(--bs-modal-bg);
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  color: var(--bs-modal-color);
  display: flex;
  flex-direction: column;
  outline: 0;
  pointer-events: auto;
  position: relative;
  width: 100%;
}
.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000;
  --bs-backdrop-opacity: 0.6;
  background-color: var(--bs-backdrop-bg);
  height: 100vh;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
  z-index: var(--bs-backdrop-zindex);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}
.modal-header {
  align-items: center;
  border-bottom: var(--bs-modal-header-border-width) solid
    var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
  display: flex;
  flex-shrink: 0;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
}
.modal-header .btn-close {
  margin: calc(var(--bs-modal-header-padding-y) * -0.5)
    calc(var(--bs-modal-header-padding-x) * -0.5)
    calc(var(--bs-modal-header-padding-y) * -0.5) auto;
  padding: calc(var(--bs-modal-header-padding-y) * 0.5)
    calc(var(--bs-modal-header-padding-x) * 0.5);
}
.modal-title {
  line-height: var(--bs-modal-title-line-height);
  margin-bottom: 0;
}
.modal-body {
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
  position: relative;
}
.modal-footer {
  align-items: center;
  background-color: var(--bs-modal-footer-bg);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-top: var(--bs-modal-footer-border-width) solid
    var(--bs-modal-footer-border-color);
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * 0.5);
}
.modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * 0.5);
}
@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  .modal-dialog {
    margin-left: auto;
    margin-right: auto;
    max-width: var(--bs-modal-width);
  }
  .modal-sm {
    --bs-modal-width: 20rem;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    --bs-modal-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    --bs-modal-width: 1140px;
  }
}
.modal-fullscreen {
  height: 100%;
  margin: 0;
  max-width: none;
  width: 100vw;
}
.modal-fullscreen .modal-content {
  border: 0;
  border-radius: 0;
  height: 100%;
}
.modal-fullscreen .modal-footer,
.modal-fullscreen .modal-header {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}
@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    height: 100%;
    margin: 0;
    max-width: none;
    width: 100vw;
  }
  .modal-fullscreen-sm-down .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .modal-fullscreen-sm-down .modal-footer,
  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    height: 100%;
    margin: 0;
    max-width: none;
    width: 100vw;
  }
  .modal-fullscreen-md-down .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .modal-fullscreen-md-down .modal-footer,
  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    height: 100%;
    margin: 0;
    max-width: none;
    width: 100vw;
  }
  .modal-fullscreen-lg-down .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .modal-fullscreen-lg-down .modal-footer,
  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    height: 100%;
    margin: 0;
    max-width: none;
    width: 100vw;
  }
  .modal-fullscreen-xl-down .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .modal-fullscreen-xl-down .modal-footer,
  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1439.98px) {
  .modal-fullscreen-xxl-down {
    height: 100%;
    margin: 0;
    max-width: none;
    width: 100vw;
  }
  .modal-fullscreen-xxl-down .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
  }
  .modal-fullscreen-xxl-down .modal-footer,
  .modal-fullscreen-xxl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 0.5rem;
  --bs-tooltip-padding-y: 0.25rem;
  --bs-tooltip-margin: ;
  --bs-tooltip-font-size: 0.75rem;
  --bs-tooltip-color: #fff;
  --bs-tooltip-bg: #000;
  --bs-tooltip-border-radius: 0.375rem;
  --bs-tooltip-opacity: 0.9;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  word-wrap: break-word;
  display: block;
  font-family: Nunito, sans-serif;
  font-size: var(--bs-tooltip-font-size);
  font-style: normal;
  font-weight: 400;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.375;
  margin: var(--bs-tooltip-margin);
  opacity: 0;
  padding: var(--bs-tooltip-arrow-height);
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  z-index: var(--bs-tooltip-zindex);
}
.tooltip.show {
  opacity: var(--bs-tooltip-opacity);
}
.tooltip .tooltip-arrow {
  display: block;
  height: var(--bs-tooltip-arrow-height);
  width: var(--bs-tooltip-arrow-width);
}
.tooltip .tooltip-arrow:before {
  border-color: transparent;
  border-style: solid;
  content: "";
  position: absolute;
}
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow,
.bs-tooltip-top .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow:before,
.bs-tooltip-top .tooltip-arrow:before {
  border-top-color: var(--bs-tooltip-bg);
  border-width: var(--bs-tooltip-arrow-height)
    calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  top: -1px;
}
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow,
.bs-tooltip-end .tooltip-arrow {
  height: var(--bs-tooltip-arrow-width);
  left: 0;
  width: var(--bs-tooltip-arrow-height);
}
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow:before,
.bs-tooltip-end .tooltip-arrow:before {
  border-right-color: var(--bs-tooltip-bg);
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5)
    var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  right: -1px;
}
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow,
.bs-tooltip-bottom .tooltip-arrow {
  top: 0;
}
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow:before,
.bs-tooltip-bottom .tooltip-arrow:before {
  border-bottom-color: var(--bs-tooltip-bg);
  border-width: 0 calc(var(--bs-tooltip-arrow-width) * 0.5)
    var(--bs-tooltip-arrow-height);
  bottom: -1px;
}
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow,
.bs-tooltip-start .tooltip-arrow {
  height: var(--bs-tooltip-arrow-width);
  right: 0;
  width: var(--bs-tooltip-arrow-height);
}
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow:before,
.bs-tooltip-start .tooltip-arrow:before {
  border-left-color: var(--bs-tooltip-bg);
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) 0
    calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  left: -1px;
}
.tooltip-inner {
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius);
  color: var(--bs-tooltip-color);
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  text-align: center;
}
.popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 0.75rem;
  --bs-popover-bg: #fff;
  --bs-popover-border-width: 1px;
  --bs-popover-border-color: var(--bs-border-color-translucent);
  --bs-popover-border-radius: 0.5rem;
  --bs-popover-inner-border-radius: calc(0.5rem - 1px);
  --bs-popover-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-popover-header-padding-x: 1rem;
  --bs-popover-header-padding-y: 0.5rem;
  --bs-popover-header-font-size: 0.875rem;
  --bs-popover-header-color: ;
  --bs-popover-header-bg: #f0f0f0;
  --bs-popover-body-padding-x: 1rem;
  --bs-popover-body-padding-y: 1rem;
  --bs-popover-body-color: #141735;
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  word-wrap: break-word;
  background-clip: padding-box;
  background-color: var(--bs-popover-bg);
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius);
  display: block;
  font-family: Nunito, sans-serif;
  font-size: var(--bs-popover-font-size);
  font-style: normal;
  font-weight: 400;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.375;
  max-width: var(--bs-popover-max-width);
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  z-index: var(--bs-popover-zindex);
}
.popover .popover-arrow {
  display: block;
  height: var(--bs-popover-arrow-height);
  width: var(--bs-popover-arrow-width);
}
.popover .popover-arrow:after,
.popover .popover-arrow:before {
  border: 0 solid transparent;
  content: "";
  display: block;
  position: absolute;
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow,
.bs-popover-top > .popover-arrow {
  bottom: calc(
    (var(--bs-popover-arrow-height)) * -1 - var(--bs-popover-border-width)
  );
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow:after,
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow:before,
.bs-popover-top > .popover-arrow:after,
.bs-popover-top > .popover-arrow:before {
  border-width: var(--bs-popover-arrow-height)
    calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow:before,
.bs-popover-top > .popover-arrow:before {
  border-top-color: var(--bs-popover-arrow-border);
  bottom: 0;
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow:after,
.bs-popover-top > .popover-arrow:after {
  border-top-color: var(--bs-popover-bg);
  bottom: var(--bs-popover-border-width);
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow,
.bs-popover-end > .popover-arrow {
  height: var(--bs-popover-arrow-width);
  left: calc(
    (var(--bs-popover-arrow-height)) * -1 - var(--bs-popover-border-width)
  );
  width: var(--bs-popover-arrow-height);
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow:after,
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow:before,
.bs-popover-end > .popover-arrow:after,
.bs-popover-end > .popover-arrow:before {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5)
    var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow:before,
.bs-popover-end > .popover-arrow:before {
  border-right-color: var(--bs-popover-arrow-border);
  left: 0;
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow:after,
.bs-popover-end > .popover-arrow:after {
  border-right-color: var(--bs-popover-bg);
  left: var(--bs-popover-border-width);
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow,
.bs-popover-bottom > .popover-arrow {
  top: calc(
    (var(--bs-popover-arrow-height)) * -1 - var(--bs-popover-border-width)
  );
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow:after,
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow:before,
.bs-popover-bottom > .popover-arrow:after,
.bs-popover-bottom > .popover-arrow:before {
  border-width: 0 calc(var(--bs-popover-arrow-width) * 0.5)
    var(--bs-popover-arrow-height);
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow:before,
.bs-popover-bottom > .popover-arrow:before {
  border-bottom-color: var(--bs-popover-arrow-border);
  top: 0;
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow:after,
.bs-popover-bottom > .popover-arrow:after {
  border-bottom-color: var(--bs-popover-bg);
  top: var(--bs-popover-border-width);
}
.bs-popover-auto[data-popper-placement^="bottom"] .popover-header:before,
.bs-popover-bottom .popover-header:before {
  border-bottom: var(--bs-popover-border-width) solid
    var(--bs-popover-header-bg);
  content: "";
  display: block;
  left: 50%;
  margin-left: calc(var(--bs-popover-arrow-width) * -0.5);
  position: absolute;
  top: 0;
  width: var(--bs-popover-arrow-width);
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow,
.bs-popover-start > .popover-arrow {
  height: var(--bs-popover-arrow-width);
  right: calc(
    (var(--bs-popover-arrow-height)) * -1 - var(--bs-popover-border-width)
  );
  width: var(--bs-popover-arrow-height);
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow:after,
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow:before,
.bs-popover-start > .popover-arrow:after,
.bs-popover-start > .popover-arrow:before {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) 0
    calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow:before,
.bs-popover-start > .popover-arrow:before {
  border-left-color: var(--bs-popover-arrow-border);
  right: 0;
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow:after,
.bs-popover-start > .popover-arrow:after {
  border-left-color: var(--bs-popover-bg);
  right: var(--bs-popover-border-width);
}
.popover-header {
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid
    var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
  color: var(--bs-popover-header-color);
  font-size: var(--bs-popover-header-font-size);
  margin-bottom: 0;
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
}
.popover-header:empty {
  display: none;
}
.popover-body {
  color: var(--bs-popover-body-color);
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
}
.carousel {
  position: relative;
}
.carousel.pointer-event {
  touch-action: pan-y;
}
.carousel-inner {
  overflow: hidden;
  position: relative;
  width: 100%;
}
.carousel-inner:after {
  clear: both;
  content: "";
  display: block;
}
.carousel-item {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: none;
  float: left;
  margin-right: -100%;
  position: relative;
  transition: transform 0.6s ease-in-out;
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}
.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
  display: block;
}
.active.carousel-item-end,
.carousel-item-next:not(.carousel-item-start) {
  transform: translateX(100%);
}
.active.carousel-item-start,
.carousel-item-prev:not(.carousel-item-end) {
  transform: translateX(-100%);
}
.carousel-fade .carousel-item {
  opacity: 0;
  transform: none;
  transition-property: opacity;
}
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end,
.carousel-fade .carousel-item.active {
  opacity: 1;
  z-index: 1;
}
.carousel-fade .active.carousel-item-end,
.carousel-fade .active.carousel-item-start {
  opacity: 0;
  transition: opacity 0s 0.6s;
  z-index: 0;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-end,
  .carousel-fade .active.carousel-item-start {
    transition: none;
  }
}
.carousel-control-next,
.carousel-control-prev {
  align-items: center;
  background: none;
  border: 0;
  bottom: 0;
  color: #fff;
  display: flex;
  justify-content: center;
  opacity: 0.5;
  padding: 0;
  position: absolute;
  text-align: center;
  top: 0;
  transition: opacity 0.15s ease;
  width: 15%;
  z-index: 1;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-next,
  .carousel-control-prev {
    transition: none;
  }
}
.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
  color: #fff;
  opacity: 0.9;
  outline: 0;
  text-decoration: none;
}
.carousel-control-prev {
  left: 0;
}
.carousel-control-next {
  right: 0;
}
.carousel-control-next-icon,
.carousel-control-prev-icon {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: inline-block;
  height: 2rem;
  width: 2rem;
}
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3E%3Cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3E%3C/svg%3E");
}
.carousel-control-next-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3E%3Cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
}
.carousel-indicators {
  bottom: 0;
  display: flex;
  justify-content: center;
  left: 0;
  list-style: none;
  margin-bottom: 1rem;
  margin-left: 15%;
  margin-right: 15%;
  padding: 0;
  position: absolute;
  right: 0;
  z-index: 2;
}
.carousel-indicators [data-bs-target] {
  background-clip: padding-box;
  background-color: #fff;
  border: 0;
  border-bottom: 10px solid transparent;
  border-top: 10px solid transparent;
  box-sizing: content-box;
  cursor: pointer;
  flex: 0 1 auto;
  height: 3px;
  margin-left: 3px;
  margin-right: 3px;
  opacity: 0.5;
  padding: 0;
  text-indent: -999px;
  transition: opacity 0.6s ease;
  width: 30px;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}
.carousel-caption {
  bottom: 1.25rem;
  color: #fff;
  left: 15%;
  padding-bottom: 1.25rem;
  padding-top: 1.25rem;
  position: absolute;
  right: 15%;
  text-align: center;
}
.carousel-dark .carousel-control-next-icon,
.carousel-dark .carousel-control-prev-icon {
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}
.spinner-border,
.spinner-grow {
  animation: var(--bs-spinner-animation-speed) linear infinite
    var(--bs-spinner-animation-name);
  border-radius: 50%;
  display: inline-block;
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  width: var(--bs-spinner-width);
}
@keyframes spinner-border {
  to {
    transform: rotate(1turn);
  }
}
.spinner-border {
  --bs-spinner-width: 1em;
  --bs-spinner-height: 1em;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.25em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-border;
  border-right-color: currentcolor;
  border: var(--bs-spinner-border-width) solid;
  border-right: var(--bs-spinner-border-width) solid transparent;
}
.spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.2em;
}
@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  --bs-spinner-width: 1em;
  --bs-spinner-height: 1em;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0;
}
.spinner-grow-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    --bs-spinner-animation-speed: 1.5s;
  }
}
.offcanvas,
.offcanvas-lg,
.offcanvas-md,
.offcanvas-sm,
.offcanvas-xl,
.offcanvas-xxl {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: ;
  --bs-offcanvas-bg: #fff;
  --bs-offcanvas-border-width: 1px;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
@media (max-width: 575.98px) {
  .offcanvas-sm {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: transform 0.3s ease-in-out;
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex);
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    border-right: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-sm.offcanvas-end {
    border-left: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-sm.offcanvas-top {
    border-bottom: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    top: 0;
    transform: translateY(-100%);
  }
  .offcanvas-sm.offcanvas-bottom,
  .offcanvas-sm.offcanvas-top {
    height: var(--bs-offcanvas-height);
    left: 0;
    max-height: 100%;
    right: 0;
  }
  .offcanvas-sm.offcanvas-bottom {
    border-top: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-sm.show:not(.hiding),
  .offcanvas-sm.showing {
    transform: none;
  }
  .offcanvas-sm.hiding,
  .offcanvas-sm.show,
  .offcanvas-sm.showing {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    background-color: transparent !important;
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: transform 0.3s ease-in-out;
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex);
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    border-right: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-md.offcanvas-end {
    border-left: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-md.offcanvas-top {
    border-bottom: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    top: 0;
    transform: translateY(-100%);
  }
  .offcanvas-md.offcanvas-bottom,
  .offcanvas-md.offcanvas-top {
    height: var(--bs-offcanvas-height);
    left: 0;
    max-height: 100%;
    right: 0;
  }
  .offcanvas-md.offcanvas-bottom {
    border-top: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-md.show:not(.hiding),
  .offcanvas-md.showing {
    transform: none;
  }
  .offcanvas-md.hiding,
  .offcanvas-md.show,
  .offcanvas-md.showing {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    background-color: transparent !important;
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: transform 0.3s ease-in-out;
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex);
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    border-right: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-lg.offcanvas-end {
    border-left: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-lg.offcanvas-top {
    border-bottom: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    top: 0;
    transform: translateY(-100%);
  }
  .offcanvas-lg.offcanvas-bottom,
  .offcanvas-lg.offcanvas-top {
    height: var(--bs-offcanvas-height);
    left: 0;
    max-height: 100%;
    right: 0;
  }
  .offcanvas-lg.offcanvas-bottom {
    border-top: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-lg.show:not(.hiding),
  .offcanvas-lg.showing {
    transform: none;
  }
  .offcanvas-lg.hiding,
  .offcanvas-lg.show,
  .offcanvas-lg.showing {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    background-color: transparent !important;
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: transform 0.3s ease-in-out;
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex);
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .offcanvas-xl.offcanvas-start {
    border-right: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-xl.offcanvas-end {
    border-left: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-xl.offcanvas-top {
    border-bottom: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    top: 0;
    transform: translateY(-100%);
  }
  .offcanvas-xl.offcanvas-bottom,
  .offcanvas-xl.offcanvas-top {
    height: var(--bs-offcanvas-height);
    left: 0;
    max-height: 100%;
    right: 0;
  }
  .offcanvas-xl.offcanvas-bottom {
    border-top: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-xl.show:not(.hiding),
  .offcanvas-xl.showing {
    transform: none;
  }
  .offcanvas-xl.hiding,
  .offcanvas-xl.show,
  .offcanvas-xl.showing {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    background-color: transparent !important;
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
@media (max-width: 1439.98px) {
  .offcanvas-xxl {
    background-clip: padding-box;
    background-color: var(--bs-offcanvas-bg);
    bottom: 0;
    color: var(--bs-offcanvas-color);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    outline: 0;
    position: fixed;
    transition: transform 0.3s ease-in-out;
    visibility: hidden;
    z-index: var(--bs-offcanvas-zindex);
  }
}
@media (max-width: 1439.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1439.98px) {
  .offcanvas-xxl.offcanvas-start {
    border-right: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    left: 0;
    top: 0;
    transform: translateX(-100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-xxl.offcanvas-end {
    border-left: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    right: 0;
    top: 0;
    transform: translateX(100%);
    width: var(--bs-offcanvas-width);
  }
  .offcanvas-xxl.offcanvas-top {
    border-bottom: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    top: 0;
    transform: translateY(-100%);
  }
  .offcanvas-xxl.offcanvas-bottom,
  .offcanvas-xxl.offcanvas-top {
    height: var(--bs-offcanvas-height);
    left: 0;
    max-height: 100%;
    right: 0;
  }
  .offcanvas-xxl.offcanvas-bottom {
    border-top: var(--bs-offcanvas-border-width) solid
      var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-xxl.show:not(.hiding),
  .offcanvas-xxl.showing {
    transform: none;
  }
  .offcanvas-xxl.hiding,
  .offcanvas-xxl.show,
  .offcanvas-xxl.showing {
    visibility: visible;
  }
}
@media (min-width: 1440px) {
  .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .offcanvas-xxl .offcanvas-body {
    background-color: transparent !important;
    display: flex;
    flex-grow: 0;
    overflow-y: visible;
    padding: 0;
  }
}
.offcanvas {
  background-clip: padding-box;
  background-color: var(--bs-offcanvas-bg);
  bottom: 0;
  color: var(--bs-offcanvas-color);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  outline: 0;
  position: fixed;
  transition: transform 0.3s ease-in-out;
  visibility: hidden;
  z-index: var(--bs-offcanvas-zindex);
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  border-right: var(--bs-offcanvas-border-width) solid
    var(--bs-offcanvas-border-color);
  left: 0;
  top: 0;
  transform: translateX(-100%);
  width: var(--bs-offcanvas-width);
}
.offcanvas.offcanvas-end {
  border-left: var(--bs-offcanvas-border-width) solid
    var(--bs-offcanvas-border-color);
  right: 0;
  top: 0;
  transform: translateX(100%);
  width: var(--bs-offcanvas-width);
}
.offcanvas.offcanvas-top {
  border-bottom: var(--bs-offcanvas-border-width) solid
    var(--bs-offcanvas-border-color);
  top: 0;
  transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom,
.offcanvas.offcanvas-top {
  height: var(--bs-offcanvas-height);
  left: 0;
  max-height: 100%;
  right: 0;
}
.offcanvas.offcanvas-bottom {
  border-top: var(--bs-offcanvas-border-width) solid
    var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.offcanvas.show:not(.hiding),
.offcanvas.showing {
  transform: none;
}
.offcanvas.hiding,
.offcanvas.show,
.offcanvas.showing {
  visibility: visible;
}
.offcanvas-backdrop {
  background-color: #000;
  height: 100vh;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
  z-index: 1040;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.6;
}
.offcanvas-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  margin-bottom: calc(var(--bs-offcanvas-padding-y) * -0.5);
  margin-right: calc(var(--bs-offcanvas-padding-x) * -0.5);
  margin-top: calc(var(--bs-offcanvas-padding-y) * -0.5);
  padding: calc(var(--bs-offcanvas-padding-y) * 0.5)
    calc(var(--bs-offcanvas-padding-x) * 0.5);
}
.offcanvas-title {
  line-height: 1.375;
  margin-bottom: 0;
}
.offcanvas-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.placeholder {
  background-color: currentcolor;
  cursor: wait;
  display: inline-block;
  min-height: 1em;
  opacity: 0.5;
  vertical-align: middle;
}
.placeholder.btn:before {
  content: "";
  display: inline-block;
}
.placeholder-xs {
  min-height: 0.6em;
}
.placeholder-sm {
  min-height: 0.8em;
}
.placeholder-lg {
  min-height: 1.2em;
}
.placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite;
}
@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  animation: placeholder-wave 2s linear infinite;
  -webkit-mask-image: linear-gradient(
    130deg,
    #000 55%,
    rgba(0, 0, 0, 0.8) 75%,
    #000 95%
  );
  mask-image: linear-gradient(
    130deg,
    #000 55%,
    rgba(0, 0, 0, 0.8) 75%,
    #000 95%
  );
  -webkit-mask-size: 200% 100%;
  mask-size: 200% 100%;
}
@keyframes placeholder-wave {
  to {
    -webkit-mask-position: -200% 0;
    mask-position: -200% 0;
  }
}
.clearfix:after {
  clear: both;
  content: "";
  display: block;
}
.text-bg-primary {
  background-color: RGBA(74, 105, 210, var(--bs-bg-opacity, 1)) !important;
  color: #fff !important;
}
.text-bg-secondary {
  background-color: RGBA(164, 173, 189, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.text-bg-success {
  background-color: RGBA(12, 136, 9, var(--bs-bg-opacity, 1)) !important;
  color: #fff !important;
}
.text-bg-info {
  background-color: RGBA(13, 202, 240, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.text-bg-warning {
  background-color: RGBA(255, 193, 7, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.text-bg-danger {
  background-color: RGBA(242, 86, 76, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.text-bg-light {
  background-color: RGBA(248, 249, 250, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.text-bg-dark {
  background-color: RGBA(33, 37, 41, var(--bs-bg-opacity, 1)) !important;
  color: #fff !important;
}
.link-primary {
  color: #4a69d2 !important;
}
.link-primary:focus,
.link-primary:hover {
  color: #3b54a8 !important;
}
.link-secondary {
  color: #a4adbd !important;
}
.link-secondary:focus,
.link-secondary:hover {
  color: #b6bdca !important;
}
.link-success {
  color: #0c8809 !important;
}
.link-success:focus,
.link-success:hover {
  color: #0a6d07 !important;
}
.link-info {
  color: #0dcaf0 !important;
}
.link-info:focus,
.link-info:hover {
  color: #3dd5f3 !important;
}
.link-warning {
  color: #ffc107 !important;
}
.link-warning:focus,
.link-warning:hover {
  color: #ffcd39 !important;
}
.link-danger {
  color: #f2564c !important;
}
.link-danger:focus,
.link-danger:hover {
  color: #f57870 !important;
}
.link-light {
  color: #f8f9fa !important;
}
.link-light:focus,
.link-light:hover {
  color: #f9fafb !important;
}
.link-dark {
  color: #212529 !important;
}
.link-dark:focus,
.link-dark:hover {
  color: #1a1e21 !important;
}
.ratio {
  position: relative;
  width: 100%;
}
.ratio:before {
  content: "";
  display: block;
  padding-top: var(--bs-aspect-ratio);
}
.ratio > * {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}
.ratio-4x3 {
  --bs-aspect-ratio: 75%;
}
.ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}
.ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}
.fixed-top {
  top: 0;
}
.fixed-bottom,
.fixed-top {
  left: 0;
  position: fixed;
  right: 0;
  z-index: 1030;
}
.fixed-bottom {
  bottom: 0;
}
.sticky-top {
  top: 0;
}
.sticky-bottom,
.sticky-top {
  position: sticky;
  z-index: 1020;
}
.sticky-bottom {
  bottom: 0;
}
@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-sm-bottom {
    bottom: 0;
    position: sticky;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-md-bottom {
    bottom: 0;
    position: sticky;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-lg-bottom {
    bottom: 0;
    position: sticky;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xl-bottom {
    bottom: 0;
    position: sticky;
    z-index: 1020;
  }
}
@media (min-width: 1440px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xxl-bottom {
    bottom: 0;
    position: sticky;
    z-index: 1020;
  }
}
.hstack {
  align-items: center;
  flex-direction: row;
}
.hstack,
.vstack {
  align-self: stretch;
  display: flex;
}
.vstack {
  flex: 1 1 auto;
  flex-direction: column;
}
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  clip: rect(0, 0, 0, 0) !important;
  border: 0 !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  white-space: nowrap !important;
  width: 1px !important;
}
.stretched-link:after {
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vr {
  align-self: stretch;
  background-color: currentcolor;
  display: inline-block;
  min-height: 1em;
  opacity: 0.1;
  width: 1px;
}
.align-baseline {
  vertical-align: baseline !important;
}
.align-top {
  vertical-align: top !important;
}
.align-middle {
  vertical-align: middle !important;
}
.align-bottom {
  vertical-align: bottom !important;
}
.align-text-bottom {
  vertical-align: text-bottom !important;
}
.align-text-top {
  vertical-align: text-top !important;
}
.float-start {
  float: left !important;
}
.float-end {
  float: right !important;
}
.float-none {
  float: none !important;
}
.opacity-0 {
  opacity: 0 !important;
}
.opacity-25 {
  opacity: 0.25 !important;
}
.opacity-50 {
  opacity: 0.5 !important;
}
.opacity-75 {
  opacity: 0.75 !important;
}
.opacity-100 {
  opacity: 1 !important;
}
.overflow-auto {
  overflow: auto !important;
}
.overflow-hidden {
  overflow: hidden !important;
}
.overflow-visible {
  overflow: visible !important;
}
.overflow-scroll {
  overflow: scroll !important;
}
.d-inline {
  display: inline !important;
}
.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-grid {
  display: grid !important;
}
.d-table {
  display: table !important;
}
.d-table-row {
  display: table-row !important;
}
.d-table-cell {
  display: table-cell !important;
}
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}
.d-none {
  display: none !important;
}
.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
.shadow-none {
  box-shadow: none !important;
}
.position-static {
  position: static !important;
}
.position-relative {
  position: relative !important;
}
.position-absolute {
  position: absolute !important;
}
.position-fixed {
  position: fixed !important;
}
.position-sticky {
  position: sticky !important;
}
.top-0 {
  top: 0 !important;
}
.top-50 {
  top: 50% !important;
}
.top-100 {
  top: 100% !important;
}
.bottom-0 {
  bottom: 0 !important;
}
.bottom-50 {
  bottom: 50% !important;
}
.bottom-100 {
  bottom: 100% !important;
}
.start-0 {
  left: 0 !important;
}
.start-50 {
  left: 50% !important;
}
.start-100 {
  left: 100% !important;
}
.end-0 {
  right: 0 !important;
}
.end-50 {
  right: 50% !important;
}
.end-100 {
  right: 100% !important;
}
.translate-middle {
  transform: translate(-50%, -50%) !important;
}
.translate-middle-x {
  transform: translateX(-50%) !important;
}
.translate-middle-y {
  transform: translateY(-50%) !important;
}
.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.border-0 {
  border: 0 !important;
}
.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style)
    var(--bs-border-color) !important;
}
.border-top-0 {
  border-top: 0 !important;
}
.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style)
    var(--bs-border-color) !important;
}
.border-end-0 {
  border-right: 0 !important;
}
.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style)
    var(--bs-border-color) !important;
}
.border-bottom-0 {
  border-bottom: 0 !important;
}
.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style)
    var(--bs-border-color) !important;
}
.border-start-0 {
  border-left: 0 !important;
}
.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(
    var(--bs-primary-rgb),
    var(--bs-border-opacity)
  ) !important;
}
.border-secondary {
  --bs-border-opacity: 1;
  border-color: rgba(
    var(--bs-secondary-rgb),
    var(--bs-border-opacity)
  ) !important;
}
.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(
    var(--bs-success-rgb),
    var(--bs-border-opacity)
  ) !important;
}
.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}
.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(
    var(--bs-warning-rgb),
    var(--bs-border-opacity)
  ) !important;
}
.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}
.border-light {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
}
.border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}
.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}
.border-1 {
  --bs-border-width: 1px;
}
.border-2 {
  --bs-border-width: 2px;
}
.border-3 {
  --bs-border-width: 3px;
}
.border-4 {
  --bs-border-width: 4px;
}
.border-5 {
  --bs-border-width: 5px;
}
.border-opacity-10 {
  --bs-border-opacity: 0.1;
}
.border-opacity-25 {
  --bs-border-opacity: 0.25;
}
.border-opacity-50 {
  --bs-border-opacity: 0.5;
}
.border-opacity-75 {
  --bs-border-opacity: 0.75;
}
.border-opacity-100 {
  --bs-border-opacity: 1;
}
.w-25 {
  width: 25% !important;
}
.w-50 {
  width: 50% !important;
}
.w-75 {
  width: 75% !important;
}
.w-100 {
  width: 100% !important;
}
.w-auto {
  width: auto !important;
}
.mw-100 {
  max-width: 100% !important;
}
.vw-100 {
  width: 100vw !important;
}
.min-vw-100 {
  min-width: 100vw !important;
}
.h-25 {
  height: 25% !important;
}
.h-50 {
  height: 50% !important;
}
.h-75 {
  height: 75% !important;
}
.h-100 {
  height: 100% !important;
}
.h-auto {
  height: auto !important;
}
.mh-100 {
  max-height: 100% !important;
}
.vh-100 {
  height: 100vh !important;
}
.min-vh-100 {
  min-height: 100vh !important;
}
.flex-fill {
  flex: 1 1 auto !important;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-grow-0 {
  flex-grow: 0 !important;
}
.flex-grow-1 {
  flex-grow: 1 !important;
}
.flex-shrink-0 {
  flex-shrink: 0 !important;
}
.flex-shrink-1 {
  flex-shrink: 1 !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.justify-content-evenly {
  justify-content: space-evenly !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
.align-content-start {
  align-content: flex-start !important;
}
.align-content-end {
  align-content: flex-end !important;
}
.align-content-center {
  align-content: center !important;
}
.align-content-between {
  align-content: space-between !important;
}
.align-content-around {
  align-content: space-around !important;
}
.align-content-stretch {
  align-content: stretch !important;
}
.align-self-auto {
  align-self: auto !important;
}
.align-self-start {
  align-self: flex-start !important;
}
.align-self-end {
  align-self: flex-end !important;
}
.align-self-center {
  align-self: center !important;
}
.align-self-baseline {
  align-self: baseline !important;
}
.align-self-stretch {
  align-self: stretch !important;
}
.order-first {
  order: -1 !important;
}
.order-0 {
  order: 0 !important;
}
.order-1 {
  order: 1 !important;
}
.order-2 {
  order: 2 !important;
}
.order-3 {
  order: 3 !important;
}
.order-4 {
  order: 4 !important;
}
.order-5 {
  order: 5 !important;
}
.order-last {
  order: 6 !important;
}
.m-0 {
  margin: 0 !important;
}
.m-1 {
  margin: 0.25rem !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.m-3 {
  margin: 1rem !important;
}
.m-4 {
  margin: 1.5rem !important;
}
.m-5 {
  margin: 3rem !important;
}
.m-auto {
  margin: auto !important;
}
.mx-0 {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.mx-1 {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
}
.mx-2 {
  margin-left: 0.5rem !important;
  margin-right: 0.5rem !important;
}
.mx-3 {
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}
.mx-4 {
  margin-left: 1.5rem !important;
  margin-right: 1.5rem !important;
}
.mx-5 {
  margin-left: 3rem !important;
  margin-right: 3rem !important;
}
.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}
.my-0 {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}
.my-1 {
  margin-bottom: 0.25rem !important;
  margin-top: 0.25rem !important;
}
.my-2 {
  margin-bottom: 0.5rem !important;
  margin-top: 0.5rem !important;
}
.my-3 {
  margin-bottom: 1rem !important;
  margin-top: 1rem !important;
}
.my-4 {
  margin-bottom: 1.5rem !important;
  margin-top: 1.5rem !important;
}
.my-5 {
  margin-bottom: 3rem !important;
  margin-top: 3rem !important;
}
.my-auto {
  margin-bottom: auto !important;
  margin-top: auto !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mt-1 {
  margin-top: 0.25rem !important;
}
.mt-2 {
  margin-top: 0.5rem !important;
}
.mt-3 {
  margin-top: 1rem !important;
}
.mt-4 {
  margin-top: 1.5rem !important;
}
.mt-5 {
  margin-top: 3rem !important;
}
.mt-auto {
  margin-top: auto !important;
}
.me-0 {
  margin-right: 0 !important;
}
.me-1 {
  margin-right: 0.25rem !important;
}
.me-2 {
  margin-right: 0.5rem !important;
}
.me-3 {
  margin-right: 1rem !important;
}
.me-4 {
  margin-right: 1.5rem !important;
}
.me-5 {
  margin-right: 3rem !important;
}
.me-auto {
  margin-right: auto !important;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mb-1 {
  margin-bottom: 0.25rem !important;
}
.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mb-3 {
  margin-bottom: 1rem !important;
}
.mb-4 {
  margin-bottom: 1.5rem !important;
}
.mb-5 {
  margin-bottom: 3rem !important;
}
.mb-auto {
  margin-bottom: auto !important;
}
.ms-0 {
  margin-left: 0 !important;
}
.ms-1 {
  margin-left: 0.25rem !important;
}
.ms-2 {
  margin-left: 0.5rem !important;
}
.ms-3 {
  margin-left: 1rem !important;
}
.ms-4 {
  margin-left: 1.5rem !important;
}
.ms-5 {
  margin-left: 3rem !important;
}
.ms-auto {
  margin-left: auto !important;
}
.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: 0.25rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.p-3 {
  padding: 1rem !important;
}
.p-4 {
  padding: 1.5rem !important;
}
.p-5 {
  padding: 3rem !important;
}
.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.px-1 {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}
.px-2 {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}
.px-3 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.px-4 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}
.px-5 {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}
.py-0 {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
.py-1 {
  padding-bottom: 0.25rem !important;
  padding-top: 0.25rem !important;
}
.py-2 {
  padding-bottom: 0.5rem !important;
  padding-top: 0.5rem !important;
}
.py-3 {
  padding-bottom: 1rem !important;
  padding-top: 1rem !important;
}
.py-4 {
  padding-bottom: 1.5rem !important;
  padding-top: 1.5rem !important;
}
.py-5 {
  padding-bottom: 3rem !important;
  padding-top: 3rem !important;
}
.pt-0 {
  padding-top: 0 !important;
}
.pt-1 {
  padding-top: 0.25rem !important;
}
.pt-2 {
  padding-top: 0.5rem !important;
}
.pt-3 {
  padding-top: 1rem !important;
}
.pt-4 {
  padding-top: 1.5rem !important;
}
.pt-5 {
  padding-top: 3rem !important;
}
.pe-0 {
  padding-right: 0 !important;
}
.pe-1 {
  padding-right: 0.25rem !important;
}
.pe-2 {
  padding-right: 0.5rem !important;
}
.pe-3 {
  padding-right: 1rem !important;
}
.pe-4 {
  padding-right: 1.5rem !important;
}
.pe-5 {
  padding-right: 3rem !important;
}
.pb-0 {
  padding-bottom: 0 !important;
}
.pb-1 {
  padding-bottom: 0.25rem !important;
}
.pb-2 {
  padding-bottom: 0.5rem !important;
}
.pb-3 {
  padding-bottom: 1rem !important;
}
.pb-4 {
  padding-bottom: 1.5rem !important;
}
.pb-5 {
  padding-bottom: 3rem !important;
}
.ps-0 {
  padding-left: 0 !important;
}
.ps-1 {
  padding-left: 0.25rem !important;
}
.ps-2 {
  padding-left: 0.5rem !important;
}
.ps-3 {
  padding-left: 1rem !important;
}
.ps-4 {
  padding-left: 1.5rem !important;
}
.ps-5 {
  padding-left: 3rem !important;
}
.gap-0 {
  gap: 0 !important;
}
.gap-1 {
  gap: 0.25rem !important;
}
.gap-2 {
  gap: 0.5rem !important;
}
.gap-3 {
  gap: 1rem !important;
}
.gap-4 {
  gap: 1.5rem !important;
}
.gap-5 {
  gap: 3rem !important;
}
.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}
.fs-1 {
  font-size: calc(1.34375rem + 1.125vw) !important;
}
.fs-2 {
  font-size: calc(1.3rem + 0.6vw) !important;
}
.fs-3 {
  font-size: calc(1.27813rem + 0.3375vw) !important;
}
.fs-4 {
  font-size: calc(1.25625rem + 0.075vw) !important;
}
.fs-5 {
  font-size: 1.09375rem !important;
}
.fs-6 {
  font-size: 0.875rem !important;
}
.fst-italic {
  font-style: italic !important;
}
.fst-normal {
  font-style: normal !important;
}
.fw-light {
  font-weight: 300 !important;
}
.fw-lighter {
  font-weight: lighter !important;
}
.fw-normal {
  font-weight: 400 !important;
}
.fw-bold {
  font-weight: 700 !important;
}
.fw-semibold {
  font-weight: 600 !important;
}
.fw-bolder {
  font-weight: bolder !important;
}
.lh-1 {
  line-height: 1 !important;
}
.lh-sm {
  line-height: 1.25 !important;
}
.lh-base {
  line-height: 1.375 !important;
}
.lh-lg {
  line-height: 2 !important;
}
.text-start {
  text-align: left !important;
}
.text-end {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
.text-decoration-none {
  text-decoration: none !important;
}
.text-decoration-underline {
  text-decoration: underline !important;
}
.text-decoration-line-through {
  text-decoration: line-through !important;
}
.text-lowercase {
  text-transform: lowercase !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.text-wrap {
  white-space: normal !important;
}
.text-nowrap {
  white-space: nowrap !important;
}
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}
.text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}
.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}
.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}
.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}
.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}
.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}
.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}
.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}
.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}
.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}
.text-muted {
  --bs-text-opacity: 1;
  color: #bdbdbd !important;
}
.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}
.text-white-50 {
  --bs-text-opacity: 1;
  color: hsla(0, 0%, 100%, 0.5) !important;
}
.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}
.text-opacity-25 {
  --bs-text-opacity: 0.25;
}
.text-opacity-50 {
  --bs-text-opacity: 0.5;
}
.text-opacity-75 {
  --bs-text-opacity: 0.75;
}
.text-opacity-100 {
  --bs-text-opacity: 1;
}
.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-primary-rgb),
    var(--bs-bg-opacity)
  ) !important;
}
.bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-secondary-rgb),
    var(--bs-bg-opacity)
  ) !important;
}
.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-success-rgb),
    var(--bs-bg-opacity)
  ) !important;
}
.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}
.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-warning-rgb),
    var(--bs-bg-opacity)
  ) !important;
}
.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}
.bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}
.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}
.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}
.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}
.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-body-bg-rgb),
    var(--bs-bg-opacity)
  ) !important;
}
.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}
.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}
.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}
.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}
.bg-opacity-100 {
  --bs-bg-opacity: 1;
}
.bg-gradient {
  background-image: var(--bs-gradient) !important;
}
.user-select-all {
  -webkit-user-select: all !important;
  -moz-user-select: all !important;
  user-select: all !important;
}
.user-select-auto {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  user-select: auto !important;
}
.user-select-none {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  user-select: none !important;
}
.pe-none {
  pointer-events: none !important;
}
.pe-auto {
  pointer-events: auto !important;
}
.rounded {
  border-radius: var(--bs-border-radius) !important;
}
.rounded-0 {
  border-radius: 0 !important;
}
.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}
.rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}
.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}
.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}
.rounded-5 {
  border-radius: var(--bs-border-radius-2xl) !important;
}
.rounded-circle {
  border-radius: 50% !important;
}
.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}
.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
}
.rounded-end,
.rounded-top {
  border-top-right-radius: var(--bs-border-radius) !important;
}
.rounded-bottom,
.rounded-end {
  border-bottom-right-radius: var(--bs-border-radius) !important;
}
.rounded-bottom,
.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
}
.rounded-start {
  border-top-left-radius: var(--bs-border-radius) !important;
}
.visible {
  visibility: visible !important;
}
.invisible {
  visibility: hidden !important;
}
@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }
  .float-sm-end {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-sm-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }
  .mx-sm-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .mx-sm-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
  .mx-sm-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
  }
  .mx-sm-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
  .mx-sm-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-sm-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
    margin-top: 0.25rem !important;
  }
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
    margin-top: 0.5rem !important;
  }
  .my-sm-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important;
  }
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;
  }
  .my-sm-5 {
    margin-bottom: 3rem !important;
    margin-top: 3rem !important;
  }
  .my-sm-auto {
    margin-bottom: auto !important;
    margin-top: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .me-sm-5 {
    margin-right: 3rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .px-sm-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-sm-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
  .px-sm-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .px-sm-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .px-sm-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .px-sm-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .py-sm-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .py-sm-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important;
  }
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
  }
  .py-sm-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-5 {
    padding-left: 3rem !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .gap-sm-3 {
    gap: 1rem !important;
  }
  .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .gap-sm-5 {
    gap: 3rem !important;
  }
  .text-sm-start {
    text-align: left !important;
  }
  .text-sm-end {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }
  .float-md-end {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-md-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }
  .mx-md-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .mx-md-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
  .mx-md-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
  }
  .mx-md-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
  .mx-md-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-md-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .my-md-1 {
    margin-bottom: 0.25rem !important;
    margin-top: 0.25rem !important;
  }
  .my-md-2 {
    margin-bottom: 0.5rem !important;
    margin-top: 0.5rem !important;
  }
  .my-md-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important;
  }
  .my-md-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;
  }
  .my-md-5 {
    margin-bottom: 3rem !important;
    margin-top: 3rem !important;
  }
  .my-md-auto {
    margin-bottom: auto !important;
    margin-top: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mt-md-5 {
    margin-top: 3rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .me-md-5 {
    margin-right: 3rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .ms-md-5 {
    margin-left: 3rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .px-md-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-md-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
  .px-md-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .px-md-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .px-md-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .px-md-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .py-md-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .py-md-1 {
    padding-bottom: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
  .py-md-2 {
    padding-bottom: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .py-md-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important;
  }
  .py-md-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
  }
  .py-md-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pt-md-5 {
    padding-top: 3rem !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pe-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .ps-md-5 {
    padding-left: 3rem !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.25rem !important;
  }
  .gap-md-2 {
    gap: 0.5rem !important;
  }
  .gap-md-3 {
    gap: 1rem !important;
  }
  .gap-md-4 {
    gap: 1.5rem !important;
  }
  .gap-md-5 {
    gap: 3rem !important;
  }
  .text-md-start {
    text-align: left !important;
  }
  .text-md-end {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }
  .float-lg-end {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-lg-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }
  .mx-lg-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .mx-lg-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
  .mx-lg-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
  }
  .mx-lg-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
  .mx-lg-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-lg-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
    margin-top: 0.25rem !important;
  }
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
    margin-top: 0.5rem !important;
  }
  .my-lg-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important;
  }
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;
  }
  .my-lg-5 {
    margin-bottom: 3rem !important;
    margin-top: 3rem !important;
  }
  .my-lg-auto {
    margin-bottom: auto !important;
    margin-top: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .me-lg-5 {
    margin-right: 3rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .px-lg-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-lg-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
  .px-lg-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .px-lg-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .px-lg-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .px-lg-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .py-lg-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .py-lg-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important;
  }
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
  }
  .py-lg-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-5 {
    padding-left: 3rem !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .gap-lg-3 {
    gap: 1rem !important;
  }
  .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .gap-lg-5 {
    gap: 3rem !important;
  }
  .text-lg-start {
    text-align: left !important;
  }
  .text-lg-end {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important;
  }
  .float-xl-end {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-xl-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }
  .mx-xl-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .mx-xl-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
  .mx-xl-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
  }
  .mx-xl-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
  .mx-xl-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-xl-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
    margin-top: 0.25rem !important;
  }
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
    margin-top: 0.5rem !important;
  }
  .my-xl-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important;
  }
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;
  }
  .my-xl-5 {
    margin-bottom: 3rem !important;
    margin-top: 3rem !important;
  }
  .my-xl-auto {
    margin-bottom: auto !important;
    margin-top: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xl-5 {
    margin-right: 3rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .px-xl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-xl-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
  .px-xl-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .px-xl-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .px-xl-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .px-xl-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .py-xl-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .py-xl-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important;
  }
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
  }
  .py-xl-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-5 {
    padding-left: 3rem !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .gap-xl-3 {
    gap: 1rem !important;
  }
  .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .gap-xl-5 {
    gap: 3rem !important;
  }
  .text-xl-start {
    text-align: left !important;
  }
  .text-xl-end {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1440px) {
  .float-xxl-start {
    float: left !important;
  }
  .float-xxl-end {
    float: right !important;
  }
  .float-xxl-none {
    float: none !important;
  }
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .order-xxl-first {
    order: -1 !important;
  }
  .order-xxl-0 {
    order: 0 !important;
  }
  .order-xxl-1 {
    order: 1 !important;
  }
  .order-xxl-2 {
    order: 2 !important;
  }
  .order-xxl-3 {
    order: 3 !important;
  }
  .order-xxl-4 {
    order: 4 !important;
  }
  .order-xxl-5 {
    order: 5 !important;
  }
  .order-xxl-last {
    order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .mx-xxl-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
  }
  .mx-xxl-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
  }
  .mx-xxl-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }
  .mx-xxl-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
  }
  .mx-xxl-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
  }
  .mx-xxl-auto {
    margin-left: auto !important;
    margin-right: auto !important;
  }
  .my-xxl-0 {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .my-xxl-1 {
    margin-bottom: 0.25rem !important;
    margin-top: 0.25rem !important;
  }
  .my-xxl-2 {
    margin-bottom: 0.5rem !important;
    margin-top: 0.5rem !important;
  }
  .my-xxl-3 {
    margin-bottom: 1rem !important;
    margin-top: 1rem !important;
  }
  .my-xxl-4 {
    margin-bottom: 1.5rem !important;
    margin-top: 1.5rem !important;
  }
  .my-xxl-5 {
    margin-bottom: 3rem !important;
    margin-top: 3rem !important;
  }
  .my-xxl-auto {
    margin-bottom: auto !important;
    margin-top: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .me-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .ms-xxl-auto {
    margin-left: auto !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .px-xxl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-xxl-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }
  .px-xxl-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  .px-xxl-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  .px-xxl-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  .px-xxl-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .py-xxl-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
  }
  .py-xxl-1 {
    padding-bottom: 0.25rem !important;
    padding-top: 0.25rem !important;
  }
  .py-xxl-2 {
    padding-bottom: 0.5rem !important;
    padding-top: 0.5rem !important;
  }
  .py-xxl-3 {
    padding-bottom: 1rem !important;
    padding-top: 1rem !important;
  }
  .py-xxl-4 {
    padding-bottom: 1.5rem !important;
    padding-top: 1.5rem !important;
  }
  .py-xxl-5 {
    padding-bottom: 3rem !important;
    padding-top: 3rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .gap-xxl-3 {
    gap: 1rem !important;
  }
  .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .gap-xxl-5 {
    gap: 3rem !important;
  }
  .text-xxl-start {
    text-align: left !important;
  }
  .text-xxl-end {
    text-align: right !important;
  }
  .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.1875rem !important;
  }
  .fs-2 {
    font-size: 1.75rem !important;
  }
  .fs-3 {
    font-size: 1.53125rem !important;
  }
  .fs-4 {
    font-size: 1.3125rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}
.hamburger {
  background-color: transparent;
  border: 0;
  color: inherit;
  cursor: pointer;
  display: inline-block;
  font: inherit;
  margin: 0;
  overflow: visible;
  padding: 18px 15px;
  text-transform: none;
  transition-duration: 0.15s;
  transition-property: opacity, filter;
  transition-timing-function: linear;
}
.hamburger.is-active:hover,
.hamburger:hover {
  opacity: 1;
}
.hamburger.is-active .hamburger-inner,
.hamburger.is-active .hamburger-inner:after,
.hamburger.is-active .hamburger-inner:before {
  background-color: #a4b4cb;
}
.hamburger-box {
  display: inline-block;
  height: 14px;
  position: relative;
  width: 20px;
}
.hamburger-inner {
  display: block;
  margin-top: -1px;
  top: 50%;
}
.hamburger-inner,
.hamburger-inner:after,
.hamburger-inner:before {
  background-color: #a4b4cb;
  border-radius: 0;
  height: 2px;
  position: absolute;
  transition-duration: 0.15s;
  transition-property: transform;
  transition-timing-function: ease;
  width: 20px;
}
.hamburger-inner:after,
.hamburger-inner:before {
  content: "";
  display: block;
}
.hamburger-inner:before {
  top: -6px;
}
.hamburger-inner:after {
  bottom: -6px;
}
.hamburger--slider .hamburger-inner {
  top: 1px;
}
.hamburger--slider .hamburger-inner:before {
  top: 6px;
  transition-duration: 0.15s;
  transition-property: transform, opacity;
  transition-timing-function: ease;
}
.hamburger--slider .hamburger-inner:after {
  top: 12px;
}
.hamburger--slider.is-active .hamburger-inner {
  transform: translate3d(0, 6px, 0) rotate(45deg);
}
.hamburger--slider.is-active .hamburger-inner:before {
  opacity: 0;
  transform: rotate(-45deg) translate3d(-2.8571428571px, -4px, 0);
}
.hamburger--slider.is-active .hamburger-inner:after {
  transform: translate3d(0, -12px, 0) rotate(-90deg);
}
.hamburger {
  margin-left: -15px;
}
.hamburger:active,
.hamburger:focus,
.hamburger:hover {
  outline: none;
}
.hamburger:active .hamburger-inner,
.hamburger:active .hamburger-inner:after,
.hamburger:active .hamburger-inner:before,
.hamburger:focus .hamburger-inner,
.hamburger:focus .hamburger-inner:after,
.hamburger:focus .hamburger-inner:before,
.hamburger:hover .hamburger-inner,
.hamburger:hover .hamburger-inner:after,
.hamburger:hover .hamburger-inner:before {
  background-color: #415ec0;
}
.flatpickr-calendar {
  animation: none;
  background: transparent;
  background: #fff;
  border: 0;
  border-radius: 5px;
  box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6,
    0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  direction: ltr;
  display: none;
  font-size: 14px;
  line-height: 24px;
  opacity: 0;
  padding: 0;
  position: absolute;
  text-align: center;
  touch-action: manipulation;
  visibility: hidden;
  width: 307.875px;
}
.flatpickr-calendar.inline,
.flatpickr-calendar.open {
  max-height: 640px;
  opacity: 1;
  visibility: visible;
}
.flatpickr-calendar.open {
  display: inline-block;
  z-index: 99999;
}
.flatpickr-calendar.animate.open {
  animation: fpFadeInDown 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px;
}
.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px);
}
.flatpickr-calendar.static.open {
  display: block;
  z-index: 999;
}
.flatpickr-calendar.multiMonth
  .flatpickr-days
  .dayContainer:nth-child(n + 1)
  .flatpickr-day.inRange:nth-child(7n + 7) {
  box-shadow: none !important;
}
.flatpickr-calendar.multiMonth
  .flatpickr-days
  .dayContainer:nth-child(n + 2)
  .flatpickr-day.inRange:nth-child(7n + 1) {
  box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-calendar .hasTime .dayContainer,
.flatpickr-calendar .hasWeeks .dayContainer {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.flatpickr-calendar .hasWeeks .dayContainer {
  border-left: 0;
}
.flatpickr-calendar.hasTime .flatpickr-time {
  border-top: 1px solid #e6e6e6;
  height: 40px;
}
.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto;
}
.flatpickr-calendar:after,
.flatpickr-calendar:before {
  border: solid transparent;
  content: "";
  display: block;
  height: 0;
  left: 22px;
  pointer-events: none;
  position: absolute;
  width: 0;
}
.flatpickr-calendar.arrowRight:after,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.rightMost:before {
  left: auto;
  right: 22px;
}
.flatpickr-calendar.arrowCenter:after,
.flatpickr-calendar.arrowCenter:before {
  left: 50%;
  right: 50%;
}
.flatpickr-calendar:before {
  border-width: 5px;
  margin: 0 -5px;
}
.flatpickr-calendar:after {
  border-width: 4px;
  margin: 0 -4px;
}
.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
  bottom: 100%;
}
.flatpickr-calendar.arrowTop:before {
  border-bottom-color: #e6e6e6;
}
.flatpickr-calendar.arrowTop:after {
  border-bottom-color: #fff;
}
.flatpickr-calendar.arrowBottom:after,
.flatpickr-calendar.arrowBottom:before {
  top: 100%;
}
.flatpickr-calendar.arrowBottom:before {
  border-top-color: #e6e6e6;
}
.flatpickr-calendar.arrowBottom:after {
  border-top-color: #fff;
}
.flatpickr-calendar:focus {
  outline: 0;
}
.flatpickr-wrapper {
  display: inline-block;
  position: relative;
}
.flatpickr-months .flatpickr-month {
  background: transparent;
  flex: 1;
  line-height: 1;
  overflow: hidden;
  position: relative;
  text-align: center;
}
.flatpickr-months .flatpickr-month,
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
  fill: rgba(0, 0, 0, 0.9);
  color: rgba(0, 0, 0, 0.9);
  height: 34px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
  cursor: pointer;
  padding: 10px;
  position: absolute;
  text-decoration: none;
  top: 0;
  z-index: 3;
}
.flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
  display: none;
}
.flatpickr-months .flatpickr-next-month i,
.flatpickr-months .flatpickr-prev-month i {
  position: relative;
}
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
  left: 0;
}
.flatpickr-months .flatpickr-next-month.flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month {
  right: 0;
}
.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:hover {
  color: #959ea9;
}
.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
  fill: #f64747;
}
.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month svg {
  height: 14px;
  width: 14px;
}
.flatpickr-months .flatpickr-next-month svg path,
.flatpickr-months .flatpickr-prev-month svg path {
  fill: inherit;
  transition: fill 0.1s;
}
.numInputWrapper {
  height: auto;
  position: relative;
}
.numInputWrapper input,
.numInputWrapper span {
  display: inline-block;
}
.numInputWrapper input {
  width: 100%;
}
.numInputWrapper input::-ms-clear {
  display: none;
}
.numInputWrapper input::-webkit-inner-spin-button,
.numInputWrapper input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.numInputWrapper span {
  border: 1px solid rgba(57, 57, 57, 0.15);
  box-sizing: border-box;
  cursor: pointer;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  padding: 0 4px 0 2px;
  position: absolute;
  right: 0;
  width: 14px;
}
.numInputWrapper span:hover {
  background: rgba(0, 0, 0, 0.1);
}
.numInputWrapper span:active {
  background: rgba(0, 0, 0, 0.2);
}
.numInputWrapper span:after {
  content: "";
  display: block;
  position: absolute;
}
.numInputWrapper span.arrowUp {
  border-bottom: 0;
  top: 0;
}
.numInputWrapper span.arrowUp:after {
  border-bottom: 4px solid rgba(57, 57, 57, 0.6);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  top: 26%;
}
.numInputWrapper span.arrowDown {
  top: 50%;
}
.numInputWrapper span.arrowDown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(57, 57, 57, 0.6);
  top: 40%;
}
.numInputWrapper span svg {
  height: auto;
  width: inherit;
}
.numInputWrapper span svg path {
  fill: rgba(0, 0, 0, 0.5);
}
.numInputWrapper:hover {
  background: rgba(0, 0, 0, 0.05);
}
.numInputWrapper:hover span {
  opacity: 1;
}
.flatpickr-current-month {
  color: inherit;
  display: inline-block;
  font-size: 135%;
  font-weight: 300;
  height: 34px;
  left: 12.5%;
  line-height: inherit;
  line-height: 1;
  padding: 7.48px 0 0;
  position: absolute;
  text-align: center;
  transform: translateZ(0);
  width: 75%;
}
.flatpickr-current-month span.cur-month {
  color: inherit;
  display: inline-block;
  font-family: inherit;
  font-weight: 700;
  margin-left: 0.5ch;
  padding: 0;
}
.flatpickr-current-month span.cur-month:hover {
  background: rgba(0, 0, 0, 0.05);
}
.flatpickr-current-month .numInputWrapper {
  display: inline-block;
  width: 6ch;
  width: 7ch\0;
}
.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month input.cur-year {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  background: transparent;
  border: 0;
  border-radius: 0;
  box-sizing: border-box;
  color: inherit;
  cursor: text;
  display: inline-block;
  font-family: inherit;
  font-size: inherit;
  font-weight: 300;
  height: auto;
  line-height: inherit;
  margin: 0;
  padding: 0 0 0 0.5ch;
  vertical-align: initial;
}
.flatpickr-current-month input.cur-year:focus {
  outline: 0;
}
.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  background: transparent;
  color: rgba(0, 0, 0, 0.5);
  font-size: 100%;
  pointer-events: none;
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
  appearance: menulist;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  background: transparent;
  border: none;
  border-radius: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  color: inherit;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  font-weight: 300;
  height: auto;
  line-height: inherit;
  margin: -1px 0 0;
  outline: none;
  padding: 0 0 0 0.5ch;
  position: relative;
  vertical-align: initial;
  width: auto;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:active,
.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  outline: none;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: rgba(0, 0, 0, 0.05);
}
.flatpickr-current-month
  .flatpickr-monthDropdown-months
  .flatpickr-monthDropdown-month {
  background-color: transparent;
  outline: none;
  padding: 0;
}
.flatpickr-weekdays {
  align-items: center;
  background: transparent;
  display: flex;
  height: 28px;
  overflow: hidden;
  text-align: center;
  width: 100%;
}
.flatpickr-weekdays .flatpickr-weekdaycontainer {
  display: flex;
  flex: 1;
}
span.flatpickr-weekday {
  background: transparent;
  color: rgba(0, 0, 0, 0.54);
  cursor: default;
  display: block;
  flex: 1;
  font-size: 90%;
  font-weight: bolder;
  line-height: 1;
  margin: 0;
  text-align: center;
}
.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0;
}
.flatpickr-days {
  align-items: flex-start;
  display: flex;
  overflow: hidden;
  position: relative;
  width: 307.875px;
}
.flatpickr-days:focus {
  outline: 0;
}
.dayContainer {
  box-sizing: border-box;
  display: inline-block;
  display: flex;
  flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  justify-content: space-around;
  max-width: 307.875px;
  min-width: 307.875px;
  opacity: 1;
  outline: 0;
  padding: 0;
  text-align: left;
  transform: translateZ(0);
  width: 307.875px;
}
.dayContainer + .dayContainer {
  box-shadow: -1px 0 0 #e6e6e6;
}
.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  box-sizing: border-box;
  color: #393939;
  cursor: pointer;
  display: inline-block;
  flex-basis: 14.2857143%;
  font-weight: 400;
  height: 39px;
  line-height: 39px;
  margin: 0;
  max-width: 39px;
  position: relative;
  text-align: center;
  width: 14.2857143%;
}
.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
  background: #e6e6e6;
  border-color: #e6e6e6;
  cursor: pointer;
  outline: 0;
}
.flatpickr-day.today {
  border-color: #959ea9;
}
.flatpickr-day.today:focus,
.flatpickr-day.today:hover {
  background: #959ea9;
  border-color: #959ea9;
  color: #fff;
}
.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
  background: #569ff7;
  border-color: #569ff7;
  box-shadow: none;
}
.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
  border-radius: 50px 0 0 50px;
}
.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
  border-radius: 0 50px 50px 0;
}
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #569ff7;
}
.flatpickr-day.endRange.startRange.endRange,
.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange {
  border-radius: 50px;
}
.flatpickr-day.inRange {
  border-radius: 0;
  box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
  background: transparent;
  border-color: transparent;
  color: rgba(57, 57, 57, 0.3);
  cursor: default;
}
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
  color: rgba(57, 57, 57, 0.1);
  cursor: not-allowed;
}
.flatpickr-day.week.selected {
  border-radius: 0;
  box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
}
.flatpickr-day.hidden {
  visibility: hidden;
}
.rangeMode .flatpickr-day {
  margin-top: 1px;
}
.flatpickr-weekwrapper {
  float: left;
}
.flatpickr-weekwrapper .flatpickr-weeks {
  box-shadow: 1px 0 0 #e6e6e6;
  padding: 0 12px;
}
.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  line-height: 28px;
  width: 100%;
}
.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
  background: transparent;
  border: none;
  color: rgba(57, 57, 57, 0.3);
  cursor: default;
  display: block;
  max-width: none;
  width: 100%;
}
.flatpickr-innerContainer {
  box-sizing: border-box;
  display: block;
  display: flex;
  overflow: hidden;
}
.flatpickr-rContainer {
  box-sizing: border-box;
  display: inline-block;
  padding: 0;
}
.flatpickr-time {
  box-sizing: border-box;
  display: block;
  display: flex;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  outline: 0;
  overflow: hidden;
  text-align: center;
}
.flatpickr-time:after {
  clear: both;
  content: "";
  display: table;
}
.flatpickr-time .numInputWrapper {
  flex: 1;
  float: left;
  height: 40px;
  width: 40%;
}
.flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: #393939;
}
.flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: #393939;
}
.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%;
}
.flatpickr-time.time24hr .numInputWrapper {
  width: 49%;
}
.flatpickr-time input {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  background: transparent;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  box-sizing: border-box;
  color: #393939;
  font-size: 14px;
  height: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  position: relative;
  text-align: center;
}
.flatpickr-time input.flatpickr-hour {
  font-weight: 700;
}
.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
  font-weight: 400;
}
.flatpickr-time input:focus {
  border: 0;
  outline: 0;
}
.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator {
  align-self: center;
  color: #393939;
  float: left;
  font-weight: 700;
  height: inherit;
  line-height: inherit;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: 2%;
}
.flatpickr-time .flatpickr-am-pm {
  cursor: pointer;
  font-weight: 400;
  outline: 0;
  text-align: center;
  width: 18%;
}
.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time input:hover {
  background: #eee;
}
.flatpickr-input[readonly] {
  cursor: pointer;
}
@keyframes fpFadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
.flatpickr-monthSelect-months {
  flex-wrap: wrap;
  margin: 10px 1px 3px;
}
.flatpickr-monthSelect-month {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  box-sizing: border-box;
  color: #393939;
  cursor: pointer;
  display: inline-block;
  font-weight: 400;
  justify-content: center;
  -webkit-justify-content: center;
  margin: 0.5px;
  padding: 10px;
  position: relative;
  text-align: center;
  width: 33%;
}
.flatpickr-monthSelect-month.flatpickr-disabled {
  color: #eee;
}
.flatpickr-monthSelect-month.flatpickr-disabled:focus,
.flatpickr-monthSelect-month.flatpickr-disabled:hover {
  background: none !important;
  cursor: not-allowed;
}
.flatpickr-monthSelect-theme-dark {
  background: #3f4458;
}
.flatpickr-monthSelect-theme-dark .flatpickr-current-month input.cur-year {
  color: #fff;
}
.flatpickr-monthSelect-theme-dark .flatpickr-months .flatpickr-next-month,
.flatpickr-monthSelect-theme-dark .flatpickr-months .flatpickr-prev-month {
  fill: #fff;
  color: #fff;
}
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month {
  color: hsla(0, 0%, 100%, 0.95);
}
.flatpickr-monthSelect-month.today {
  border-color: #959ea9;
}
.flatpickr-monthSelect-month.inRange,
.flatpickr-monthSelect-month.inRange.today,
.flatpickr-monthSelect-month:focus,
.flatpickr-monthSelect-month:hover {
  background: #e6e6e6;
  border-color: #e6e6e6;
  cursor: pointer;
  outline: 0;
}
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month.inRange,
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month:focus,
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month:hover {
  background: #646c8c;
  border-color: #646c8c;
}
.flatpickr-monthSelect-month.today:focus,
.flatpickr-monthSelect-month.today:hover {
  background: #959ea9;
  border-color: #959ea9;
  color: #fff;
}
.flatpickr-monthSelect-month.endRange,
.flatpickr-monthSelect-month.selected,
.flatpickr-monthSelect-month.startRange {
  background-color: #569ff7;
  border-color: #569ff7;
  box-shadow: none;
  color: #fff;
}
.flatpickr-monthSelect-month.startRange {
  border-radius: 50px 0 0 50px;
}
.flatpickr-monthSelect-month.endRange {
  border-radius: 0 50px 50px 0;
}
.flatpickr-monthSelect-month.startRange.endRange {
  border-radius: 50px;
}
.flatpickr-monthSelect-month.inRange {
  border-radius: 0;
  box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month.endRange,
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month.selected,
.flatpickr-monthSelect-theme-dark .flatpickr-monthSelect-month.startRange {
  background: #80cbc4;
  border-color: #80cbc4;
  box-shadow: none;
  color: #fff;
}
.flatpickr {
  position: relative;
}
.flatpickr .flatpickr-input {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.6 1.6H12V.8a.8.8 0 0 0-1.6 0v.8H5.6V.8A.8.8 0 0 0 4 .8v.8H2.4A2.4 2.4 0 0 0 0 4v9.6A2.4 2.4 0 0 0 2.4 16h11.2a2.4 2.4 0 0 0 2.4-2.4V4a2.4 2.4 0 0 0-2.4-2.4Zm.8 12a.8.8 0 0 1-.8.8H2.4a.8.8 0 0 1-.8-.8V8h12.8v5.6Zm0-7.2H1.6V4a.8.8 0 0 1 .8-.8H4V4a.8.8 0 0 0 1.6 0v-.8h4.8V4A.8.8 0 0 0 12 4v-.8h1.6a.8.8 0 0 1 .8.8v2.4Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-position: center right 1rem;
  background-repeat: no-repeat;
  background-size: 1rem;
}
.flatpickr .flatpickr-input.form-control-sm {
  background-position: center right 0.75rem;
}
.flatpickr-input {
  border-color: #dee4f0;
  color: #141735;
  padding-right: 2.5rem;
}
.flatpickr-input[readonly] {
  background-color: var(--bs-white);
}
.flatpickr-input.active,
.flatpickr-input:focus {
  border-color: #a5b4e9;
  color: #141735;
}
.flatpickr-input.form-control-clear {
  width: 4.375rem;
}
.flatpickr-icon {
  color: #a4adbd;
  display: flex;
  font-size: 1.5rem;
  position: absolute;
  right: 0;
  top: 0;
}
.flatpickr-range > .flatpickr-input {
  min-width: 18rem;
}
@media (min-width: 576px) {
  .flatpickr-range .flatpickr {
    width: 10rem;
  }
}
.flatpickr-noborder {
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.flatpickr-noborder .form-control {
  border-color: transparent;
}
@media (min-width: 992px) {
  .flatpickr-sm .flatpickr-input {
    font-size: 0.75rem;
  }
}
.flatpickr-calendar {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  font-size: 0.875rem;
  transition: none;
  width: 18.8125rem;
}
@media (max-width: 575.98px) {
  .flatpickr-calendar {
    width: calc(100vw - 1.5rem);
  }
}
.flatpickr-calendar:before {
  border-width: 7px;
}
.flatpickr-calendar:after {
  border-width: 6px;
}
.flatpickr-calendar.open {
  z-index: 1065;
}
.flatpickr-calendar.open.arrowTop {
  margin-top: 0.5rem;
}
.flatpickr-calendar.open.arrowBottom {
  margin-top: -0.5rem;
}
.flatpickr-innerContainer,
.flatpickr-months {
  padding: 1rem;
}
.flatpickr-months {
  align-items: center;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.flatpickr-months + .flatpickr-innerContainer {
  padding-top: 0;
}
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
  border-radius: 0.375rem;
  top: 1rem;
}
.flatpickr-months .flatpickr-next-month:focus-visible,
.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:focus-visible,
.flatpickr-months .flatpickr-prev-month:hover {
  background-color: #f8f7fa;
}
.flatpickr-months .flatpickr-next-month:focus-visible svg,
.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:focus-visible svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
  fill: #141735;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
  left: 1rem;
}
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  right: 1rem;
}
.flatpickr-months .numInputWrapper {
  width: 6.5ch;
}
.flatpickr-current-month {
  align-items: center;
  display: flex;
  font-size: 100%;
  justify-content: center;
  padding-top: 0;
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E")
    no-repeat right 0.5rem center;
  background-size: 0.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 0;
  min-height: 1.375rem;
  padding-right: 24px;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: rgba(0, 0, 0, 0.05)
    url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E")
    no-repeat right 0.5rem center;
  background-size: 0.5rem;
}
.flatpickr-current-month input.cur-year {
  font-weight: 700;
}
.flatpickr-current-month .numInputWrapper span:after {
  background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E")
    no-repeat 50%;
  background-size: 75% 50%;
  border: none;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  transform: rotate(180deg);
}
.flatpickr-next-month,
.flatpickr-prev-month {
  align-items: center;
  display: flex;
  justify-content: center;
  padding: 0;
}
.flatpickr-days,
.flatpickr-days .dayContainer {
  gap: 4px;
  max-width: none;
  min-width: 0;
  width: 100%;
}
.flatpickr-day {
  align-items: center;
  aspect-ratio: 1/1;
  border-radius: 0.375rem;
  color: #141735;
  display: flex;
  flex-basis: calc(14.28571% - 3.42857px);
  height: auto;
  justify-content: center;
  line-height: normal;
  max-width: none;
  min-width: 0;
  width: calc(14.28571% - 3.42857px);
}
.flatpickr-day.today {
  border-color: var(--bs-primary);
}
.flatpickr-day.inRange {
  box-shadow: -5px 0 0 #f8f7fa, 5px 0 0 #f8f7fa;
}
.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
  background-color: #f8f7fa;
  border-color: #f8f7fa;
}
.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
  background-color: #4a69d2;
  border-color: #4a69d2;
  color: #fff;
}
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #4a69d2;
}
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
  color: #566171;
}
span.flatpickr-weekday {
  color: #141735;
  font-size: 114.286%;
  font-weight: 700;
}
.flatpickr-with-label .flatpickr-label {
  color: #bdbdbd;
  left: 0.75rem;
  position: absolute;
  top: 0.4375rem;
}
.flatpickr-with-label .flatpickr-input {
  height: 3.125rem;
  padding-top: 1.625rem;
}
.flatpickr-with-label .flatpickr-icon {
  top: 0.3125rem;
}
.flatpickr-calendar.hasTime .flatpickr-time {
  border-top: none;
}
.flatpickr-calendar {
  border-radius: 0.375rem;
}
.flatpickr-calendar:after,
.flatpickr-calendar:before {
  display: none;
}
.flatpickr-calendar.hasTime.noCalendar {
  border-radius: 0.375rem;
  overflow: hidden;
}
.flatpickr-clear .flatpickr-input {
  background-color: transparent;
  border: none;
  padding: 0;
}
.flatpickr-clear .flatpickr-icon {
  color: #4a69d2;
  padding: 0;
}
.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
  border-bottom-left-radius: 0.375rem;
  border-top-left-radius: 0.375rem;
}
.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
  border-bottom-right-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.flatpickr-hide-input {
  position: relative;
}
.flatpickr-hide-input .flatpickr-input {
  height: 100%;
  left: 0;
  min-height: auto;
  opacity: 0;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
}
.flatpickr-monthSelect-month {
  width: calc(33% - 1px);
}
@keyframes vscomp-animation-spin {
  to {
    transform: rotate(1turn);
  }
}
.vscomp-popup-active {
  overflow: hidden !important;
}
.vscomp-ele {
  display: inline-block;
  max-width: 250px;
  width: 100%;
}
.vscomp-wrapper {
  color: #333;
  display: inline-flex;
  flex-wrap: wrap;
  font-family: sans-serif;
  font-size: 14px;
  position: relative;
  text-align: left;
  width: 100%;
}
.vscomp-wrapper *,
.vscomp-wrapper :after,
.vscomp-wrapper :before {
  box-sizing: border-box;
}
.vscomp-wrapper:focus {
  outline: none;
}
.vscomp-dropbox-wrapper {
  left: 0;
  position: absolute;
  top: 0;
}
.vscomp-toggle-button {
  align-items: center;
  background-color: #fff;
  border: 1px solid #ddd;
  cursor: pointer;
  display: flex;
  padding: 7px 30px 7px 10px;
  position: relative;
  width: 100%;
}
.vscomp-value {
  height: 20px;
  line-height: 20px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vscomp-arrow {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
}
.vscomp-arrow:after {
  border-color: transparent #111 #111 transparent;
  border-style: solid;
  border-width: 1px;
  content: "";
  height: 8px;
  margin-top: -6px;
  transform: rotate(45deg);
  width: 8px;
}
.vscomp-clear-icon {
  height: 12px;
  position: relative;
  width: 12px;
}
.vscomp-clear-icon:after,
.vscomp-clear-icon:before {
  background-color: #999;
  content: "";
  height: 12px;
  left: 5px;
  position: absolute;
  top: 0;
  width: 2px;
}
.vscomp-clear-icon:before {
  transform: rotate(45deg);
}
.vscomp-clear-icon:after {
  transform: rotate(-45deg);
}
.vscomp-clear-icon:hover:after,
.vscomp-clear-icon:hover:before {
  background: #333;
}
.vscomp-clear-button {
  align-items: center;
  border-radius: 50%;
  display: none;
  height: 24px;
  justify-content: center;
  margin-top: -12px;
  position: absolute;
  right: 30px;
  top: 50%;
  width: 24px;
}
.vscomp-clear-button:hover {
  background: #ccc;
}
.vscomp-clear-button:hover .vscomp-clear-icon:after,
.vscomp-clear-button:hover .vscomp-clear-icon:before {
  background-color: #333;
}
.vscomp-dropbox-close-button {
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  bottom: -48px;
  cursor: pointer;
  display: none;
  height: 40px;
  justify-content: center;
  left: 50%;
  margin-left: -20px;
  position: absolute;
  width: 40px;
}
.vscomp-value-tag.more-value-count {
  white-space: nowrap;
}
.vscomp-dropbox-container {
  width: 100%;
  z-index: 2;
}
.vscomp-dropbox {
  background-color: #fff;
  width: 100%;
}
.vscomp-options-container {
  max-height: 210px;
  overflow: auto;
  position: relative;
}
.vscomp-options-bottom-freezer {
  bottom: 0;
  height: 2px;
  left: 0;
  position: absolute;
  right: 0;
}
.vscomp-option {
  cursor: pointer;
  flex-wrap: wrap;
  height: 40px;
  padding: 0 15px;
  position: relative;
}
.vscomp-option.selected {
  background-color: #eee;
}
.vscomp-option.focused {
  background-color: #ccc;
}
.vscomp-option.disabled {
  cursor: default;
  opacity: 0.5;
}
.vscomp-option.group-title .vscomp-option-text {
  cursor: default;
  opacity: 0.6;
}
.vscomp-option.group-title.selected {
  background-color: transparent;
}
.vscomp-option.group-option {
  padding-left: 30px;
}
.vscomp-new-option-icon {
  height: 30px;
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
}
.vscomp-new-option-icon:before {
  border-color: #512da8 #512da8 transparent transparent;
  border-style: solid;
  border-width: 15px;
  content: "";
  position: absolute;
  right: 0;
  top: 0;
}
.vscomp-new-option-icon:after {
  align-items: center;
  color: #fff;
  content: "+";
  display: flex;
  font-size: 18px;
  height: 15px;
  justify-content: center;
  position: absolute;
  right: 1px;
  top: 0;
  width: 15px;
}
.vscomp-option-text {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.vscomp-option-description,
.vscomp-option-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.vscomp-option-description {
  color: #666;
  font-size: 13px;
  line-height: 15px;
}
.vscomp-search-container {
  align-items: center;
  border-bottom: 1px solid #ddd;
  display: flex;
  height: 40px;
  padding: 0 5px 0 15px;
  position: relative;
}
.vscomp-search-input {
  background-color: transparent;
  border: 0;
  color: inherit;
  font-size: 15px;
  height: 38px;
  padding: 10px 0;
  width: calc(100% - 30px);
}
.vscomp-search-input:focus {
  outline: none;
}
.vscomp-search-clear {
  align-items: center;
  color: #999;
  cursor: pointer;
  display: flex;
  font-size: 25px;
  height: 30px;
  justify-content: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  visibility: hidden;
  width: 30px;
}
.vscomp-search-clear:hover {
  color: inherit;
}
.vscomp-no-options,
.vscomp-no-search-results {
  align-items: center;
  display: none;
  justify-content: center;
  padding: 20px 10px;
}
.vscomp-options-loader {
  display: none;
  padding: 20px 0;
  text-align: center;
}
.vscomp-options-loader:before {
  animation: vscomp-animation-spin 0.8s linear infinite;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: -4px -5px 3px -3px rgba(0, 0, 0, 0.3);
  content: "";
  display: inline-block;
  height: 40px;
  opacity: 0.7;
  width: 40px;
}
.vscomp-ele[disabled] {
  cursor: not-allowed;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.vscomp-ele[disabled] .vscomp-wrapper {
  opacity: 0.7;
  pointer-events: none;
}
.vscomp-wrapper .checkbox-icon {
  display: inline-flex;
  height: 15px;
  margin-right: 10px;
  position: relative;
  width: 15px;
}
.vscomp-wrapper .checkbox-icon:after {
  border: 2px solid #888;
  content: "";
  display: inline-block;
  height: 100%;
  transition-duration: 0.2s;
  width: 100%;
}
.vscomp-wrapper .checkbox-icon.checked:after {
  border-color: transparent #512da8 #512da8 transparent;
  transform: rotate(45deg) translate(1px, -4px);
  width: 50%;
}
.vscomp-wrapper.show-as-popup .vscomp-dropbox-container {
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  height: 100vh;
  justify-content: center;
  left: 0;
  opacity: 1;
  overflow: auto;
  padding: 0 10px;
  position: fixed;
  top: 0;
  width: 100vw;
}
.vscomp-wrapper.show-as-popup .vscomp-dropbox {
  margin-top: -24px;
  max-height: calc(80% - 48px);
  max-width: 500px;
  position: relative;
  width: 80%;
}
.vscomp-wrapper.show-as-popup .vscomp-dropbox-close-button {
  display: flex;
}
.vscomp-wrapper.popup-position-left .vscomp-dropbox-container {
  justify-content: flex-start;
}
.vscomp-wrapper.popup-position-right .vscomp-dropbox-container {
  justify-content: flex-end;
}
.vscomp-wrapper.has-select-all .vscomp-toggle-all-button {
  align-items: center;
  cursor: pointer;
  display: flex;
}
.vscomp-wrapper.has-select-all .vscomp-search-input,
.vscomp-wrapper.has-select-all .vscomp-toggle-all-label {
  width: calc(100% - 55px);
}
.vscomp-wrapper.has-select-all .vscomp-toggle-all-label {
  display: none;
}
.vscomp-wrapper:not(.has-search-input) .vscomp-toggle-all-button {
  width: 100%;
}
.vscomp-wrapper:not(.has-search-input) .vscomp-toggle-all-label {
  display: inline-block;
}
.vscomp-wrapper.multiple .vscomp-option .vscomp-option-text {
  width: calc(100% - 25px);
}
.vscomp-wrapper.multiple .vscomp-option .vscomp-option-description {
  padding-left: 25px;
}
.vscomp-wrapper.multiple .vscomp-option.selected .checkbox-icon:after {
  border-color: transparent #512da8 #512da8 transparent;
  transform: rotate(45deg) translate(1px, -4px);
  width: 50%;
}
.vscomp-wrapper.focused .vscomp-toggle-button,
.vscomp-wrapper:focus .vscomp-toggle-button {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2);
}
.vscomp-wrapper.closed .vscomp-dropbox-container,
.vscomp-wrapper.closed.vscomp-dropbox-wrapper {
  display: none;
}
.vscomp-wrapper:not(.has-value) .vscomp-value {
  opacity: 0.5;
}
.vscomp-wrapper.has-clear-button.has-value .vscomp-clear-button {
  display: flex;
}
.vscomp-wrapper.has-clear-button .vscomp-toggle-button {
  padding-right: 54px;
}
.vscomp-wrapper.has-no-options .vscomp-options-container,
.vscomp-wrapper.has-no-search-results .vscomp-options-container {
  display: none;
}
.vscomp-wrapper.has-no-options .vscomp-no-options,
.vscomp-wrapper.has-no-search-results .vscomp-no-search-results {
  display: flex;
}
.vscomp-wrapper.has-search-value .vscomp-search-clear {
  visibility: visible;
}
.vscomp-wrapper.has-no-options .vscomp-toggle-all-button {
  opacity: 0.5;
  pointer-events: none;
}
.vscomp-wrapper.keep-always-open .vscomp-toggle-button {
  padding-right: 24px;
}
.vscomp-wrapper.keep-always-open .vscomp-clear-button {
  right: 5px;
}
.vscomp-wrapper.keep-always-open .vscomp-arrow {
  display: none;
}
.vscomp-wrapper.keep-always-open .vscomp-dropbox-container {
  position: relative;
  z-index: 1;
}
.vscomp-wrapper.keep-always-open .vscomp-dropbox {
  border: 1px solid #ddd;
  box-shadow: none;
  transition-duration: 0s;
}
.vscomp-wrapper.keep-always-open.focused,
.vscomp-wrapper.keep-always-open:focus,
.vscomp-wrapper.keep-always-open:hover {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2);
}
.vscomp-wrapper.server-searching .vscomp-options-list {
  display: none;
}
.vscomp-wrapper.server-searching .vscomp-options-loader {
  display: block;
}
.vscomp-wrapper.has-error .vscomp-toggle-button {
  border-color: #b00020;
}
.vscomp-wrapper.show-value-as-tags .vscomp-toggle-button {
  padding: 4px 22px 0 10px;
}
.vscomp-wrapper.show-value-as-tags .vscomp-value {
  display: flex;
  flex-wrap: wrap;
  height: auto;
  min-height: 28px;
  overflow: auto;
  text-overflow: unset;
  white-space: normal;
}
.vscomp-wrapper.show-value-as-tags .vscomp-value-tag {
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 20px;
  display: inline-flex;
  font-size: 12px;
  line-height: 16px;
  margin: 0 4px 4px 0;
  max-width: 100%;
  overflow: hidden;
  padding: 2px 3px 2px 8px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vscomp-wrapper.show-value-as-tags .vscomp-value-tag.more-value-count {
  padding-right: 8px;
}
.vscomp-wrapper.show-value-as-tags .vscomp-value-tag-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 20px);
}
.vscomp-wrapper.show-value-as-tags .vscomp-value-tag-clear-button {
  align-items: center;
  display: flex;
  height: 20px;
  justify-content: center;
  width: 20px;
}
.vscomp-wrapper.show-value-as-tags
  .vscomp-value-tag-clear-button
  .vscomp-clear-icon {
  transform: scale(0.8);
}
.vscomp-wrapper.show-value-as-tags .vscomp-arrow {
  height: 34px;
}
.vscomp-wrapper.show-value-as-tags .vscomp-clear-button {
  margin-top: 0;
  top: 5px;
}
.vscomp-wrapper.show-value-as-tags.has-value .vscomp-arrow {
  display: none;
}
.vscomp-wrapper.show-value-as-tags.has-value .vscomp-clear-button {
  right: 2px;
}
.vscomp-wrapper.show-value-as-tags:not(.has-value) .vscomp-toggle-button {
  padding-bottom: 2px;
}
.vscomp-wrapper.show-value-as-tags:not(.has-value) .vscomp-value {
  align-items: center;
  padding-bottom: 3px;
}
.vscomp-ele {
  max-width: none;
}
.vscomp-wrapper {
  color: #566171;
}
.vscomp-dropbox-wrapper {
  height: 0;
}
.vscomp-wrapper:not(.has-value) .vscomp-value {
  color: #566171;
  opacity: 1;
}
.vscomp-toggle-button {
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  min-height: 2.5rem;
  padding: 0.5625rem 2.5rem 0.5625rem 1rem;
}
.vscomp-wrapper.has-clear-button .vscomp-toggle-button {
  padding-right: 2.125rem;
}
.vscomp-arrow {
  justify-content: flex-end;
}
.vscomp-arrow:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.254 1.726 9.074.546l-3.82 3.822L1.434.547.253 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100%;
  border: none;
  height: 6px;
  margin-right: 1rem;
  margin-top: 0;
  transform: none;
  width: 10px;
}
.vscomp-wrapper.focused .vscomp-toggle-button,
.vscomp-wrapper:focus .vscomp-toggle-button {
  border-color: #a5b4e9;
  box-shadow: none;
}
.vscomp-dropbox-container {
  background-color: var(--bs-white);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(51, 51, 51, 0.12);
  min-width: 16.875rem;
  padding: 0.625rem;
  transition: none;
  z-index: 1060 !important;
}
.vscomp-dropbox {
  background-color: transparent;
}
.vscomp-search-container {
  border-bottom: none;
  padding: 0 0.625rem;
}
.vscomp-search-input {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='23' height='23' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='9.667' cy='9.666' r='8' stroke='%23828282' stroke-width='2'/%3E%3Cpath d='M9.667 5.667a4 4 0 0 0-4 4M21.667 21.666l-4-4' stroke='%23828282' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
  background-position: center left 12px;
  background-repeat: no-repeat;
  font-size: 0.875rem;
  padding: 0.5625rem 0.5rem 0.5625rem 3rem;
}
.vscomp-wrapper.has-select-all .vscomp-search-input,
.vscomp-wrapper.has-select-all .vscomp-toggle-all-label {
  width: 100%;
}
.vscomp-option {
  align-items: center;
  border-radius: 0.375rem;
  color: #566171;
  display: flex;
  padding: 0.625rem;
}
.vscomp-option.selected {
  background-color: transparent;
}
.vscomp-option.focused {
  background-color: #edf5ff;
  color: #141735;
}
.vscomp-option.gui-duyet .checkbox-icon:before {
  background-color: #0d99ff, #dcf1ff;
}
.vscomp-option.cho-duyet .checkbox-icon:before {
  background-color: #f48620, #fff6ee;
}
.vscomp-option.da-duyet .checkbox-icon:before {
  background-color: #0c8809, #e3f0d9;
}
.vscomp-option.gui-dang .checkbox-icon:before {
  background-color: #458ab9, #ddeefa;
}
.vscomp-option.cho-dang .checkbox-icon:before {
  background-color: #ff6d19, #fcf2ec;
}
.vscomp-option.xuat-ban .checkbox-icon:before {
  background-color: #096d34, #e7f6ee;
}
.vscomp-option.bai-nhap .checkbox-icon:before {
  background-color: #8f969f, #ecf0f6;
}
.vscomp-option.da-huy .checkbox-icon:before {
  background-color: #6d1b1c, #f9e2e2;
}
.vscomp-option.tra-lai .checkbox-icon:before {
  background-color: #bf472d, #f8f1ea;
}
.vscomp-option.giu-cho .checkbox-icon:before {
  background-color: #f2586e, #fff0f1;
}
.vscomp-wrapper .checkbox-icon {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100%;
  border: 1px solid #cbd5e1;
  border-radius: 0.25rem;
  flex-shrink: 0;
  height: 1rem;
  margin-right: 0.5rem;
  margin-top: -0.125rem;
  width: 1rem;
}
.vscomp-wrapper .checkbox-icon:after {
  display: none;
}
.vscomp-wrapper .checkbox-icon.checked,
.vscomp-wrapper.multiple .vscomp-option.selected .checkbox-icon {
  background-color: #213b94;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3E%3C/svg%3E");
  border-color: #213b94;
}
.vscomp-wrapper.multiple .vscomp-option .vscomp-option-text {
  flex: 1 1 0;
  width: auto;
}
.vscomp-no-search-results {
  background-color: #fff;
  border: 1px solid #dfe7fc;
  color: #828282;
  justify-content: flex-start;
  padding: 1rem;
}
.vscomp-clear-button {
  right: 0.5625rem;
}
.vscomp-clear-button:focus-visible,
.vscomp-clear-button:hover {
  background: none;
}
.vscomp-clear-button:focus-visible .vscomp-clear-icon,
.vscomp-clear-button:hover .vscomp-clear-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.707 3.293a1 1 0 0 1 0 1.414l-8 8a1 1 0 0 1-1.414-1.414l8-8a1 1 0 0 1 1.414 0Z' fill='%23828282'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M3.293 3.293a1 1 0 0 1 1.414 0l8 8a1 1 0 0 1-1.414 1.414l-8-8a1 1 0 0 1 0-1.414Z' fill='%23828282'/%3E%3C/svg%3E");
}
.vscomp-clear-button .vscomp-clear-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.707 3.293a1 1 0 0 1 0 1.414l-8 8a1 1 0 0 1-1.414-1.414l8-8a1 1 0 0 1 1.414 0Z' fill='%23A4B4CB'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M3.293 3.293a1 1 0 0 1 1.414 0l8 8a1 1 0 0 1-1.414 1.414l-8-8a1 1 0 0 1 0-1.414Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100%;
  height: 1rem;
  width: 1rem;
}
.vscomp-clear-button .vscomp-clear-icon:after,
.vscomp-clear-button .vscomp-clear-icon:before,
.vscomp-wrapper.has-clear-button.has-value .vscomp-arrow,
[data-plugin="virtual-select"] {
  display: none;
}
.select2-container {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  position: relative;
  vertical-align: middle;
}
.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 28px;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  display: block;
  overflow: hidden;
  padding-left: 8px;
  padding-right: 20px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.select2-container .select2-selection--single .select2-selection__clear {
  background-color: transparent;
  border: none;
  font-size: 1em;
}
.select2-container[dir="rtl"]
  .select2-selection--single
  .select2-selection__rendered {
  padding-left: 20px;
  padding-right: 8px;
}
.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  min-height: 32px;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
  display: inline;
  list-style: none;
  padding: 0;
}
.select2-container .select2-selection--multiple .select2-selection__clear {
  background-color: transparent;
  border: none;
  font-size: 1em;
}
.select2-container .select2-search--inline .select2-search__field {
  border: none;
  box-sizing: border-box;
  font-family: sans-serif;
  font-size: 100%;
  height: 18px !important;
  margin-left: 5px;
  margin-top: 5px;
  max-width: 100%;
  overflow: hidden;
  padding: 0;
  resize: none;
  vertical-align: bottom;
  word-break: keep-all;
}
.select2-container
  .select2-search--inline
  .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.select2-dropdown {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: block;
  left: -100000px;
  position: absolute;
  width: 100%;
  z-index: 1051;
}
.select2-results {
  display: block;
}
.select2-results__options {
  list-style: none;
  margin: 0;
  padding: 0;
}
.select2-results__option {
  padding: 6px;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
}
.select2-results__option--selectable {
  cursor: pointer;
}
.select2-container--open .select2-dropdown {
  left: 0;
}
.select2-container--open .select2-dropdown--above {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.select2-search--dropdown {
  display: block;
  padding: 4px;
}
.select2-search--dropdown .select2-search__field {
  box-sizing: border-box;
  padding: 4px;
  width: 100%;
}
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
.select2-search--dropdown.select2-search--hide {
  display: none;
}
.select2-close-mask {
  background-color: #fff;
  border: 0;
  display: block;
  filter: alpha(opacity=0);
  height: auto;
  left: 0;
  margin: 0;
  min-height: 100%;
  min-width: 100%;
  opacity: 0;
  padding: 0;
  position: fixed;
  top: 0;
  width: auto;
  z-index: 99;
}
.select2-hidden-accessible {
  clip: rect(0 0 0 0) !important;
  border: 0 !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  white-space: nowrap !important;
  width: 1px !important;
}
.select2-container--default .select2-selection--single {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: 700;
  height: 26px;
  margin-right: 20px;
  padding-right: 0;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__placeholder {
  color: #999;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 26px;
  position: absolute;
  right: 1px;
  top: 1px;
  width: 20px;
}
.select2-container--default
  .select2-selection--single
  .select2-selection__arrow
  b {
  border-color: #888 transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}
.select2-container--default[dir="rtl"]
  .select2-selection--single
  .select2-selection__clear {
  float: left;
}
.select2-container--default[dir="rtl"]
  .select2-selection--single
  .select2-selection__arrow {
  left: 1px;
  right: auto;
}
.select2-container--default.select2-container--disabled
  .select2-selection--single {
  background-color: #eee;
  cursor: default;
}
.select2-container--default.select2-container--disabled
  .select2-selection--single
  .select2-selection__clear {
  display: none;
}
.select2-container--default.select2-container--open
  .select2-selection--single
  .select2-selection__arrow
  b {
  border-color: transparent transparent #888;
  border-width: 0 4px 5px;
}
.select2-container--default .select2-selection--multiple {
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
  cursor: text;
  padding-bottom: 5px;
  padding-right: 5px;
  position: relative;
}
.select2-container--default
  .select2-selection--multiple.select2-selection--clearable {
  padding-right: 25px;
}
.select2-container--default
  .select2-selection--multiple
  .select2-selection__clear {
  cursor: pointer;
  font-weight: 700;
  height: 20px;
  margin-right: 10px;
  margin-top: 5px;
  padding: 1px;
  position: absolute;
  right: 0;
}
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-sizing: border-box;
  display: inline-block;
  margin-left: 5px;
  margin-top: 5px;
  max-width: 100%;
  overflow: hidden;
  padding: 0 0 0 20px;
  position: relative;
  text-overflow: ellipsis;
  vertical-align: bottom;
  white-space: nowrap;
}
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice__display {
  cursor: default;
  padding-left: 2px;
  padding-right: 5px;
}
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice__remove {
  background-color: transparent;
  border: none;
  border-bottom-left-radius: 4px;
  border-right: 1px solid #aaa;
  border-top-left-radius: 4px;
  color: #999;
  cursor: pointer;
  font-size: 1em;
  font-weight: 700;
  left: 0;
  padding: 0 4px;
  position: absolute;
  top: 0;
}
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice__remove:focus,
.select2-container--default
  .select2-selection--multiple
  .select2-selection__choice__remove:hover {
  background-color: #f1f1f1;
  color: #333;
  outline: none;
}
.select2-container--default[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}
.select2-container--default[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice__display {
  padding-left: 5px;
  padding-right: 2px;
}
.select2-container--default[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice__remove {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 4px;
  border-left: 1px solid #aaa;
  border-right: none;
  border-top-left-radius: 0;
  border-top-right-radius: 4px;
}
.select2-container--default[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__clear {
  float: left;
  margin-left: 10px;
  margin-right: auto;
}
.select2-container--default.select2-container--focus
  .select2-selection--multiple {
  border: 1px solid #000;
  outline: 0;
}
.select2-container--default.select2-container--disabled
  .select2-selection--multiple {
  background-color: #eee;
  cursor: default;
}
.select2-container--default.select2-container--disabled
  .select2-selection__choice__remove {
  display: none;
}
.select2-container--default.select2-container--open.select2-container--above
  .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--above
  .select2-selection--single {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.select2-container--default.select2-container--open.select2-container--below
  .select2-selection--multiple,
.select2-container--default.select2-container--open.select2-container--below
  .select2-selection--single {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid #aaa;
}
.select2-container--default .select2-search--inline .select2-search__field {
  -webkit-appearance: textfield;
  background: transparent;
  border: none;
  box-shadow: none;
  outline: 0;
}
.select2-container--default .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}
.select2-container--default .select2-results__option .select2-results__option {
  padding-left: 1em;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__group {
  padding-left: 0;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__option {
  margin-left: -1em;
  padding-left: 2em;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option {
  margin-left: -2em;
  padding-left: 3em;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option {
  margin-left: -3em;
  padding-left: 4em;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option {
  margin-left: -4em;
  padding-left: 5em;
}
.select2-container--default
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option
  .select2-results__option {
  margin-left: -5em;
  padding-left: 6em;
}
.select2-container--default .select2-results__option--group {
  padding: 0;
}
.select2-container--default .select2-results__option--disabled {
  color: #999;
}
.select2-container--default .select2-results__option--selected {
  background-color: #ddd;
}
.select2-container--default
  .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #5897fb;
  color: #fff;
}
.select2-container--default .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}
.select2-container--classic .select2-selection--single {
  background-color: #f7f7f7;
  background-image: linear-gradient(180deg, #fff 50%, #eee);
  background-repeat: repeat-x;
  border: 1px solid #ddd;
  border-radius: 0.375rem;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFFFF",endColorstr="#FFEEEEEE",GradientType=0);
  outline: 0;
}
.select2-container--classic .select2-selection--single:focus {
  border: 1px solid #5897fb;
}
.select2-container--classic
  .select2-selection--single
  .select2-selection__rendered {
  color: #444;
  line-height: 28px;
}
.select2-container--classic
  .select2-selection--single
  .select2-selection__clear {
  cursor: pointer;
  float: right;
  font-weight: 700;
  height: 26px;
  margin-right: 20px;
}
.select2-container--classic
  .select2-selection--single
  .select2-selection__placeholder {
  color: #999;
}
.select2-container--classic
  .select2-selection--single
  .select2-selection__arrow {
  background-color: #ddd;
  background-image: linear-gradient(180deg, #eee 50%, #ccc);
  background-repeat: repeat-x;
  border: none;
  border-bottom-right-radius: 0.375rem;
  border-left: 1px solid #ddd;
  border-top-right-radius: 0.375rem;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFEEEEEE",endColorstr="#FFCCCCCC",GradientType=0);
  height: 26px;
  position: absolute;
  right: 1px;
  top: 1px;
  width: 20px;
}
.select2-container--classic
  .select2-selection--single
  .select2-selection__arrow
  b {
  border-color: #888 transparent transparent;
  border-style: solid;
  border-width: 5px 4px 0;
  height: 0;
  left: 50%;
  margin-left: -4px;
  margin-top: -2px;
  position: absolute;
  top: 50%;
  width: 0;
}
.select2-container--classic[dir="rtl"]
  .select2-selection--single
  .select2-selection__clear {
  float: left;
}
.select2-container--classic[dir="rtl"]
  .select2-selection--single
  .select2-selection__arrow {
  border: none;
  border-radius: 0;
  border-bottom-left-radius: 0.375rem;
  border-right: 1px solid #ddd;
  border-top-left-radius: 0.375rem;
  left: 1px;
  right: auto;
}
.select2-container--classic.select2-container--open .select2-selection--single {
  border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open
  .select2-selection--single
  .select2-selection__arrow {
  background: transparent;
  border: none;
}
.select2-container--classic.select2-container--open
  .select2-selection--single
  .select2-selection__arrow
  b {
  border-color: transparent transparent #888;
  border-width: 0 4px 5px;
}
.select2-container--classic.select2-container--open.select2-container--above
  .select2-selection--single {
  background-image: linear-gradient(180deg, #fff 0, #eee 50%);
  background-repeat: repeat-x;
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFFFFFFF",endColorstr="#FFEEEEEE",GradientType=0);
}
.select2-container--classic.select2-container--open.select2-container--below
  .select2-selection--single {
  background-image: linear-gradient(180deg, #eee 50%, #fff);
  background-repeat: repeat-x;
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFEEEEEE",endColorstr="#FFFFFFFF",GradientType=0);
}
.select2-container--classic .select2-selection--multiple {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 0.375rem;
  cursor: text;
  outline: 0;
  padding-bottom: 5px;
  padding-right: 5px;
}
.select2-container--classic .select2-selection--multiple:focus {
  border: 1px solid #5897fb;
}
.select2-container--classic
  .select2-selection--multiple
  .select2-selection__clear {
  display: none;
}
.select2-container--classic
  .select2-selection--multiple
  .select2-selection__choice {
  background-color: #e4e4e4;
  border: 1px solid #ddd;
  border-radius: 0.375rem;
  display: inline-block;
  margin-left: 5px;
  margin-top: 5px;
  padding: 0;
}
.select2-container--classic
  .select2-selection--multiple
  .select2-selection__choice__display {
  cursor: default;
  padding-left: 2px;
  padding-right: 5px;
}
.select2-container--classic
  .select2-selection--multiple
  .select2-selection__choice__remove {
  background-color: transparent;
  border: none;
  border-bottom-left-radius: 0.375rem;
  border-top-left-radius: 0.375rem;
  color: #888;
  cursor: pointer;
  font-size: 1em;
  font-weight: 700;
  padding: 0 4px;
}
.select2-container--classic
  .select2-selection--multiple
  .select2-selection__choice__remove:hover {
  color: #555;
  outline: none;
}
.select2-container--classic[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice {
  margin-left: 5px;
  margin-right: auto;
}
.select2-container--classic[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice__display {
  padding-left: 5px;
  padding-right: 2px;
}
.select2-container--classic[dir="rtl"]
  .select2-selection--multiple
  .select2-selection__choice__remove {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0.375rem;
}
.select2-container--classic.select2-container--open
  .select2-selection--multiple {
  border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open.select2-container--above
  .select2-selection--multiple {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.select2-container--classic.select2-container--open.select2-container--below
  .select2-selection--multiple {
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.select2-container--classic .select2-search--dropdown .select2-search__field {
  border: 1px solid #ddd;
  outline: 0;
}
.select2-container--classic .select2-search--inline .select2-search__field {
  box-shadow: none;
  outline: 0;
}
.select2-container--classic .select2-dropdown {
  background-color: #fff;
  border: 1px solid transparent;
}
.select2-container--classic .select2-dropdown--above {
  border-bottom: none;
}
.select2-container--classic .select2-dropdown--below {
  border-top: none;
}
.select2-container--classic .select2-results > .select2-results__options {
  max-height: 200px;
  overflow-y: auto;
}
.select2-container--classic .select2-results__option--group {
  padding: 0;
}
.select2-container--classic .select2-results__option--disabled {
  color: grey;
}
.select2-container--classic
  .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #3875d7;
  color: #fff;
}
.select2-container--classic .select2-results__group {
  cursor: default;
  display: block;
  padding: 6px;
}
.select2-container--classic.select2-container--open .select2-dropdown {
  border-color: #5897fb;
}
[data-plugin="select2"] {
  visibility: hidden;
}
[data-plugin="select2"].select2-hidden-accessible {
  display: none;
}
:root {
  --select2-single-height: 2.5rem;
  --select2-space-x: 1rem;
  --select2-space-y: 0.5625rem;
  --select2-border-radius: 0.375rem;
  --select2-color: #566171;
  --select2-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.754 1.726 9.574.546l-3.82 3.822L1.934.547.753 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  --select2-icon-size: 11px 7px;
  --select2-icon-position-right: 1rem;
  --select2-dropdown-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
.select2 {
  width: 100% !important;
}
.select2 .select2-selection {
  background-color: #fff;
  border: 1px solid #dee4f0;
  border-radius: var(--select2-border-radius) !important;
  height: var(--select2-single-height);
  outline: none;
}
.select2 .select2-selection .select2-selection__rendered {
  color: #141735;
  flex-grow: 1;
  margin-bottom: 0;
  padding-left: var(--select2-space-x);
  padding-right: 2.5rem;
}
.select2 .select2-selection .select2-selection__arrow {
  background-image: var(--select2-icon);
  background-position: right var(--select2-icon-position-right) center;
  background-repeat: no-repeat;
  background-size: var(--select2-icon-size);
  bottom: 1px;
  height: calc(var(--select2-single-height) - 2px);
  width: 2.125rem;
}
.select2 .select2-selection .select2-selection__arrow > *,
.select2
  .select2-selection.select2-selection--clearable
  .select2-selection__arrow {
  display: none;
}
.select2 .select2-selection .select2-selection__clear {
  align-items: center;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.707 3.293a1 1 0 0 1 0 1.414l-8 8a1 1 0 0 1-1.414-1.414l8-8a1 1 0 0 1 1.414 0Z' fill='%23A4B4CB'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M3.293 3.293a1 1 0 0 1 1.414 0l8 8a1 1 0 0 1-1.414 1.414l-8-8a1 1 0 0 1 0-1.414Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-position: 50%;
  background-repeat: no-repeat;
  display: flex;
  height: 1.5rem;
  justify-content: center;
  margin-right: 0;
  padding: 0;
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
}
.select2 .select2-selection .select2-selection__clear:focus-visible,
.select2 .select2-selection .select2-selection__clear:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.707 3.293a1 1 0 0 1 0 1.414l-8 8a1 1 0 0 1-1.414-1.414l8-8a1 1 0 0 1 1.414 0Z' fill='%23828282'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M3.293 3.293a1 1 0 0 1 1.414 0l8 8a1 1 0 0 1-1.414 1.414l-8-8a1 1 0 0 1 0-1.414Z' fill='%23828282'/%3E%3C/svg%3E");
}
.select2 .select2-selection .select2-selection__clear > span {
  display: none;
}
.select2 .select2-selection .select2-selection__placeholder {
  color: #566171;
}
.select2 .select2-selection--single .select2-selection__rendered {
  overflow: hidden;
  padding-bottom: 0.3125rem;
  padding-top: 0.3125rem;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.select2-container.select2-container--focus .select2-selection,
.select2-container.select2-container--open .select2-selection {
  border-color: #a5b4e9;
}
.select2-container--default .select2-dropdown {
  background-color: #fff;
  border: 0 solid #dfe7fc;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(51, 51, 51, 0.12);
  padding: 0.625rem;
  z-index: 1065;
}
.select2-container--default .select2-dropdown .select2-results__options {
  margin-right: -0.625rem;
  padding-right: 0.625rem;
}
.select2-container--default .select2-dropdown .select2-results__option {
  border-radius: 0.375rem;
  color: var(--select2-color);
  padding: 0.625rem;
}
.select2-container--default
  .select2-dropdown
  .select2-results__option:not(:last-child) {
  margin-bottom: 1px;
}
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted,
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected {
  background-color: #edf5ff;
  color: #141735;
}
@media (min-width: 992px) {
  .select2-container--default .select2-results > .select2-results__options {
    max-height: 18.75rem;
  }
}
.select2-container--default
  .select2-dropdown-has-check
  .select2-results__option--selected {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='15' height='13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m1 9 4 3 9-11' stroke='%23377DFF'/%3E%3C/svg%3E");
  background-position: center right 10px;
  background-repeat: no-repeat;
  padding-right: 2.5rem;
}
.select2-container--default .select2-search--dropdown {
  margin-bottom: 0.5rem;
  padding: 0;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='23' height='23' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='9.667' cy='9.666' r='8' stroke='%23828282' stroke-width='2'/%3E%3Cpath d='M9.667 5.667a4 4 0 0 0-4 4M21.667 21.666l-4-4' stroke='%23828282' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E");
  background-position: center left 12px;
  background-repeat: no-repeat;
  border: 1px solid #dfe7fc;
  border-radius: 0.1875rem;
  border-radius: 0.375rem;
  min-height: 2.5rem;
  outline: none;
  padding: var(--select2-space-y) var(--select2-space-x) var(--select2-space-y)
    3rem;
}
.select2-container--open .select2-dropdown--below {
  margin-top: 0.5rem;
}
.select2-container--open .select2-dropdown--above {
  margin-top: -0.5rem;
}
.select2-container--open .select2-dropdown-noborder {
  border: none;
  box-shadow: var(--select2-dropdown-shadow);
}
.form-control-primary + .select2 .select2-selection {
  background-color: #edf5ff;
  border-color: transparent;
}
.is-warning + .select2 .select2-selection {
  border-color: #ffb36d;
}
.select2 .select2-selection--tagging {
  height: auto;
  min-height: var(--select2-single-height);
  padding-bottom: 0.375rem;
  padding-left: calc(var(--select2-space-x) - 0.25rem);
  padding-top: 0.125rem;
}
.select2 .select2-selection--tagging .select2-selection__rendered {
  padding-left: 0;
  padding-right: 0;
}
.select2 .select2-selection--tagging .select2-selection__choice {
  background-color: transparent;
  border-color: #a4b4cb;
  border-radius: 50rem;
  color: #213b94;
  margin-left: 0.25rem;
  margin-top: 0.25rem;
  min-height: 1.625rem;
  padding: 0.125rem 1.5rem 0.125rem 0.5rem;
}
.select2 .select2-selection--tagging .select2-selection__choice__display {
  padding: 0;
}
.select2 .select2-selection--tagging .select2-selection__choice__remove {
  align-items: center;
  border: none;
  bottom: 0;
  color: #a4b4cb;
  display: flex;
  font-size: 1.25rem;
  justify-content: center;
  left: auto;
  line-height: 1;
  right: 2px;
}
.select2
  .select2-selection--tagging
  .select2-selection__choice__remove:focus-visible,
.select2 .select2-selection--tagging .select2-selection__choice__remove:hover {
  background-color: transparent;
  color: #8390a2;
}
.select2 .select2-selection--tagging .select2-search__field {
  margin-top: 0.6875rem;
  transform: translateY(-0.25rem);
}
.select2 .select2-selection--tagging .select2-selection__clear {
  margin-top: 0;
}
.select2-container--default .select2-results__group {
  padding: 0;
}
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  box-sizing: border-box;
  touch-action: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.noUi-target {
  position: relative;
}
.noUi-base,
.noUi-connects {
  height: 100%;
  position: relative;
  width: 100%;
  z-index: 1;
}
.noUi-connects {
  overflow: hidden;
  z-index: 0;
}
.noUi-connect,
.noUi-origin {
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform-style: preserve-3d;
  transform-style: flat;
  width: 100%;
  will-change: transform;
  z-index: 1;
}
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto;
}
.noUi-vertical .noUi-origin {
  top: -100%;
  width: 0;
}
.noUi-horizontal .noUi-origin {
  height: 0;
}
.noUi-handle {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
}
.noUi-touch-area {
  height: 100%;
  width: 100%;
}
.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  transition: transform 0.3s;
}
.noUi-state-drag * {
  cursor: inherit !important;
}
.noUi-horizontal {
  height: 18px;
}
.noUi-horizontal .noUi-handle {
  height: 28px;
  right: -17px;
  top: -6px;
  width: 34px;
}
.noUi-vertical {
  width: 18px;
}
.noUi-vertical .noUi-handle {
  bottom: -17px;
  height: 34px;
  right: -6px;
  width: 28px;
}
.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto;
}
.noUi-target {
  background: #fafafa;
  border: 1px solid #d3d3d3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px #f0f0f0, 0 3px 6px -5px #bbb;
}
.noUi-connects {
  border-radius: 3px;
}
.noUi-connect {
  background: #3fb8af;
}
.noUi-draggable {
  cursor: ew-resize;
}
.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}
.noUi-handle {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ebebeb, 0 3px 6px -3px #bbb;
  cursor: default;
}
.noUi-active {
  box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ddd, 0 3px 6px -3px #bbb;
}
.noUi-handle:after,
.noUi-handle:before {
  background: #e8e7e6;
  content: "";
  display: block;
  height: 14px;
  left: 14px;
  position: absolute;
  top: 6px;
  width: 1px;
}
.noUi-handle:after {
  left: 17px;
}
.noUi-vertical .noUi-handle:after,
.noUi-vertical .noUi-handle:before {
  height: 1px;
  left: 6px;
  top: 14px;
  width: 14px;
}
.noUi-vertical .noUi-handle:after {
  top: 17px;
}
[disabled] .noUi-connect {
  background: #b8b8b8;
}
[disabled] .noUi-handle,
[disabled].noUi-handle,
[disabled].noUi-target {
  cursor: not-allowed;
}
.noUi-pips,
.noUi-pips * {
  box-sizing: border-box;
}
.noUi-pips {
  color: #999;
  position: absolute;
}
.noUi-value {
  position: absolute;
  text-align: center;
  white-space: nowrap;
}
.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}
.noUi-marker {
  background: #ccc;
  position: absolute;
}
.noUi-marker-large,
.noUi-marker-sub {
  background: #aaa;
}
.noUi-pips-horizontal {
  height: 80px;
  left: 0;
  padding: 10px 0;
  top: 100%;
  width: 100%;
}
.noUi-value-horizontal {
  transform: translate(-50%, 50%);
}
.noUi-rtl .noUi-value-horizontal {
  transform: translate(50%, 50%);
}
.noUi-marker-horizontal.noUi-marker {
  height: 5px;
  margin-left: -1px;
  width: 2px;
}
.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}
.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}
.noUi-pips-vertical {
  height: 100%;
  left: 100%;
  padding: 0 10px;
  top: 0;
}
.noUi-value-vertical {
  padding-left: 25px;
  transform: translateY(-50%);
}
.noUi-rtl .noUi-value-vertical {
  transform: translateY(50%);
}
.noUi-marker-vertical.noUi-marker {
  height: 2px;
  margin-top: -1px;
  width: 5px;
}
.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}
.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}
.noUi-tooltip {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  color: #000;
  display: block;
  padding: 5px;
  position: absolute;
  text-align: center;
  white-space: nowrap;
}
.noUi-horizontal .noUi-tooltip {
  bottom: 120%;
  left: 50%;
  transform: translate(-50%);
}
.noUi-vertical .noUi-tooltip {
  right: 120%;
  top: 50%;
  transform: translateY(-50%);
}
.noUi-horizontal .noUi-origin > .noUi-tooltip {
  bottom: 10px;
  left: auto;
  transform: translate(50%);
}
.noUi-vertical .noUi-origin > .noUi-tooltip {
  right: 28px;
  top: auto;
  transform: translateY(-18px);
}
.noUi-target {
  --nouislider-height: 0.5rem;
  --nouislider-handle-bg: var(--bs-primary);
  --nouislider-handle-width: var(--nouislider-height);
  --nouislider-handle-height: var(--nouislider-handle-width);
  --nouislider-connects-height: 0.5rem;
  --nouislider-connects-border-radius: var(--bs-border-radius-sm);
  --nouislider-connects-bg: var(--bs-gray-200);
  --nouislider-connect: var(--bs-primary);
  --nouislider-tooltip-bg: var(--bs-primary);
  --nouislider-tooltip-color: var(--bs-body-color);
  --nouislider-tooltip-border-radius: var(--bs-border-radius-sm);
  background-color: transparent;
  border: none;
  box-shadow: none;
}
.noUi-base {
  align-items: center;
  display: flex;
  justify-content: center;
}
.noUi-connects {
  background-color: var(--nouislider-connects-bg);
  border-radius: var(--nouislider-connects-border-radius);
}
.noUi-horizontal .noUi-connects {
  height: var(--nouislider-connects-height);
}
.noUi-vertical .noUi-connects {
  width: var(--nouislider-connects-height);
}
.noUi-connect {
  background-color: var(--nouislider-connect);
}
.noUi-handle {
  background-color: #2c3f7e;
  border: none;
  border-radius: 50%;
  box-shadow: none;
  cursor: pointer;
  outline: none;
}
.noUi-handle:after,
.noUi-handle:before {
  display: none;
}
.noUi-horizontal .noUi-handle {
  height: var(--nouislider-handle-width);
  right: calc(var(--nouislider-handle-width) * -0.5);
  top: calc(
    (var(--nouislider-handle-height) - var(--nouislider-height)) * -0.5
  );
  width: var(--nouislider-handle-width);
}
.noUi-vertical .noUi-handle {
  height: 1.5rem;
  right: calc(
    (var(--nouislider-handle-height) - var(--nouislider-height)) * -0.5
  );
  top: calc(var(--nouislider-handle-width) * -0.5);
  width: 1.5rem;
}
.noUi-touch-area {
  height: 200%;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 200%;
}
.noUi-tooltip {
  border: none;
  border-radius: var(--nouislider-tooltip-border-radius);
  color: var(--nouislider-tooltip-color);
  font-size: 0.75rem;
  line-height: 1;
  padding: 0.25rem;
}
.noUi-horizontal .noUi-tooltip {
  bottom: auto;
  margin-top: 0.25rem;
  top: 100%;
}
.noUi-horizontal .noUi-tooltip:after {
  display: none;
}
.noUi-vertical .noUi-tooltip {
  margin-right: 0.5rem;
  right: 100%;
}
.noUi-vertical .noUi-tooltip:after {
  border-color: transparent transparent transparent #4a69d2;
  border-style: solid;
  border-width: 0.1875rem 0 0.1875rem 0.1875rem;
  content: "";
  height: 0;
  left: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
}
.noUi-horizontal {
  flex-grow: 1;
  height: var(--nouislider-height);
}
.noUi-vertical {
  height: 12.5rem;
  width: var(--nouislider-height);
}
.noUi-max,
.noUi-min {
  bottom: 100%;
  color: #566171;
  font-size: 0.75rem;
  line-height: 1;
  margin-bottom: 0.5rem;
  position: absolute;
}
.noUi-min {
  left: 0;
}
.noUi-max {
  right: 0;
}
.swal2-popup.swal2-toast {
  background: #24346a;
  box-shadow: 0 4px 24px rgba(36, 52, 106, 0.2),
    0 4px 12px rgba(36, 52, 106, 0.05);
  box-sizing: border-box;
  grid-column: 1/4 !important;
  grid-row: 1/4 !important;
  grid-template-columns: min-content auto min-content;
  overflow-y: hidden;
  padding: 1rem 1.5rem;
  pointer-events: all;
}
.swal2-popup.swal2-toast > * {
  grid-column: 2;
}
.swal2-popup.swal2-toast .swal2-title {
  font-size: 1.1875rem;
  margin: 0;
  padding: 0;
  text-align: initial;
}
.swal2-popup.swal2-toast .swal2-loading {
  justify-content: center;
}
.swal2-popup.swal2-toast .swal2-input {
  font-size: 1em;
  height: 2em;
  margin: 0.5em;
}
.swal2-popup.swal2-toast .swal2-validation-message {
  font-size: 1em;
}
.swal2-popup.swal2-toast .swal2-footer {
  font-size: 0.8em;
  margin: 0.5em 0 0;
  padding: 0.5em 0 0;
}
.swal2-popup.swal2-toast .swal2-close {
  align-self: center;
  font-size: 1.25rem;
  grid-column: 3/3;
  grid-row: 1/99;
  height: 1em;
  margin: 0;
  width: 1em;
}
.swal2-popup.swal2-toast .swal2-html-container {
  font-size: 0.875rem;
  margin: 0.25rem 0 0;
  overflow: initial;
  padding: 0;
  text-align: initial;
}
.swal2-popup.swal2-toast .swal2-html-container:empty {
  padding: 0;
}
.swal2-popup.swal2-toast .swal2-loader {
  align-self: center;
  grid-column: 1;
  grid-row: 1/99;
  height: 2em;
  margin: 0.25em;
  width: 2em;
}
.swal2-popup.swal2-toast .swal2-icon {
  align-self: center;
  grid-column: 1;
  grid-row: 1/99;
  height: 2em;
  margin: 0 0.5em 0 0;
  min-width: 2em;
  width: 2em;
}
.swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
  align-items: center;
  display: flex;
  font-size: 1.25rem;
  font-weight: 700;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
  height: 2em;
  width: 2em;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^="swal2-x-mark-line"] {
  top: 0.875em;
  width: 1.375em;
}
.swal2-popup.swal2-toast
  .swal2-icon.swal2-error
  [class^="swal2-x-mark-line"][class$="left"] {
  left: 0.3125em;
}
.swal2-popup.swal2-toast
  .swal2-icon.swal2-error
  [class^="swal2-x-mark-line"][class$="right"] {
  right: 0.3125em;
}
.swal2-popup.swal2-toast .swal2-actions {
  height: auto;
  justify-content: flex-start;
  margin: 0.5em 0 0;
  padding: 0 0.5em;
}
.swal2-popup.swal2-toast .swal2-styled {
  font-size: 1em;
  margin: 0.25em 0.5em;
  padding: 0.4em 0.6em;
}
.swal2-popup.swal2-toast .swal2-success {
  border-color: #a5dc86;
}
.swal2-popup.swal2-toast .swal2-success [class^="swal2-success-circular-line"] {
  border-radius: 50%;
  height: 3em;
  position: absolute;
  transform: rotate(45deg);
  width: 1.6em;
}
.swal2-popup.swal2-toast
  .swal2-success
  [class^="swal2-success-circular-line"][class$="left"] {
  border-radius: 4em 0 0 4em;
  left: -0.5em;
  top: -0.8em;
  transform: rotate(-45deg);
  transform-origin: 2em 2em;
}
.swal2-popup.swal2-toast
  .swal2-success
  [class^="swal2-success-circular-line"][class$="right"] {
  border-radius: 0 4em 4em 0;
  left: 0.9375em;
  top: -0.25em;
  transform-origin: 0 1.5em;
}
.swal2-popup.swal2-toast .swal2-success .swal2-success-ring {
  height: 2em;
  width: 2em;
}
.swal2-popup.swal2-toast .swal2-success .swal2-success-fix {
  height: 2.6875em;
  left: 0.4375em;
  top: 0;
  width: 0.4375em;
}
.swal2-popup.swal2-toast .swal2-success [class^="swal2-success-line"] {
  height: 0.3125em;
}
.swal2-popup.swal2-toast
  .swal2-success
  [class^="swal2-success-line"][class$="tip"] {
  left: 0.1875em;
  top: 1.125em;
  width: 0.75em;
}
.swal2-popup.swal2-toast
  .swal2-success
  [class^="swal2-success-line"][class$="long"] {
  right: 0.1875em;
  top: 0.9375em;
  width: 1.375em;
}
.swal2-popup.swal2-toast
  .swal2-success.swal2-icon-show
  .swal2-success-line-tip {
  animation: swal2-toast-animate-success-line-tip 0.75s;
}
.swal2-popup.swal2-toast
  .swal2-success.swal2-icon-show
  .swal2-success-line-long {
  animation: swal2-toast-animate-success-line-long 0.75s;
}
.swal2-popup.swal2-toast.swal2-show {
  animation: swal2-toast-show 0.5s;
}
.swal2-popup.swal2-toast.swal2-hide {
  animation: swal2-toast-hide 0.1s forwards;
}
.swal2-container {
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
  display: grid;
  grid-template-areas: "top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";
  grid-template-rows: minmax(min-content, auto) minmax(min-content, auto) minmax(
      min-content,
      auto
    );
  height: 100%;
  inset: 0;
  overflow-x: hidden;
  padding: 0.625em;
  position: fixed;
  transition: background-color 0.1s;
  z-index: 1060;
}
.swal2-container.swal2-backdrop-show,
.swal2-container.swal2-noanimation {
  background: rgba(0, 0, 0, 0.4);
}
.swal2-container.swal2-backdrop-hide {
  background: transparent !important;
}
.swal2-container.swal2-bottom-start,
.swal2-container.swal2-center-start,
.swal2-container.swal2-top-start {
  grid-template-columns: minmax(0, 1fr) auto auto;
}
.swal2-container.swal2-bottom,
.swal2-container.swal2-center,
.swal2-container.swal2-top {
  grid-template-columns: auto minmax(0, 1fr) auto;
}
.swal2-container.swal2-bottom-end,
.swal2-container.swal2-center-end,
.swal2-container.swal2-top-end {
  grid-template-columns: auto auto minmax(0, 1fr);
}
.swal2-container.swal2-top-start > .swal2-popup {
  align-self: start;
}
.swal2-container.swal2-top > .swal2-popup {
  align-self: start;
  grid-column: 2;
  justify-self: center;
}
.swal2-container.swal2-top-end > .swal2-popup,
.swal2-container.swal2-top-right > .swal2-popup {
  align-self: start;
  grid-column: 3;
  justify-self: end;
}
.swal2-container.swal2-center-left > .swal2-popup,
.swal2-container.swal2-center-start > .swal2-popup {
  align-self: center;
  grid-row: 2;
}
.swal2-container.swal2-center > .swal2-popup {
  align-self: center;
  grid-column: 2;
  grid-row: 2;
  justify-self: center;
}
.swal2-container.swal2-center-end > .swal2-popup,
.swal2-container.swal2-center-right > .swal2-popup {
  align-self: center;
  grid-column: 3;
  grid-row: 2;
  justify-self: end;
}
.swal2-container.swal2-bottom-left > .swal2-popup,
.swal2-container.swal2-bottom-start > .swal2-popup {
  align-self: end;
  grid-column: 1;
  grid-row: 3;
}
.swal2-container.swal2-bottom > .swal2-popup {
  align-self: end;
  grid-column: 2;
  grid-row: 3;
  justify-self: center;
}
.swal2-container.swal2-bottom-end > .swal2-popup,
.swal2-container.swal2-bottom-right > .swal2-popup {
  align-self: end;
  grid-column: 3;
  grid-row: 3;
  justify-self: end;
}
.swal2-container.swal2-grow-fullscreen > .swal2-popup,
.swal2-container.swal2-grow-row > .swal2-popup {
  grid-column: 1/4;
  width: 100%;
}
.swal2-container.swal2-grow-column > .swal2-popup,
.swal2-container.swal2-grow-fullscreen > .swal2-popup {
  align-self: stretch;
  grid-row: 1/4;
}
.swal2-container.swal2-no-transition {
  transition: none !important;
}
.swal2-popup {
  background: #fff;
  border: none;
  border-radius: 0.75rem;
  box-sizing: border-box;
  color: #fff;
  display: none;
  font-family: inherit;
  font-size: 1rem;
  grid-template-columns: minmax(0, 100%);
  max-width: 100%;
  padding: 0 0 1.25em;
  position: relative;
  width: 32em;
}
.swal2-popup:focus {
  outline: none;
}
.swal2-popup.swal2-loading {
  overflow-y: hidden;
}
.swal2-title {
  word-wrap: break-word;
  color: inherit;
  font-size: 1.875em;
  font-weight: 600;
  margin: 0;
  max-width: 100%;
  padding: 0.8em 1em 0;
  position: relative;
  text-align: center;
  text-transform: none;
}
.swal2-actions {
  align-items: center;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 1.25em auto 0;
  padding: 0;
  width: auto;
  z-index: 1;
}
.swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
  opacity: 0.4;
}
.swal2-actions:not(.swal2-loading) .swal2-styled:hover {
  background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
}
.swal2-actions:not(.swal2-loading) .swal2-styled:active {
  background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
}
.swal2-loader {
  align-items: center;
  animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
  border-color: #2778c4 transparent;
  border-radius: 100%;
  border-style: solid;
  border-width: 0.25em;
  display: none;
  height: 2.2em;
  justify-content: center;
  margin: 0 1.875em;
  width: 2.2em;
}
.swal2-styled {
  box-shadow: 0 0 0 3px transparent;
  font-weight: 500;
  margin: 0.3125em;
  padding: 0.625em 1.1em;
  transition: box-shadow 0.1s;
}
.swal2-styled:not([disabled]) {
  cursor: pointer;
}
.swal2-styled.swal2-confirm {
  background: initial;
  background-color: #7066e0;
  border: 0;
  border-radius: 0.25em;
  color: #fff;
  font-size: 1em;
}
.swal2-styled.swal2-confirm:focus {
  box-shadow: 0 0 0 3px rgba(112, 102, 224, 0.5);
}
.swal2-styled.swal2-deny {
  background: initial;
  background-color: #dc3741;
  border: 0;
  border-radius: 0.25em;
  color: #fff;
  font-size: 1em;
}
.swal2-styled.swal2-deny:focus {
  box-shadow: 0 0 0 3px rgba(220, 55, 65, 0.5);
}
.swal2-styled.swal2-cancel {
  background: initial;
  background-color: #6e7881;
  border: 0;
  border-radius: 0.25em;
  color: #fff;
  font-size: 1em;
}
.swal2-styled.swal2-cancel:focus {
  box-shadow: 0 0 0 3px hsla(208, 8%, 47%, 0.5);
}
.swal2-styled.swal2-default-outline:focus {
  box-shadow: 0 0 0 3px rgba(100, 150, 200, 0.5);
}
.swal2-styled:focus {
  outline: none;
}
.swal2-styled::-moz-focus-inner {
  border: 0;
}
.swal2-footer {
  border-top: 1px solid #eee;
  color: inherit;
  font-size: 1em;
  justify-content: center;
  margin: 1em 0 0;
  padding: 1em 1em 0;
}
.swal2-timer-progress-bar-container {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  bottom: 0;
  grid-column: auto !important;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
}
.swal2-timer-progress-bar {
  background: rgba(0, 0, 0, 0.2);
  height: 0.25em;
  width: 100%;
}
.swal2-image {
  margin: 2em auto 1em;
  max-width: 100%;
}
.swal2-close {
  align-items: center;
  background: transparent;
  border: none;
  border-radius: 0.75rem;
  color: #ccc;
  cursor: pointer;
  font-family: monospace;
  font-size: 2.5em;
  height: 1.2em;
  justify-content: center;
  justify-self: end;
  margin-bottom: -1.2em;
  margin-right: 0;
  margin-top: 0;
  overflow: hidden;
  padding: 0;
  transition: color 0.1s, box-shadow 0.1s;
  width: 1.2em;
  z-index: 2;
}
.swal2-close:hover {
  background: transparent;
  color: #f27474;
  transform: none;
}
.swal2-close:focus {
  box-shadow: inset 0 0 0 3px rgba(100, 150, 200, 0.5);
  outline: none;
}
.swal2-close::-moz-focus-inner {
  border: 0;
}
.swal2-html-container {
  word-wrap: break-word;
  color: inherit;
  font-size: 1.125em;
  font-weight: 400;
  justify-content: center;
  line-height: normal;
  margin: 1em 1.6em 0.3em;
  overflow: auto;
  padding: 0;
  text-align: center;
  word-break: break-word;
  z-index: 1;
}
.swal2-checkbox,
.swal2-file,
.swal2-input,
.swal2-radio,
.swal2-select,
.swal2-textarea {
  margin: 1em 2em 3px;
}
.swal2-file,
.swal2-input,
.swal2-textarea {
  background: transparent;
  border: 1px solid #d9d9d9;
  border-radius: 0.1875em;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;
  box-sizing: border-box;
  color: inherit;
  font-size: 1.125em;
  transition: border-color 0.1s, box-shadow 0.1s;
  width: auto;
}
.swal2-file.swal2-inputerror,
.swal2-input.swal2-inputerror,
.swal2-textarea.swal2-inputerror {
  border-color: #f27474 !important;
  box-shadow: 0 0 2px #f27474 !important;
}
.swal2-file:focus,
.swal2-input:focus,
.swal2-textarea:focus {
  border: 1px solid #b4dbed;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06),
    0 0 0 3px rgba(100, 150, 200, 0.5);
  outline: none;
}
.swal2-file::-moz-placeholder,
.swal2-input::-moz-placeholder,
.swal2-textarea::-moz-placeholder {
  color: #ccc;
}
.swal2-file::placeholder,
.swal2-input::placeholder,
.swal2-textarea::placeholder {
  color: #ccc;
}
.swal2-range {
  background: #fff;
  margin: 1em 2em 3px;
}
.swal2-range input {
  width: 80%;
}
.swal2-range output {
  color: inherit;
  font-weight: 600;
  text-align: center;
  width: 20%;
}
.swal2-range input,
.swal2-range output {
  font-size: 1.125em;
  height: 2.625em;
  line-height: 2.625em;
  padding: 0;
}
.swal2-input {
  height: 2.625em;
  padding: 0 0.75em;
}
.swal2-file {
  background: transparent;
  font-size: 1.125em;
  margin-left: auto;
  margin-right: auto;
  width: 75%;
}
.swal2-textarea {
  height: 6.75em;
  padding: 0.75em;
}
.swal2-select {
  background: transparent;
  color: inherit;
  font-size: 1.125em;
  max-width: 100%;
  min-width: 50%;
  padding: 0.375em 0.625em;
}
.swal2-checkbox,
.swal2-radio {
  align-items: center;
  background: #fff;
  color: inherit;
  justify-content: center;
}
.swal2-checkbox label,
.swal2-radio label {
  font-size: 1.125em;
  margin: 0 0.6em;
}
.swal2-checkbox input,
.swal2-radio input {
  flex-shrink: 0;
  margin: 0 0.4em;
}
.swal2-input-label {
  display: flex;
  justify-content: center;
  margin: 1em auto 0;
}
.swal2-validation-message {
  align-items: center;
  background: #f0f0f0;
  color: #666;
  font-size: 1em;
  font-weight: 300;
  justify-content: center;
  margin: 1em 0 0;
  overflow: hidden;
  padding: 0.625em;
}
.swal2-validation-message:before {
  background-color: #f27474;
  border-radius: 50%;
  color: #fff;
  content: "!";
  display: inline-block;
  font-weight: 600;
  height: 1.5em;
  line-height: 1.5em;
  margin: 0 0.625em;
  min-width: 1.5em;
  text-align: center;
  width: 1.5em;
}
.swal2-icon {
  border: 0.25em solid #000;
  border-radius: 50%;
  box-sizing: content-box;
  cursor: default;
  font-family: inherit;
  height: 5em;
  justify-content: center;
  line-height: 5em;
  margin: 2.5em auto 0.6em;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  width: 5em;
}
.swal2-icon .swal2-icon-content {
  align-items: center;
  display: flex;
  font-size: 3.75em;
}
.swal2-icon.swal2-error {
  border-color: #f27474;
  color: #f27474;
}
.swal2-icon.swal2-error .swal2-x-mark {
  flex-grow: 1;
  position: relative;
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"] {
  background-color: #f27474;
  border-radius: 0.125em;
  display: block;
  height: 0.3125em;
  position: absolute;
  top: 2.3125em;
  width: 2.9375em;
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"][class$="left"] {
  left: 1.0625em;
  transform: rotate(45deg);
}
.swal2-icon.swal2-error [class^="swal2-x-mark-line"][class$="right"] {
  right: 1em;
  transform: rotate(-45deg);
}
.swal2-icon.swal2-error.swal2-icon-show {
  animation: swal2-animate-error-icon 0.5s;
}
.swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark {
  animation: swal2-animate-error-x-mark 0.5s;
}
.swal2-icon.swal2-warning {
  border-color: #facea8;
  color: #f8bb86;
}
.swal2-icon.swal2-warning.swal2-icon-show {
  animation: swal2-animate-error-icon 0.5s;
}
.swal2-icon.swal2-warning.swal2-icon-show .swal2-icon-content {
  animation: swal2-animate-i-mark 0.5s;
}
.swal2-icon.swal2-info {
  border-color: #9de0f6;
  color: #3fc3ee;
}
.swal2-icon.swal2-info.swal2-icon-show {
  animation: swal2-animate-error-icon 0.5s;
}
.swal2-icon.swal2-info.swal2-icon-show .swal2-icon-content {
  animation: swal2-animate-i-mark 0.8s;
}
.swal2-icon.swal2-question {
  border-color: #c9dae1;
  color: #87adbd;
}
.swal2-icon.swal2-question.swal2-icon-show {
  animation: swal2-animate-error-icon 0.5s;
}
.swal2-icon.swal2-question.swal2-icon-show .swal2-icon-content {
  animation: swal2-animate-question-mark 0.8s;
}
.swal2-icon.swal2-success {
  border-color: #a5dc86;
  color: #a5dc86;
}
.swal2-icon.swal2-success [class^="swal2-success-circular-line"] {
  border-radius: 50%;
  height: 7.5em;
  position: absolute;
  transform: rotate(45deg);
  width: 3.75em;
}
.swal2-icon.swal2-success
  [class^="swal2-success-circular-line"][class$="left"] {
  border-radius: 7.5em 0 0 7.5em;
  left: -2.0635em;
  top: -0.4375em;
  transform: rotate(-45deg);
  transform-origin: 3.75em 3.75em;
}
.swal2-icon.swal2-success
  [class^="swal2-success-circular-line"][class$="right"] {
  border-radius: 0 7.5em 7.5em 0;
  left: 1.875em;
  top: -0.6875em;
  transform: rotate(-45deg);
  transform-origin: 0 3.75em;
}
.swal2-icon.swal2-success .swal2-success-ring {
  border: 0.25em solid hsla(98, 55%, 69%, 0.3);
  border-radius: 50%;
  box-sizing: content-box;
  height: 100%;
  left: -0.25em;
  position: absolute;
  top: -0.25em;
  width: 100%;
  z-index: 2;
}
.swal2-icon.swal2-success .swal2-success-fix {
  height: 5.625em;
  left: 1.625em;
  position: absolute;
  top: 0.5em;
  transform: rotate(-45deg);
  width: 0.4375em;
  z-index: 1;
}
.swal2-icon.swal2-success [class^="swal2-success-line"] {
  background-color: #a5dc86;
  border-radius: 0.125em;
  display: block;
  height: 0.3125em;
  position: absolute;
  z-index: 2;
}
.swal2-icon.swal2-success [class^="swal2-success-line"][class$="tip"] {
  left: 0.8125em;
  top: 2.875em;
  transform: rotate(45deg);
  width: 1.5625em;
}
.swal2-icon.swal2-success [class^="swal2-success-line"][class$="long"] {
  right: 0.5em;
  top: 2.375em;
  transform: rotate(-45deg);
  width: 2.9375em;
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip {
  animation: swal2-animate-success-line-tip 0.75s;
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long {
  animation: swal2-animate-success-line-long 0.75s;
}
.swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right {
  animation: swal2-rotate-success-circular-line 4.25s ease-in;
}
.swal2-progress-steps {
  align-items: center;
  background: transparent;
  flex-wrap: wrap;
  font-weight: 600;
  margin: 1.25em auto;
  max-width: 100%;
  padding: 0;
}
.swal2-progress-steps li {
  display: inline-block;
  position: relative;
}
.swal2-progress-steps .swal2-progress-step {
  background: #2778c4;
  border-radius: 2em;
  color: #fff;
  flex-shrink: 0;
  height: 2em;
  line-height: 2em;
  text-align: center;
  width: 2em;
  z-index: 20;
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
  background: #2778c4;
}
.swal2-progress-steps
  .swal2-progress-step.swal2-active-progress-step
  ~ .swal2-progress-step {
  background: #add8e6;
  color: #fff;
}
.swal2-progress-steps
  .swal2-progress-step.swal2-active-progress-step
  ~ .swal2-progress-step-line {
  background: #add8e6;
}
.swal2-progress-steps .swal2-progress-step-line {
  background: #2778c4;
  flex-shrink: 0;
  height: 0.4em;
  margin: 0 -1px;
  width: 2.5em;
  z-index: 10;
}
[class^="swal2"] {
  -webkit-tap-highlight-color: transparent;
}
.swal2-show {
  animation: swal2-show 0.3s;
}
.swal2-hide {
  animation: swal2-hide 0.15s forwards;
}
.swal2-noanimation {
  transition: none;
}
.swal2-scrollbar-measure {
  height: 50px;
  overflow: scroll;
  position: absolute;
  top: -9999px;
  width: 50px;
}
.swal2-rtl .swal2-close {
  margin-left: 0;
  margin-right: 0;
}
.swal2-rtl .swal2-timer-progress-bar {
  left: auto;
  right: 0;
}
@keyframes swal2-toast-animate-success-line-tip {
  0% {
    left: 0.0625em;
    top: 0.5625em;
    width: 0;
  }
  54% {
    left: 0.125em;
    top: 0.125em;
    width: 0;
  }
  70% {
    left: -0.25em;
    top: 0.625em;
    width: 1.625em;
  }
  84% {
    left: 0.75em;
    top: 1.0625em;
    width: 0.5em;
  }
  to {
    left: 0.1875em;
    top: 1.125em;
    width: 0.75em;
  }
}
@keyframes swal2-toast-animate-success-line-long {
  0% {
    right: 1.375em;
    top: 1.625em;
    width: 0;
  }
  65% {
    right: 0.9375em;
    top: 1.25em;
    width: 0;
  }
  84% {
    right: 0;
    top: 0.9375em;
    width: 1.125em;
  }
  to {
    right: 0.1875em;
    top: 0.9375em;
    width: 1.375em;
  }
}
@keyframes swal2-show {
  0% {
    transform: scale(0.7);
  }
  45% {
    transform: scale(1.05);
  }
  80% {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}
@keyframes swal2-hide {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.5);
  }
}
@keyframes swal2-animate-success-line-tip {
  0% {
    left: 0.0625em;
    top: 1.1875em;
    width: 0;
  }
  54% {
    left: 0.125em;
    top: 1.0625em;
    width: 0;
  }
  70% {
    left: -0.375em;
    top: 2.1875em;
    width: 3.125em;
  }
  84% {
    left: 1.3125em;
    top: 3em;
    width: 1.0625em;
  }
  to {
    left: 0.8125em;
    top: 2.8125em;
    width: 1.5625em;
  }
}
@keyframes swal2-animate-success-line-long {
  0% {
    right: 2.875em;
    top: 3.375em;
    width: 0;
  }
  65% {
    right: 2.875em;
    top: 3.375em;
    width: 0;
  }
  84% {
    right: 0;
    top: 2.1875em;
    width: 3.4375em;
  }
  to {
    right: 0.5em;
    top: 2.375em;
    width: 2.9375em;
  }
}
@keyframes swal2-rotate-success-circular-line {
  0% {
    transform: rotate(-45deg);
  }
  5% {
    transform: rotate(-45deg);
  }
  12% {
    transform: rotate(-405deg);
  }
  to {
    transform: rotate(-405deg);
  }
}
@keyframes swal2-animate-error-x-mark {
  0% {
    margin-top: 1.625em;
    opacity: 0;
    transform: scale(0.4);
  }
  50% {
    margin-top: 1.625em;
    opacity: 0;
    transform: scale(0.4);
  }
  80% {
    margin-top: -0.375em;
    transform: scale(1.15);
  }
  to {
    margin-top: 0;
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes swal2-animate-error-icon {
  0% {
    opacity: 0;
    transform: rotateX(100deg);
  }
  to {
    opacity: 1;
    transform: rotateX(0deg);
  }
}
@keyframes swal2-rotate-loading {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
@keyframes swal2-animate-question-mark {
  0% {
    transform: rotateY(-1turn);
  }
  to {
    transform: rotateY(0);
  }
}
@keyframes swal2-animate-i-mark {
  0% {
    opacity: 0;
    transform: rotate(45deg);
  }
  25% {
    opacity: 0.4;
    transform: rotate(-25deg);
  }
  50% {
    opacity: 0.8;
    transform: rotate(15deg);
  }
  75% {
    opacity: 1;
    transform: rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: rotateX(0);
  }
}
body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
  overflow: hidden;
}
body.swal2-height-auto {
  height: auto !important;
}
body.swal2-no-backdrop .swal2-container {
  background-color: transparent !important;
  pointer-events: none;
}
body.swal2-no-backdrop .swal2-container .swal2-popup {
  pointer-events: all;
}
body.swal2-no-backdrop .swal2-container .swal2-modal {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
}
@media print {
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
    overflow-y: scroll !important;
  }
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)
    > [aria-hidden="true"] {
    display: none;
  }
  body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)
    .swal2-container {
    position: static !important;
  }
}
body.swal2-toast-shown .swal2-container {
  background-color: transparent;
  box-sizing: border-box;
  max-width: 100%;
  pointer-events: none;
  width: 26.25rem;
}
body.swal2-toast-shown .swal2-container.swal2-top {
  inset: 0 auto auto 50%;
  transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-top-end,
body.swal2-toast-shown .swal2-container.swal2-top-right {
  inset: 0 0 auto auto;
}
body.swal2-toast-shown .swal2-container.swal2-top-left,
body.swal2-toast-shown .swal2-container.swal2-top-start {
  inset: 0 auto auto 0;
}
body.swal2-toast-shown .swal2-container.swal2-center-left,
body.swal2-toast-shown .swal2-container.swal2-center-start {
  inset: 50% auto auto 0;
  transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-center {
  inset: 50% auto auto 50%;
  transform: translate(-50%, -50%);
}
body.swal2-toast-shown .swal2-container.swal2-center-end,
body.swal2-toast-shown .swal2-container.swal2-center-right {
  inset: 50% 0 auto auto;
  transform: translateY(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-left,
body.swal2-toast-shown .swal2-container.swal2-bottom-start {
  inset: auto auto 0 0;
}
body.swal2-toast-shown .swal2-container.swal2-bottom {
  inset: auto auto 0 50%;
  transform: translateX(-50%);
}
body.swal2-toast-shown .swal2-container.swal2-bottom-end,
body.swal2-toast-shown .swal2-container.swal2-bottom-right {
  inset: auto 0 0 auto;
}
:root {
  --bs-body-font-size: 0.875rem;
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 300;
  src: url(../fonts/nunito/Nunito-Light.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/nunito/Nunito-Regular.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: italic;
  font-weight: 400;
  src: url(../fonts/nunito/Nunito-Italic.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 500;
  src: url(../fonts/nunito/Nunito-Medium.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 600;
  src: url(../fonts/nunito/Nunito-SemiBold.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 700;
  src: url(../fonts/nunito/Nunito-Bold.woff2) format("woff2");
}
@font-face {
  font-family: Nunito;
  font-style: normal;
  font-weight: 800;
  src: url(../fonts/nunito/Nunito-ExtraBold.woff2) format("woff2");
}
.link-primary:focus-visible,
.link-primary:hover {
  color: #4e5fd1 !important;
}
.link-muted {
  color: #bdbdbd;
}
.link-muted:focus-visible,
.link-muted:hover {
  color: #415ec0;
}
.link-gray-1,
.text-gray-1 {
  color: #f0f0f0 !important;
}
.link-gray-1:focus-visible,
.link-gray-1:hover {
  color: #f3f3f3 !important;
}
.link-gray-2,
.text-gray-2 {
  color: #e6e6e6 !important;
}
.link-gray-2:focus-visible,
.link-gray-2:hover {
  color: #ebebeb !important;
}
.link-gray-3,
.text-gray-3 {
  color: #bdbdbd !important;
}
.link-gray-3:focus-visible,
.link-gray-3:hover {
  color: #cacaca !important;
}
.link-gray-4,
.text-gray-4 {
  color: #828282 !important;
}
.link-gray-4:focus-visible,
.link-gray-4:hover {
  color: #9b9b9b !important;
}
.link-gray-5,
.text-gray-5 {
  color: #4f4f4f !important;
}
.link-gray-5:focus-visible,
.link-gray-5:hover {
  color: #3f3f3f !important;
}
.link-gray-6,
.text-gray-6 {
  color: #333 !important;
}
.link-gray-6:focus-visible,
.link-gray-6:hover {
  color: #292929 !important;
}
.link-gui-duyet,
.text-gui-duyet {
  color: #0d99ff !important;
}
.link-gui-duyet:focus-visible,
.link-gui-duyet:hover {
  color: #3dadff !important;
}
.link-cho-duyet,
.text-cho-duyet {
  color: #f48620 !important;
}
.link-cho-duyet:focus-visible,
.link-cho-duyet:hover {
  color: #f69e4d !important;
}
.link-da-duyet,
.text-da-duyet {
  color: #0c8809 !important;
}
.link-da-duyet:focus-visible,
.link-da-duyet:hover {
  color: #0a6d07 !important;
}
.link-gui-dang,
.text-gui-dang {
  color: #458ab9 !important;
}
.link-gui-dang:focus-visible,
.link-gui-dang:hover {
  color: #6aa1c7 !important;
}
.link-cho-dang,
.text-cho-dang {
  color: #ff6d19 !important;
}
.link-cho-dang:focus-visible,
.link-cho-dang:hover {
  color: #ff8a47 !important;
}
.link-xuat-ban,
.text-xuat-ban {
  color: #096d34 !important;
}
.link-xuat-ban:focus-visible,
.link-xuat-ban:hover {
  color: #07572a !important;
}
.link-bai-nhap,
.text-bai-nhap {
  color: #8f969f !important;
}
.link-bai-nhap:focus-visible,
.link-bai-nhap:hover {
  color: #a5abb2 !important;
}
.link-da-huy,
.text-da-huy {
  color: #6d1b1c !important;
}
.link-da-huy:focus-visible,
.link-da-huy:hover {
  color: #571616 !important;
}
.link-tra-lai,
.text-tra-lai {
  color: #bf472d !important;
}
.link-tra-lai:focus-visible,
.link-tra-lai:hover {
  color: #993924 !important;
}
.link-giu-cho,
.text-giu-cho {
  color: #f2586e !important;
}
.link-giu-cho:focus-visible,
.link-giu-cho:hover {
  color: #f5798b !important;
}
.link-primary-active,
.text-primary-active {
  color: #213b94 !important;
}
.link-primary-active:focus-visible,
.link-primary-active:hover {
  color: #1a2f76 !important;
}
.link-icon {
  --bs-link-color: #a4b4cb;
}
.link-body-2,
.text-body-2 {
  color: #566171 !important;
}
.link-body-2:focus-visible,
.link-body-2:hover {
  color: #454e5a !important;
}
.text-red {
  color: #f2564c !important;
}
.text-red-active {
  color: #d7271c !important;
}
.text-green {
  color: #5bcd8f;
}
.link-green {
  color: #5bcd8f !important;
}
.link-green:focus-visible,
.link-green:hover {
  color: #7cd7a5 !important;
}
.text-brown {
  color: #ad9d85 !important;
}
.text-icon {
  color: #a4b4cb !important;
}
.bg-green {
  background-color: #5bcd8f !important;
}
.bg-primary-light {
  background-color: #e5eaf1 !important;
}
.bg-primary-light-2 {
  background-color: #edf5ff !important;
}
.text-bg-primary-light {
  background-color: RGBA(229, 234, 241, var(--bs-bg-opacity, 1)) !important;
  color: #000 !important;
}
.border-icon {
  --bs-border-opacity: 1;
  border-color: rgba(164, 180, 203, var(--bs-border-opacity)) !important;
}
.fw-1 {
  font-weight: 100 !important;
}
.fw-2 {
  font-weight: 200 !important;
}
.fw-3 {
  font-weight: 300 !important;
}
.fw-4 {
  font-weight: 400 !important;
}
.fw-5 {
  font-weight: 500 !important;
}
.fw-6 {
  font-weight: 600 !important;
}
.fw-7 {
  font-weight: 700 !important;
}
.fw-8 {
  font-weight: 800 !important;
}
.fw-9 {
  font-weight: 900 !important;
}
.flex-center {
  align-items: center;
}
.flex-center,
.flex-center-x {
  display: flex;
  justify-content: center;
}
.flex-center-y {
  align-items: center;
  display: flex;
}
.fs-base {
  font-size: 0.875rem !important;
}
.fs-sm {
  font-size: 0.75rem !important;
}
.fs-lg {
  font-size: 1.125rem !important;
}
.fs-13px {
  font-size: 0.8125rem !important;
}
.fs-15px {
  font-size: 0.9375rem !important;
}
.fs-16px {
  font-size: 1rem !important;
}
.fs-20px {
  font-size: 1.25rem !important;
}
.fs-32px {
  font-size: calc(1.325rem + 0.9vw) !important;
}
@media (min-width: 1200px) {
  .fs-32px {
    font-size: 2rem !important;
  }
}
.fs-65px {
  font-size: 4.0625rem !important;
}
.lh-heading {
  line-height: 1.2 !important;
}
.filter-light {
  filter: brightness(0) invert(1);
}
.filter-dark {
  filter: brightness(0) invert(0);
}
.rotate-180 {
  transform: rotate(180deg) !important;
}
.rotate-90 {
  transform: rotate(90deg) !important;
}
.mw-none {
  max-width: none !important;
}
.w-1 {
  width: 1% !important;
}
.min-h-auto {
  min-height: auto !important;
}
.min-w-auto {
  min-width: auto !important;
}
.min-w-150px {
  min-width: 9.375rem !important;
}
.min-w-200px {
  min-width: 12.5rem !important;
}
.w-24px {
  width: 1.5rem !important;
}
.w-40px {
  width: 2.5rem !important;
}
.w-84px {
  width: 5.9375rem !important;
}
@media (min-width: 768px) {
  .w-md-108px {
    width: 6.75rem !important;
  }
}
@media (min-width: 992px) {
  .w-lg-160px {
    width: 10rem !important;
  }
}
.my-12px {
  margin-bottom: 0.75rem;
  margin-top: 0.75rem;
}
.py-12px {
  padding-bottom: 0.75rem;
  padding-top: 0.75rem;
}
.g-10px,
.gy-10px {
  --bs-gutter-y: 0.625rem;
}
.g-10px,
.gx-10px {
  --bs-gutter-x: 0.625rem;
}
.gap-10px {
  gap: 0.625rem !important;
}
.g-20px,
.gy-20px {
  --bs-gutter-y: 1.25rem;
}
.g-20px,
.gx-20px {
  --bs-gutter-x: 1.25rem;
}
.gap-20px {
  gap: 1.25rem !important;
}
.g-30px,
.gy-30px {
  --bs-gutter-y: 1.875rem;
}
.g-30px,
.gx-30px {
  --bs-gutter-x: 1.875rem;
}
.gap-30px {
  gap: 1.875rem !important;
}
.border-blue-1 {
  border-color: #377dff !important;
}
.border-blue-2 {
  border-color: #edf2fa !important;
}
.border-blue-3 {
  border-color: #dbe8fc !important;
}
.border-blue-4 {
  border-color: #8794aa !important;
}
.border-blue-5 {
  border-color: #34678b !important;
}
.border-orange {
  border-color: #f48620 !important;
}
.border-dashed {
  border-style: dashed !important;
}
@media (min-width: 1440px) {
  .text-xxl-nowrap {
    white-space: nowrap !important;
  }
}
.text-transform-none {
  text-transform: none !important;
}
.text-break-all {
  word-break: break-all !important;
}
.scroll-y {
  max-height: 18.75rem;
  overflow-x: hidden;
  overflow-y: auto;
}
.scroll-x,
.scroll-y {
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
}
.scroll-x {
  overflow-x: auto;
  overflow-y: hidden;
}
.cursor-pointer {
  cursor: pointer !important;
}
.mt-n3px {
  margin-top: -0.1875rem !important;
}
.translate-y-n1px {
  transform: translateY(-1px) !important;
}
.translate-y-n2px {
  transform: translateY(-2px) !important;
}
.translate-y-n3px {
  transform: translateY(-3px) !important;
}
.rounded-12px {
  border-radius: 0.75rem !important;
}
[class*="iconsvg"] {
  fill: currentColor;
  display: inline-block;
  height: 1em;
  width: 1em;
}
[class$="-o"],
[class*="-o "] {
  fill: transparent;
  stroke: currentColor;
}
.iconsvg-Vector {
  font-size: 0.4375rem;
}
.iconsvg-alert-circle-lg {
  font-size: 4rem;
}
.iconsvg-arrow-decreasing {
  font-size: 1.5rem;
}
.iconsvg-arrow-right {
  font-size: 0.8125rem;
}
.iconsvg-arrow-right-circle,
.iconsvg-arrow-rise,
.iconsvg-attachment {
  font-size: 1.5rem;
}
.iconsvg-calendar {
  font-size: 1rem;
}
.iconsvg-checkmark-circle,
.iconsvg-chevron-right {
  font-size: 1.5rem;
}
.iconsvg-close {
  font-size: 1rem;
}
.iconsvg-close-circle {
  font-size: 1.25rem;
}
.iconsvg-confirm {
  font-size: 1rem;
}
.iconsvg-contact-support,
.iconsvg-download {
  font-size: 1.5rem;
}
.iconsvg-ds-booking {
  font-size: 0.75rem;
}
.iconsvg-edit {
  font-size: 1.25rem;
}
.iconsvg-edit-2 {
  font-size: 1.125rem;
}
.iconsvg-error {
  font-size: 1.375rem;
}
.iconsvg-export {
  font-size: 1rem;
}
.iconsvg-eye-fill {
  font-size: 1.25rem;
}
.iconsvg-eye-off {
  font-size: 1rem;
}
.iconsvg-file {
  font-size: 0.75rem;
}
.iconsvg-grid {
  font-size: 1rem;
}
.iconsvg-hourglass,
.iconsvg-link,
.iconsvg-login,
.iconsvg-map-pin {
  font-size: 1.5rem;
}
.iconsvg-monitor {
  font-size: 1rem;
}
.iconsvg-monitor-2 {
  font-size: 1.25rem;
}
.iconsvg-notification,
.iconsvg-pencil {
  font-size: 1.5rem;
}
.iconsvg-phone {
  font-size: 0.75rem;
}
.iconsvg-plus-circle,
.iconsvg-plus-square {
  font-size: 1.25rem;
}
.iconsvg-save {
  font-size: 1.0625rem;
}
.iconsvg-search {
  font-size: 1rem;
}
.iconsvg-send {
  font-size: 1.125rem;
}
.iconsvg-send-2,
.iconsvg-sort,
.iconsvg-sort-arrow-down {
  font-size: 1rem;
}
.iconsvg-success {
  font-size: 1.375rem;
}
.iconsvg-time-fill {
  font-size: 1.25rem;
}
.iconsvg-trash {
  font-size: 1rem;
}
.iconsvg-trash-lg {
  font-size: 4.0625rem;
}
.iconsvg-twitch-plus {
  font-size: 1.25rem;
}
.iconsvg-upload {
  font-size: 1.5rem;
}
.iconsvg-warning {
  font-size: 1.375rem;
}
.iconsvg-x-mark-circle {
  font-size: 1.5rem;
}
[class*="iconsvg2"] {
  display: inline-block;
  height: 1em;
  width: 1em;
}
.iconsvg2-admatic {
  font-size: 1.25rem;
}
.iconsvg2-facebook-rounded,
.iconsvg2-flag,
.iconsvg2-setting {
  font-size: 1.5rem;
}
[class*="icont"] {
  display: inline-block;
  height: 1em;
  line-height: 1;
  outline: none;
  position: relative;
  width: 1em;
}
[class*="icont"]:before {
  content: "";
}
.icont-close:after,
.icont-close:before {
  border-left: 2px solid;
  content: "";
  height: 1em;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: rotate(45deg) translate(-50%, -50%);
  transform-origin: 0 0;
  width: 0;
}
.icont-close:after {
  transform: rotate(-45deg) translate(-50%, -50%);
}
.icont-plus:after,
.icont-plus:before {
  border-left: 2px solid;
  content: "";
  height: 85%;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: 0 0;
  width: 0;
}
.icont-plus:after {
  transform: rotate(-90deg) translate(-50%, -50%);
}
.icont-plus-square {
  background-color: #cecece;
  border-radius: 0.125rem;
  height: 1.25em;
  width: 1.25em;
}
.icont-plus-square:after,
.icont-plus-square:before {
  border-left: 2px solid;
  content: "";
  height: 50%;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: 0 0;
  width: 0;
}
.icont-plus-square:after {
  transform: rotate(-90deg) translate(-50%, -50%);
}
.icont-check:before {
  border-bottom: 2px solid;
  border-left: 2px solid;
  height: 45%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transform: rotate(-45deg) translate(34%, -50%);
  width: 90%;
}
[class*="icont-chevron"]:before {
  border-right: 2px solid;
  border-top: 2px solid;
  bottom: 20%;
  left: 20%;
  position: absolute;
  right: 20%;
  top: 20%;
  transform: rotate(45deg) translate(-15%, 15%);
}
.icont-chevron-left:before {
  transform: rotate(-135deg) translate(-15%, 15%);
}
.icont-chevron-up:before {
  transform: rotate(-45deg) translate(-15%, 15%);
}
.icont-chevron-down:before {
  transform: rotate(135deg) translate(-15%, 15%);
}
.icont-search {
  transform: rotate(-45deg);
}
.icont-search:before {
  border: 2px solid;
  border-radius: 50%;
  height: 80%;
  top: 0;
  width: 80%;
}
.icont-search:after,
.icont-search:before {
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}
.icont-search:after {
  border-left: 2px solid;
  border-radius: 2px;
  content: "";
  height: 35%;
  top: 80%;
  width: 0;
}
[class*="icont-caret"] {
  align-items: center;
  display: flex;
  justify-content: center;
}
[class*="icont-caret"]:before {
  border-color: transparent transparent transparent currentcolor;
  border-style: solid;
  border-width: 0.35em 0 0.35em 0.5em;
  display: block;
  height: 0;
  width: 0;
}
.icont-square {
  background-color: currentColor;
  height: 0.7692307692em;
  width: 0.7692307692em;
}
.btn {
  align-items: center;
  display: inline-flex;
  justify-content: center;
  min-height: 2.5rem;
}
.btn-block {
  display: flex;
}
.btn-sm {
  min-height: 2rem;
}
.btn-lg {
  min-height: 3.25rem;
}
.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #4a69d2;
  --bs-btn-border-color: #4a69d2;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #213b94;
  --bs-btn-hover-border-color: #213b94;
  --bs-btn-focus-shadow-rgb: 101, 128, 217;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #1e3585;
  --bs-btn-active-border-color: #1e3585;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #4a69d2;
  --bs-btn-disabled-border-color: #4a69d2;
}
.btn-outline-primary {
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-active-color: var(--bs-white);
}
.btn-primary-light {
  --bs-btn-color: #828282;
  --bs-btn-bg: #eff2f8;
  --bs-btn-border-color: #eff2f8;
  --bs-btn-hover-color: #828282;
  --bs-btn-hover-bg: #e3e6ec;
  --bs-btn-hover-border-color: #e3e6ec;
  --bs-btn-focus-shadow-rgb: 223, 225, 230;
  --bs-btn-active-color: #828282;
  --bs-btn-active-bg: #d7dadf;
  --bs-btn-active-border-color: #d7dadf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #eff2f8;
  --bs-btn-disabled-border-color: #eff2f8;
}
.btn-danger {
  --bs-btn-color: #fff;
  --bs-btn-bg: #f2564c;
  --bs-btn-border-color: #f2564c;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #d7271c;
  --bs-btn-hover-border-color: #d7271c;
  --bs-btn-focus-shadow-rgb: 244, 111, 103;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #c22319;
  --bs-btn-active-border-color: #c22319;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #f2564c;
  --bs-btn-disabled-border-color: #f2564c;
}
.btn-orange {
  --bs-btn-color: #fff;
  --bs-btn-bg: #ff6d19;
  --bs-btn-border-color: #ff6d19;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #e15200;
  --bs-btn-hover-border-color: #e15200;
  --bs-btn-focus-shadow-rgb: 255, 131, 60;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #cb4a00;
  --bs-btn-active-border-color: #cb4a00;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ff6d19;
  --bs-btn-disabled-border-color: #ff6d19;
}
.btn-cancel {
  --bs-btn-color: #828282;
  --bs-btn-bg: #eff2f8;
  --bs-btn-border-color: #eff2f8;
  --bs-btn-hover-color: #828282;
  --bs-btn-hover-bg: #d3dae7;
  --bs-btn-hover-border-color: #d3dae7;
  --bs-btn-focus-shadow-rgb: 223, 225, 230;
  --bs-btn-active-color: #828282;
  --bs-btn-active-bg: #bec4d0;
  --bs-btn-active-border-color: #bec4d0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #eff2f8;
  --bs-btn-disabled-border-color: #eff2f8;
}
.btn-draft {
  --bs-btn-color: #fff;
  --bs-btn-bg: #8f969f;
  --bs-btn-border-color: #8f969f;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #586271;
  --bs-btn-hover-border-color: #586271;
  --bs-btn-focus-shadow-rgb: 160, 166, 173;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4f5866;
  --bs-btn-active-border-color: #4f5866;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #8f969f;
  --bs-btn-disabled-border-color: #8f969f;
}
.btn-outline-danger {
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-active-color: var(--bs-white);
}
.btn-file {
  --bs-btn-color: #566171;
  --bs-btn-bg: #fff;
  --bs-btn-border-color: #8f969f;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #8f969f;
  --bs-btn-hover-border-color: #8f969f;
  --bs-btn-focus-shadow-rgb: 134, 142, 152;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #81878f;
  --bs-btn-active-border-color: #81878f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fff;
  --bs-btn-disabled-border-color: #8f969f;
}
.btn-action {
  border-radius: 50%;
  font-size: 0.875rem;
  height: 1.7142857143em;
  min-height: auto;
  padding: 0.25rem;
  width: 1.7142857143em;
}
.btn-action > svg {
  font-size: 1em;
}
.btn-action-cancel {
  --bs-btn-color: #6d1b1c;
  --bs-btn-border-color: #6d1b1c;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #6d1b1c;
  --bs-btn-hover-border-color: #6d1b1c;
  --bs-btn-focus-shadow-rgb: 109, 27, 28;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #6d1b1c;
  --bs-btn-active-border-color: #6d1b1c;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #6d1b1c;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #6d1b1c;
  --bs-gradient: none;
}
.btn-action-edit {
  --bs-btn-color: #f48620;
  --bs-btn-border-color: #f48620;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #f48620;
  --bs-btn-hover-border-color: #f48620;
  --bs-btn-focus-shadow-rgb: 244, 134, 32;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #f48620;
  --bs-btn-active-border-color: #f48620;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #f48620;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #f48620;
  --bs-gradient: none;
}
.btn-action-send {
  --bs-btn-color: #0d99ff;
  --bs-btn-border-color: #0d99ff;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0d99ff;
  --bs-btn-hover-border-color: #0d99ff;
  --bs-btn-focus-shadow-rgb: 13, 153, 255;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0d99ff;
  --bs-btn-active-border-color: #0d99ff;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0d99ff;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0d99ff;
  --bs-gradient: none;
}
.btn-action-download {
  --bs-btn-color: #4db2ad;
  --bs-btn-border-color: #4db2ad;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #4db2ad;
  --bs-btn-hover-border-color: #4db2ad;
  --bs-btn-focus-shadow-rgb: 77, 178, 173;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #4db2ad;
  --bs-btn-active-border-color: #4db2ad;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #4db2ad;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #4db2ad;
  --bs-gradient: none;
}
.btn-action-request {
  --bs-btn-border-width: 0;
  --bs-btn-bg: transparent;
  --bs-btn-hover-bg: transparent;
  --bs-btn-active-bg: transparent;
  --bs-btn-disabled-bg: transparent;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: auto 100%;
  border-radius: 0;
}
.btn-action-request.btn-action-cancel {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%23fff' stroke='%236D1B1C'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-cancel:focus-visible,
.btn-action-request.btn-action-cancel:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%236D1B1C' stroke='%236D1B1C'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-edit {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%23fff' stroke='%23F48620'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-edit:focus-visible,
.btn-action-request.btn-action-edit:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%23F48620' stroke='%23F48620'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-send {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%23fff' stroke='%230D99FF'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-send:focus-visible,
.btn-action-request.btn-action-send:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%230D99FF' stroke='%230D99FF'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-download {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%23fff' stroke='%234DB2AD'/%3E%3C/svg%3E");
}
.btn-action-request.btn-action-download:focus-visible,
.btn-action-request.btn-action-download:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='32' height='33' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M.545 16.5c0-4.25 1.634-8.32 4.539-11.32a15.454 15.454 0 0 1 5.02-3.465A15.04 15.04 0 0 1 16.023.5c4.1 0 8.035 1.681 10.939 4.68 2.905 3 4.539 7.07 4.539 11.32 0 4.247-1.634 8.319-4.539 11.318-2.904 2.999-6.839 4.68-10.939 4.68H1.621a1.059 1.059 0 0 1-.611-.163 1.133 1.133 0 0 1-.425-.49 1.2 1.2 0 0 1-.065-.664c.042-.225.148-.43.3-.591l.002-.002 3.193-3.297.306-.316-.274-.344C1.77 23.782.53 20.198.545 16.501Z' fill='%234DB2AD' stroke='%234DB2AD'/%3E%3C/svg%3E");
}
.btn-select {
  --bs-btn-color: #566171;
  --bs-btn-bg: #fff;
  --bs-btn-border-color: #dee4f0;
  --bs-btn-hover-color: #566171;
  --bs-btn-hover-bg: #fff;
  --bs-btn-hover-border-color: #dee4f0;
  --bs-btn-focus-shadow-rgb: 202, 208, 221;
  --bs-btn-active-color: #566171;
  --bs-btn-active-bg: #fff;
  --bs-btn-active-border-color: #dee4f0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fff;
  --bs-btn-disabled-border-color: #dee4f0;
}
.btn-select.show {
  --bs-border-color: #a5b4e9;
}
.btn-green {
  --bs-btn-color: #000;
  --bs-btn-bg: #5bcd8f;
  --bs-btn-border-color: #5bcd8f;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #74d5a0;
  --bs-btn-hover-border-color: #6bd29a;
  --bs-btn-focus-shadow-rgb: 77, 174, 122;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #7cd7a5;
  --bs-btn-active-border-color: #6bd29a;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #5bcd8f;
  --bs-btn-disabled-border-color: #5bcd8f;
}
.btn-green-2 {
  --bs-btn-color: #000;
  --bs-btn-bg: #4db2ad;
  --bs-btn-border-color: #4db2ad;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #68beb9;
  --bs-btn-hover-border-color: #5fbab5;
  --bs-btn-focus-shadow-rgb: 65, 151, 147;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #71c1bd;
  --bs-btn-active-border-color: #5fbab5;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #4db2ad;
  --bs-btn-disabled-border-color: #4db2ad;
}
.btn-square {
  align-items: center;
  display: inline-flex;
  height: calc(1.375em + 1.125rem + 2px);
  justify-content: center;
  padding: 0.25rem;
  width: calc(1.375em + 1.125rem + 2px);
}
.btn-square.btn-sm {
  height: calc(1.375em + 0.5rem + 2px);
  width: calc(1.375em + 0.5rem + 2px);
}
.btn-square.btn-lg {
  height: calc(1.375em + 1rem + 2px);
  width: calc(1.375em + 1rem + 2px);
}
.form-control {
  background-repeat: no-repeat;
  background-size: 1rem;
  min-height: 2.5rem;
}
.form-control-sm {
  min-height: 2rem;
}
.form-control-lg {
  min-height: 3.25rem;
}
.form-control-primary {
  border-width: 0;
}
.form-control-primary,
.form-control-primary:focus {
  background-color: #edf5ff;
}
.form-control[contenteditable="true"] {
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
  max-height: 6.25rem;
  overflow-x: hidden;
  overflow-y: auto;
}
.form-control[contenteditable="true"]:empty:before {
  color: #566171;
  content: attr(placeholder);
  cursor: text;
}
.form-control-clear {
  border: none;
  min-height: auto;
  padding: 0;
}
.form-control.is-warning {
  border-color: #ffb36d;
}
.form-control-has-addon {
  position: relative;
}
.form-control-has-addon .form-control {
  padding-right: 2.5rem;
}
.form-control-has-addon
  .form-control:not(:-moz-placeholder-shown):not([contenteditable="true"]) {
  background-image: none;
}
.form-control-has-addon
  .form-control:not(:placeholder-shown):not([contenteditable="true"]) {
  background-image: none;
}
.form-control-has-addon
  .form-control:not(:-moz-placeholder-shown):not([contenteditable="true"])
  ~ .form-control-addon {
  visibility: visible;
}
.form-control-has-addon
  .form-control:not(:placeholder-shown):not([contenteditable="true"])
  ~ .form-control-addon {
  visibility: visible;
}
.form-control-has-addon
  .form-control[contenteditable="true"]:not(:empty)
  ~ .form-control-addon {
  visibility: visible;
}
.form-control-addon {
  color: #a4b4cb;
  height: 1.5rem;
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  visibility: hidden;
  width: 1.5rem;
}
.form-control-addon:focus-visible,
.form-control-addon:hover {
  color: #828282;
}
.form-control-icon-start {
  background-position: center left 0.5rem;
}
.form-control-icon-start,
.form-floating > .form-control-icon-start,
.form-floating > .form-control-icon-start ~ label {
  padding-left: 2.5rem;
}
.form-control-icon-end {
  background-position: center right 0.5rem;
}
.form-control-icon-end,
.form-floating > .form-control-icon-end,
.form-floating > .form-control-icon-end ~ label {
  padding-right: 2.5rem;
}
.form-control-user {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='28' height='28' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M22.857 24v-2.286a4.571 4.571 0 0 0-4.571-4.571H9.143a4.572 4.572 0 0 0-4.571 4.571V24M13.714 12.571a4.571 4.571 0 1 0 0-9.143 4.571 4.571 0 0 0 0 9.143Z' stroke='%23828282' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 1.25rem;
}
.form-control-lock {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='28' height='29' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.714 13.286h-16a2.286 2.286 0 0 0-2.286 2.285v8a2.286 2.286 0 0 0 2.286 2.286h16A2.286 2.286 0 0 0 24 23.571v-8a2.286 2.286 0 0 0-2.286-2.285ZM8 13.286V8.713a5.714 5.714 0 0 1 11.429 0v4.572' stroke='%23828282' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-size: 1.25rem;
}
.form-control-edit {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='25' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m6.04 19.283.023-.006 2.632-.658.045-.01c.223-.056.42-.105.599-.207.179-.101.322-.245.485-.407l.032-.033 7.194-7.194.024-.024c.313-.313.583-.583.77-.828.201-.263.353-.556.353-.916s-.152-.653-.353-.916c-.187-.245-.457-.515-.77-.829l-.024-.023-.353.354.353-.354-.171-.171-.023-.024c-.314-.313-.584-.583-.83-.77-.262-.2-.554-.353-.915-.353-.36 0-.653.152-.916.353-.245.187-.515.457-.828.77l-.024.024-7.194 7.194a7.24 7.24 0 0 1-.033.032c-.162.163-.306.306-.407.485-.102.18-.15.376-.206.6l-.011.044-.664 2.654a8.646 8.646 0 0 0-.007.027c-.039.157-.08.324-.094.464-.016.155-.012.416.197.626.************.626.197a3.43 3.43 0 0 0 .49-.101Z' stroke='%23BDBDBD'/%3E%3Cpath d='m12.611 7.5 3-2 3 3-2 3-4-4Z' fill='%23BDBDBD'/%3E%3C/svg%3E");
}
.form-control-search {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='17' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='m15.989 14.573-3.808-3.96a6.44 6.44 0 0 0 1.515-4.153A6.467 6.467 0 0 0 7.236 0a6.467 6.467 0 0 0-6.46 6.46 6.467 6.467 0 0 0 6.46 6.46 6.39 6.39 0 0 0 3.701-1.169l3.837 3.99c.16.167.376.259.608.259a.844.844 0 0 0 .607-1.427ZM7.236 1.685a4.78 4.78 0 0 1 4.775 4.775 4.78 4.78 0 0 1-4.775 4.775A4.78 4.78 0 0 1 2.462 6.46a4.78 4.78 0 0 1 4.774-4.775Z' fill='%23566171'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='a'%3E%3Cpath fill='%23fff' transform='translate(.5)' d='M0 0h16v16H0z'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
}
.form-check-icon-lg {
  padding-left: 2rem;
}
.form-check-icon-lg .form-check-input {
  height: 1.5rem;
  margin-left: -2rem;
  margin-top: -0.1875rem;
  width: 1.5rem;
}
.form-check-icon-20px {
  padding-left: 1.75rem;
}
.form-check-icon-20px .form-check-input {
  margin-left: -1.75rem;
}
.form-check-icon-20px .form-check-input,
.form-check-input-20px {
  height: 1.25rem;
  margin-top: -0.0625rem;
  width: 1.25rem;
}
.form-check-primary .form-check-input {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z' stroke='%234E77FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100%;
  border: none;
}
.form-check-primary .form-check-input:checked {
  background-color: transparent;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m9 11 3 3L22 4' stroke='%234E77FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11' stroke='%234E77FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.upload {
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  color: var(--bs-primary);
  font-size: 0.8125rem;
  padding: 0.625rem 1rem;
}
.upload-btn {
  --bs-btn-padding-x: 2.5rem;
  --bs-btn-padding-y: 0.375rem;
  min-height: auto;
}
.upload-btn input {
  display: none;
}
.upload-list {
  display: grid;
  gap: 0.25rem;
  list-style: none;
  padding-left: 0;
}
@media (min-width: 768px) {
  .upload-list-md-reverse .upload-list-item {
    justify-content: flex-end;
  }
}
.upload-list-item {
  align-items: flex-start;
  display: flex;
  min-width: 0;
  word-break: break-all;
}
.upload-list-item svg {
  font-size: 1rem;
  margin-top: -1px;
}
.upload-list-icon {
  color: #a4b4cb;
  flex-shrink: 0;
}
.upload-list-close {
  --bs-btn-color: #a4b4cb;
  --bs-btn-hover-color: #566171;
}
.upload-list-item-body {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.form-style-1 .form-control {
  border: none;
  padding-left: 0;
  padding-right: 0;
}
@media (max-width: 767.98px) {
  .form-style-1 .form-control {
    min-height: auto;
    padding: 0;
  }
}
.form-style-1 .flatpickr .flatpickr-input {
  background-position: 100%;
}
.form-rounded-12px .form-control,
.form-rounded-12px .form-select {
  border-radius: 0.75rem;
}
.form-rounded-12px .select2 {
  --select2-border-radius: 0.75rem;
}
:root {
  --form-horizontal-label-width: 5rem;
  --form-horizontal-body-width: 28rem;
}
.col-form-label {
  padding-bottom: 0;
  padding-top: 0;
  min-width: 4rem;
}
@media (min-width: 768px) {
  .col-form-label {
    width: var(--form-horizontal-label-width);
  }
}
.col-form-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}
@media (min-width: 992px) {
  /* .col-form-body {
    max-width: var(--form-horizontal-body-width);
  } */
}
@media (min-width: 768px) {
  .col-form-text {
    margin-left: var(--form-horizontal-label-width);
    /* max-width: var(--form-horizontal-body-width); */
  }
}
.form-horizontal {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 0;
}
.form-mde {
  --form-mde-padding-x: 0;
  --form-mde-padding-y: 0.375rem;
  --form-mde-placeholder-color: #4f4f4f;
}
.form-mde .form-control-wrapper {
  position: relative;
}
.form-mde .form-control {
  border-radius: 0;
  border-width: 0 0 1px;
  font-weight: 500;
  min-height: auto;
  padding: var(--form-mde-padding-y) var(--form-mde-padding-x);
}
.form-mde label {
  font-weight: 500;
}
.form-mde .form-control-icon-start {
  background-position: 0;
  padding-left: 2rem;
}
.form-mde .form-control-has-addon .form-control {
  padding-right: 2rem;
}
.form-mde .form-control-addon {
  right: 0;
}
.form-mde .form-floating > .form-control {
  height: 2rem;
}
.form-mde .form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-bottom: var(--form-mde-padding-y);
  padding-top: var(--form-mde-padding-y);
}
.form-mde .form-floating > .form-control:not(:placeholder-shown) {
  padding-bottom: var(--form-mde-padding-y);
  padding-top: var(--form-mde-padding-y);
}
.form-mde .form-floating > .form-control.form-control-icon-start ~ label {
  padding-left: 2rem;
}
.form-mde .form-floating > label {
  border-width: 0 0 1px;
}
.form-mde .form-floating > label,
.form-mde .form-label-floating {
  color: var(--form-mde-placeholder-color);
  padding: var(--form-mde-padding-y) var(--form-mde-padding-x);
}
.form-mde .flatpickr-input {
  background-position: 100%;
  padding-right: 2rem;
}
.form-mde .select2 {
  --select2-icon-position-right: 0;
}
.form-mde .select2 .select2-selection {
  background-position: center right 0.5rem;
  border-radius: 0 !important;
  border-width: 0 0 1px;
  height: auto;
}
.form-mde .select2 .select2-selection--single .select2-selection__rendered {
  height: 2rem;
  padding-left: 0;
}
.form-mde .select2 .select2-selection .select2-selection__arrow {
  height: 2rem;
  width: 2rem;
}
.form-mde .select2 .select2-selection .select2-selection__clear {
  right: 0;
  width: 0.75rem;
}
.table {
  --bs-table-cell-padding-x: 0.5rem;
  --bs-table-cell-padding-y: 0.5rem;
}
.table > :not(caption) > * > * {
  padding: var(--bs-table-cell-padding-y) var(--bs-table-cell-padding-x);
}
.table-primary {
  --bs-table-color: var(--bs-white);
}
.table-primary-light {
  --bs-table-color: #fff;
  --bs-table-bg: #a3b6ea;
  --bs-table-border-color: #93a4d3;
}
.table-primary-light,
.table-title {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-title {
  --bs-table-color: var(--bs-white);
  --bs-table-bg: #24346a;
  --bs-table-border-color: #24346a;
}
.table-sort {
  align-items: center;
  display: flex;
}
.table-sort-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='18' height='11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.746.05a.666.666 0 0 0-.22.14L.196 3.525a.67.67 0 0 0 .946.947L3.333 2.27v7.062a.667.667 0 1 0 1.333 0V2.271l2.191 2.2a.666.666 0 0 0 1.092-.216.668.668 0 0 0-.146-.73L4.473.19a.666.666 0 0 0-.22-.14.665.665 0 0 0-.507 0ZM13.746 10.95a.666.666 0 0 1-.22-.14l-3.33-3.335a.67.67 0 0 1 .946-.947l2.191 2.201V1.667a.667.667 0 0 1 1.137-.472.667.667 0 0 1 .195.472v7.062l2.192-2.2a.666.666 0 0 1 1.092.216.668.668 0 0 1-.146.73l-3.33 3.335a.668.668 0 0 1-.22.14.666.666 0 0 1-.507 0Z' fill='%23E0E0E0'/%3E%3C/svg%3E");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100%;
  cursor: pointer;
  display: inline-flex;
  flex-shrink: 0;
  height: 0.6875rem;
  margin-right: 0.5rem;
  width: 1.125rem;
}
.table-sort-icon .btn {
  border: none;
  min-height: auto;
  padding: 0;
  width: 50%;
}
.table-sort.ascending .table-sort-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='18' height='11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.746.05a.666.666 0 0 0-.22.14L.196 3.525a.67.67 0 0 0 .946.947L3.333 2.27v7.062a.667.667 0 1 0 1.333 0V2.271l2.191 2.2a.666.666 0 0 0 1.092-.216.668.668 0 0 0-.146-.73L4.473.19a.666.666 0 0 0-.22-.14.665.665 0 0 0-.507 0Z' fill='%23566171'/%3E%3Cpath d='M13.746 10.95a.666.666 0 0 1-.22-.14l-3.33-3.335a.67.67 0 0 1 .946-.947l2.191 2.201V1.667a.667.667 0 0 1 1.137-.472.667.667 0 0 1 .195.472v7.062l2.192-2.2a.666.666 0 0 1 1.092.216.668.668 0 0 1-.146.73l-3.33 3.335a.668.668 0 0 1-.22.14.666.666 0 0 1-.507 0Z' fill='%23E0E0E0'/%3E%3C/svg%3E");
}
.table-sort.descending .table-sort-icon {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='18' height='11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.746.05a.666.666 0 0 0-.22.14L.196 3.525a.67.67 0 0 0 .946.947L3.333 2.27v7.062a.667.667 0 1 0 1.333 0V2.271l2.191 2.2a.666.666 0 0 0 1.092-.216.668.668 0 0 0-.146-.73L4.473.19a.666.666 0 0 0-.22-.14.665.665 0 0 0-.507 0Z' fill='%23E0E0E0'/%3E%3Cpath d='M13.746 10.95a.666.666 0 0 1-.22-.14l-3.33-3.335a.67.67 0 0 1 .946-.947l2.191 2.201V1.667a.667.667 0 0 1 1.137-.472.667.667 0 0 1 .195.472v7.062l2.192-2.2a.666.666 0 0 1 1.092.216.668.668 0 0 1-.146.73l-3.33 3.335a.668.668 0 0 1-.22.14.666.666 0 0 1-.507 0Z' fill='%23566171'/%3E%3C/svg%3E");
}
.table-gx-0 > :not(caption) > * > * {
  padding-left: 0;
  padding-right: 0;
}
.table-gx-1 > :not(caption) > * > * {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.table-gx-2 > :not(caption) > * > * {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.table-gx-3 > :not(caption) > * > * {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.table-gx-4 > :not(caption) > * > * {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.table-gx-5 > :not(caption) > * > * {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
@media (min-width: 576px) {
  .table-gx-sm-0 > :not(caption) > * > * {
    padding-left: 0;
    padding-right: 0;
  }
  .table-gx-sm-1 > :not(caption) > * > * {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .table-gx-sm-2 > :not(caption) > * > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .table-gx-sm-3 > :not(caption) > * > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .table-gx-sm-4 > :not(caption) > * > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .table-gx-sm-5 > :not(caption) > * > * {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 768px) {
  .table-gx-md-0 > :not(caption) > * > * {
    padding-left: 0;
    padding-right: 0;
  }
  .table-gx-md-1 > :not(caption) > * > * {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .table-gx-md-2 > :not(caption) > * > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .table-gx-md-3 > :not(caption) > * > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .table-gx-md-4 > :not(caption) > * > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .table-gx-md-5 > :not(caption) > * > * {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 992px) {
  .table-gx-lg-0 > :not(caption) > * > * {
    padding-left: 0;
    padding-right: 0;
  }
  .table-gx-lg-1 > :not(caption) > * > * {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .table-gx-lg-2 > :not(caption) > * > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .table-gx-lg-3 > :not(caption) > * > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .table-gx-lg-4 > :not(caption) > * > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .table-gx-lg-5 > :not(caption) > * > * {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 1200px) {
  .table-gx-xl-0 > :not(caption) > * > * {
    padding-left: 0;
    padding-right: 0;
  }
  .table-gx-xl-1 > :not(caption) > * > * {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .table-gx-xl-2 > :not(caption) > * > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .table-gx-xl-3 > :not(caption) > * > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .table-gx-xl-4 > :not(caption) > * > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .table-gx-xl-5 > :not(caption) > * > * {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 1440px) {
  .table-gx-xxl-0 > :not(caption) > * > * {
    padding-left: 0;
    padding-right: 0;
  }
  .table-gx-xxl-1 > :not(caption) > * > * {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }
  .table-gx-xxl-2 > :not(caption) > * > * {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }
  .table-gx-xxl-3 > :not(caption) > * > * {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .table-gx-xxl-4 > :not(caption) > * > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  .table-gx-xxl-5 > :not(caption) > * > * {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
[class*="table-pe"] {
  --table-cell-pe-x: var(--bs-table-cell-padding-x);
  --table-cell-pe-y: var(--bs-table-cell-padding-y);
}
[class*="table-pe"] td:first-child,
[class*="table-pe"] th:first-child {
  padding-left: var(--table-cell-pe-x);
}
[class*="table-pe"] td:last-child,
[class*="table-pe"] th:last-child {
  padding-right: var(--table-cell-pe-x);
}
[class*="table-pe"] tbody tr:first-child td,
[class*="table-pe"] tbody tr:first-child th,
[class*="table-pe"] thead tr:first-child td,
[class*="table-pe"] thead tr:first-child th,
[class*="table-pe"] thead + tbody tr:first-child td,
[class*="table-pe"] thead + tbody tr:first-child th {
  padding-top: var(--table-cell-pe-y);
}
[class*="table-pe"] tbody tr:last-child td,
[class*="table-pe"] tbody tr:last-child th {
  padding-bottom: var(--table-cell-pe-y);
}
.table-pe-0,
.table-pe-x-0 {
  --table-cell-pe-x: 0;
}
.table-pe-0,
.table-pe-y-0 {
  --table-cell-pe-y: 0;
}
.table-pe-1,
.table-pe-x-1 {
  --table-cell-pe-x: 0.25rem;
}
.table-pe-1,
.table-pe-y-1 {
  --table-cell-pe-y: 0.25rem;
}
.table-pe-2,
.table-pe-x-2 {
  --table-cell-pe-x: 0.5rem;
}
.table-pe-2,
.table-pe-y-2 {
  --table-cell-pe-y: 0.5rem;
}
.table-pe-3,
.table-pe-x-3 {
  --table-cell-pe-x: 1rem;
}
.table-pe-3,
.table-pe-y-3 {
  --table-cell-pe-y: 1rem;
}
.table-pe-4,
.table-pe-x-4 {
  --table-cell-pe-x: 1.5rem;
}
.table-pe-4,
.table-pe-y-4 {
  --table-cell-pe-y: 1.5rem;
}
.table-pe-5,
.table-pe-x-5 {
  --table-cell-pe-x: 3rem;
}
.table-pe-5,
.table-pe-y-5 {
  --table-cell-pe-y: 3rem;
}
@media (max-width: 991.98px) {
  [class*="table-responsive"]::-webkit-scrollbar {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl .table {
    min-width: 1420px;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg .table {
    min-width: 57.25rem;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md .table {
    min-width: 37.5rem;
  }
}
@media (max-width: 575.98px) {
  .table-responsive-sm .table {
    min-width: 31.25rem;
  }
  :root {
    --table-flush: 1.5rem;
  }
}
.table-responsive-flush {
  display: flex;
  margin-left: calc(var(--table-flush) * -1);
  margin-right: calc(var(--table-flush) * -1);
  width: auto;
}
.table-responsive-flush > .table-responsive {
  display: flex;
}
.table-responsive-flush .table-responsive-inner {
  flex-grow: 1;
  padding-left: var(--table-flush);
  padding-right: var(--table-flush);
}
@media (max-width: 991.98px) {
  .modal .table-responsive-flush.table-responsive-lg {
    --table-flush: var(--modal-padding-x);
  }
}
@media (max-width: 767.98px) {
  .modal .table-responsive-flush.table-responsive-md {
    --table-flush: var(--modal-padding-x);
  }
}
@media (max-width: 575.98px) {
  .modal .table-responsive-flush.table-responsive-sm {
    --table-flush: var(--modal-padding-x);
  }
}
.table-1 {
  --bs-table-border-color: #f1f5f9;
  --bs-table-cell-padding-x: 0.625rem;
  --bs-table-cell-padding-y: 0.625rem;
  --bs-border-color: var(--bs-table-border-color);
}
.table-1 > thead {
  --bs-link-color: var(--bs-table-color);
  --bs-link-hover-color: var(--bs-table-color);
  --bs-table-color: #566171;
  background-color: #f8fafc;
  vertical-align: inherit;
}
.table-1 > thead td,
.table-1 > thead th {
  --bs-table-border-color: #e6ecf2;
  border-bottom: 1px solid var(--bs-table-border-color);
  border-top: 1px solid var(--bs-table-border-color);
  color: var(--bs-table-color);
  font-size: 0.8125rem;
  font-weight: 700;
  height: 3.625rem;
  white-space: nowrap;
}
.modal .table-1 > thead td,
.modal .table-1 > thead th {
  height: 3.125rem;
}
.table-1 > thead .flatpickr-hide-input .flatpickr-input {
  height: 28px;
}
.table-1 > tbody + tbody:before {
  content: "";
  display: block;
  height: 1.5rem;
  pointer-events: none;
}
.table-1 .btn-action {
  font-size: 1.166875rem;
}
.table-1 .dropdown-toggle:after {
  margin-left: 0.25rem;
  transform: translateY(-1px);
}
.table-1 .dropdown-menu {
  font-weight: 400;
  max-width: calc(100vw - 24px);
  white-space: normal;
  width: 22.5rem;
}
.table-1 .table-dark-2 {
  --bs-table-color: var(--bs-white);
  --bs-table-bg: #566171;
  --bs-table-border-color: rgba(var(--bs-white-rgb), 0.2);
}
.table-1 .table-dark-2,
.table-1 .table-primary-light {
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
}
.table-1 .table-primary-light {
  --bs-table-color: #788fb0;
  --bs-table-bg: #edf5ff;
  --bs-table-border-color: #edf5ff;
}
.table-1 .table-gray-light {
  --bs-table-color: #a4b4cb;
  --bs-table-bg: #f5f5f5;
  --bs-table-border-color: #e0e0e0;
  border-color: var(--bs-table-border-color);
  color: var(--bs-table-color);
  height: auto;
}
.paginate {
  align-items: center;
  display: flex;
  gap: 1.25rem;
}
.paginate-btn-next,
.paginate-btn-prev {
  --bs-btn-padding-x: 0;
  --bs-btn-padding-y: 0;
  min-height: auto;
}
.table-width-xxl {
  min-width: 1200px;
}
.table-more {
  --bs-table-bg: #edf5ff;
}
.table-more-link {
  display: block;
  padding: var(--bs-table-cell-padding-y) var(--bs-table-cell-padding-x);
}
.table-more-link svg {
  margin-top: -0.125rem;
}
.dropdown-toggle:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.254 1.726 9.074.546l-3.82 3.822L1.434.547.253 1.726l5 5 5-5Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  background-size: 100% 100%;
  border: none;
  content: "";
  flex-shrink: 0;
  height: 0.375rem;
  margin-left: 0.5rem;
  margin-top: 0;
  transition: all 0.2s ease-in-out;
  width: 0.625rem;
}
@media (prefers-reduced-motion: reduce) {
  .dropdown-toggle:after {
    transition: none;
  }
}
.dropdown-menu {
  box-shadow: var(--bs-dropdown-box-shadow);
}
.dropdown-item {
  border-radius: 0.375rem;
}
.dropdown-item-checkbox {
  display: flex;
  gap: 0.5rem;
  white-space: normal;
}
.dropdown-item-checkbox .form-check-input {
  flex-shrink: 0;
  margin-top: 0.1428571429em;
}
.dropdown-item-checkbox .form-check-input-20px {
  margin-top: -1px;
}
.list {
  --list-gap: 1rem;
  --list-border-width: var(--bs-border-width);
  --list-border-color: var(--bs-border-color);
  --list-font-size: var(--bs-body-font-size);
  --list-item-padding-left: 0;
  --list-item-color: inherit;
  --list-item-hover-color: var(--bs-primary);
  --list-item-active-color: var(--bs-primary);
  --list-icon-color: var(--bs-gray-600);
  --list-icon-font-size: var(--list-font-size);
  --list-bullet-size: 4px;
  --list-bullet-color: var(--list-icon-color);
  font-size: var(--list-font-size);
  list-style: none;
  padding-left: 0;
}
.list-item {
  color: var(--list-item-color);
  padding-left: var(--list-item-padding-left);
  position: relative;
}
.list-item:not(:last-child) {
  margin-bottom: var(--list-gap);
}
.list-item.active {
  color: var(--list-item-active-color);
}
.list-item a {
  color: var(--list-item-color);
}
.list-item a:focus-visible,
.list-item a:hover {
  color: var(--list-item-hover-color);
}
.list .list {
  margin-top: var(--list-gap);
  padding-left: calc(var(--list-gap) * 2);
}
.list-icons {
  --list-item-padding-left: calc(var(--list-icon-font-size) + 0.5em);
}
.list-icons .list-icon {
  color: var(--list-icon-color);
  font-size: var(--list-icon-font-size);
  left: 0;
  position: absolute;
  top: calc(
    (
        var(--list-font-size) * var(--bs-body-line-height) -
          var(--list-icon-font-size)
      ) * 0.5
  );
}
.list-ordered {
  --list-item-padding-left: calc(var(--list-icon-font-size) + 0.5em);
  counter-reset: custom-counter;
}
.list-ordered .list-item:before {
  color: var(--list-icon-color);
  content: counter(custom-counter);
  counter-increment: custom-counter;
  font-weight: 700;
  left: 0;
  position: absolute;
  top: 0;
}
.list-bullets {
  --list-item-padding-left: calc(var(--list-bullet-size) + 0.75em);
}
.list-bullets .list-item:before {
  background-color: var(--list-bullet-color);
  border-radius: 50%;
  content: "";
  height: var(--list-bullet-size);
  left: 0;
  position: absolute;
  top: calc(
    (
        var(--list-font-size) * var(--bs-body-line-height) -
          var(--list-bullet-size)
      ) * 0.5
  );
  width: var(--list-bullet-size);
}
.list-border .list-item:not(:last-child) {
  border-bottom: var(--list-border-width) solid var(--list-border-color);
  margin-bottom: var(--list-gap);
  padding-bottom: var(--list-gap);
}
.list-inline {
  display: flex;
  flex-wrap: wrap;
}
.list-inline .list-item {
  margin-bottom: 0;
  white-space: nowrap;
}
.list-inline .list-item:not(:last-child) {
  margin-right: var(--list-gap);
}
.list-inline-border .list-item {
  display: flex;
}
.list-inline-border .list-item:not(:last-child) {
  margin-right: 0;
}
.list-inline-border .list-item:not(:last-child):after {
  color: var(--list-border-color);
  content: "|";
  padding: 0 var(--list-gap);
}
.list-sm {
  --list-font-size: 0.75rem;
  --list-gap: 0.5rem;
  --list-bullet-size: 0.3125rem;
}
.list-lg {
  --list-font-size: 1.125rem;
  --list-gap: 1.5rem;
  --list-bullet-size: 0.4375rem;
}
.list-gap-0 {
  --list-gap: 0;
}
.list-gap-1 {
  --list-gap: 0.25rem;
}
.list-gap-2 {
  --list-gap: 0.5rem;
}
.list-gap-3 {
  --list-gap: 1rem;
}
.list-gap-4 {
  --list-gap: 1.5rem;
}
.list-gap-5 {
  --list-gap: 3rem;
}
.media {
  align-items: flex-start;
  display: flex;
}
.media-body {
  flex: 1;
}
.media-list .media:not(:last-child) {
  margin-bottom: 2.25rem;
}
.overlay {
  background-color: rgba(0, 0, 0, 0.6);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1039;
}
.overlay-search {
  top: 3.25rem;
}
@media (max-width: 991.98px) {
  .overlay-search {
    top: 3.25rem;
  }
}
.search {
  display: flex;
  gap: 0.625rem;
}
@media (max-width: 991.98px) {
  .search {
    flex-direction: column;
  }
}
.search-input {
  padding-right: 2.5rem;
}
.search-btn {
  --bs-btn-border-radius: 0.75rem;
  --bs-btn-color: #566171;
  --bs-btn-bg: #fff;
  --bs-btn-border-color: #dee4f0;
  --bs-btn-hover-color: #566171;
  --bs-btn-hover-bg: #dee4f0;
  --bs-btn-hover-border-color: #dee4f0;
  --bs-btn-focus-shadow-rgb: 202, 208, 221;
  --bs-btn-active-color: #566171;
  --bs-btn-active-bg: #c8cdd8;
  --bs-btn-active-border-color: #c8cdd8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fff;
  --bs-btn-disabled-border-color: #dee4f0;
}
.search-btn:first-child {
  left: 0;
}
.search-btn:last-child {
  right: 0;
}
.search-btn ~ .search-input {
  padding-left: 2.5rem;
  padding-right: 1rem;
}
.search-btn-close {
  right: 0;
}
.search-btn-close ~ .search-input {
  padding-right: 2.5rem;
}
.search-result {
  margin-top: 0.5rem;
  width: 100%;
}
.search-result .dropdown-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:root {
  --nav-flush: 1.5rem;
}
.nav-responsive {
  margin-left: calc(var(--nav-flush) * -1);
  margin-right: calc(var(--nav-flush) * -1);
}
@media (max-width: 991.98px) {
  .nav-responsive {
    -webkit-overflow-scrolling: touch;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
  }
  .nav-responsive::-webkit-scrollbar {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .nav-responsive .nav {
    flex-wrap: nowrap;
  }
  .nav-responsive .nav-link {
    white-space: nowrap;
  }
}
.nav-responsive .nav-responsive-inner {
  padding-left: var(--nav-flush);
  padding-right: var(--nav-flush);
}
@media (max-width: 991.98px) {
  .nav-responsive .nav-responsive-inner {
    flex-grow: 1;
  }
  .nav-responsive-lg {
    --nav-flush: 1.5rem;
  }
}
@media (max-width: 767.98px) {
  .nav-responsive-md {
    --nav-flush: 1.5rem;
  }
}
@media (max-width: 575.98px) {
  .nav-responsive-sm {
    --nav-flush: 1.5rem;
  }
}
.nav-pills {
  border-radius: 50rem;
}
.nav-pills .nav-link {
  border-radius: 50rem;
  color: #8794aa;
  font-weight: 600;
  padding: 0.5625rem 1rem;
}
.nav-pills .nav-link:focus-visible:not(.active),
.nav-pills .nav-link:hover:not(.active) {
  color: #4a69d2;
}
.nav-tabs {
  --bs-nav-link-padding-x: 1.5rem;
  --bs-nav-link-padding-y: 0.875rem;
  --bs-nav-link-font-weight: bold;
  --bs-nav-link-font-size: 1rem;
  --bs-nav-link-color: #566171;
  --bs-nav-tabs-border-width: 0;
  --bs-nav-tabs-border-radius: 0.375rem;
  --bs-nav-tabs-link-active-color: #566171;
  --bs-nav-tabs-link-active-bg: var(--bs-white);
  gap: 0.5rem;
}
@media (max-width: 767.98px) {
  .nav-tabs {
    --bs-nav-link-font-size: 0.875rem;
  }
}
.nav-tabs .nav-link {
  background-color: #e5eaf1;
  color: #828282;
  text-align: center;
  text-transform: uppercase;
  transition: none;
}
.nav-tabs .nav-link.active {
  box-shadow: 0 -2px 8px -1px rgba(0, 0, 0, 0.08);
}
.tab-history {
  margin-left: auto;
  margin-right: auto;
  margin-top: 2.75rem;
  max-width: 50rem;
}
@media (min-width: 1200px) {
  .tab-history {
    width: 20rem;
  }
}
.tab-history .nav {
  --bs-nav-link-padding-x: 0.5rem;
  --bs-nav-link-font-size: var(--bs-body-font-size);
  gap: 0;
  position: relative;
}
.tab-history .nav:after {
  background-color: var(--bs-white);
  content: "";
  height: 0.5rem;
  left: 0;
  position: absolute;
  right: 0;
  top: 100%;
}
.tab-history .nav-item {
  display: flex;
}
.tab-history .nav-link {
  align-items: center;
  display: flex;
  justify-content: center;
}
.tab-history .tab-content {
  background-color: var(--bs-white);
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.05), 0 1px 6px rgba(0, 0, 0, 0.08);
}
.history {
  --history-item-padding-x: 1rem;
  --history-item-padding-y: 1rem;
  --history-item-bg: var(--bs-white);
}
.history-item {
  background-color: var(--history-item-bg);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: var(--history-item-padding-y) var(--history-item-padding-x);
  scroll-margin-top: calc(var(--header-height) + 1rem);
}
.history-item:not(:last-child) {
  border-bottom: 1px solid #dee4f0;
}
.history-item:last-child {
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.history-item-request {
  color: var(--bs-primary);
}
.history-item-unread {
  --history-item-bg: #edf5ff;
}
.history-item-unread .history-item-request {
  color: #4f4f4f;
}
.history .btn-action {
  opacity: 0;
}
.history-item:focus-visible .btn-action,
.history-item:hover .btn-action {
  opacity: 1;
}
.history-sub {
  background-color: #f5f5f5;
  display: grid;
  gap: var(--history-item-padding-y);
  margin: 0 calc(var(--history-item-padding-x) * -1)
    calc(var(--history-item-padding-x) * -1);
  padding: var(--history-item-padding-y) 0;
}
.history-sub .history-item {
  --history-item-bg: transparent;
  --history-item-padding-y: 0;
  border: none;
  padding-left: 2.25rem;
  position: relative;
}
.history-sub .history-item:before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 4.5 4.5 2 2 4.5' stroke='%23A4B4CB' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M10 10H6.5a2 2 0 0 1-2-2V2' stroke='%23A4B4CB' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100%;
  content: "";
  height: 12px;
  left: var(--history-item-padding-x);
  pointer-events: none;
  position: absolute;
  top: 0.625rem;
  width: 12px;
}
.history-sub + .history-reply {
  margin-left: 0;
  margin-right: 0;
  margin-top: 1.5rem;
}
.history-reply-content {
  background-color: #ededed;
  border: 1px solid #e1e1e1;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}
.history-reply,
.history-reply-content {
  margin-left: var(--history-item-padding-x);
  margin-right: var(--history-item-padding-x);
}
.reply {
  background-color: var(--bs-white);
  border-radius: 0.375rem;
  position: relative;
}
.reply-input {
  border: none;
  padding-left: 2rem;
  padding-right: 2.75rem;
}
.reply-input:after {
  border: 1px solid #dee4f0;
  border-radius: 0.375rem;
  bottom: 0;
  content: "";
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  transition: all 0.2s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .reply-input:after {
    transition: none;
  }
}
.reply-input:focus:after {
  border-color: #a5b4e9;
}
.reply-attachment {
  bottom: 0.75rem;
  cursor: pointer;
  left: 0.5rem;
  position: absolute;
}
.reply-btn {
  border-radius: 0.25rem;
  bottom: 2px;
  height: 2.25rem;
  min-height: auto;
  position: absolute;
  right: 2px;
  width: 2.25rem;
}
.reply .upload-list {
  margin: 0.5rem;
}
:root {
  --modal-padding-x: 1.875rem;
  --modal-padding-y: 1.875rem;
}
@media (max-width: 575.98px) {
  :root {
    --modal-padding-x: 1.25rem;
  }
}
.modal {
  --bs-modal-header-padding: 0;
  --bs-modal-header-border-width: 0;
  --form-horizontal-body-width: none;
}
.modal-title {
  margin-bottom: 1.5rem;
}
.modal-title-bg {
  background-color: #edf5ff;
  margin-left: calc(var(--modal-padding-x) * -1);
  margin-right: calc(var(--modal-padding-x) * -1);
  padding: 0.75rem var(--modal-padding-x);
}
.modal-btn-close {
  background-size: 30%;
  height: 2.5rem;
  position: absolute;
  right: 0;
  top: 0;
  width: 2.5rem;
}
.modal-content {
  background-color: #fff;
  border: none;
  padding: var(--modal-padding-y) var(--modal-padding-x);
}
@media (min-width: 576px) {
  .modal-xs {
    max-width: 19.25rem;
  }
  .modal-480px {
    max-width: 30rem;
  }
  .modal-500px {
    max-width: 31.25rem;
  }
  .modal-708px {
    max-width: 44.375rem;
  }
}
.modal-table-lg .modal-dialog {
  max-width: calc(50rem + var(--bs-modal-margin));
}
@media (min-width: 576px) {
  .modal-table-lg .modal-dialog {
    padding-left: var(--bs-modal-margin);
    padding-right: var(--bs-modal-margin);
  }
}
.modal-form .form-1 {
  background-color: transparent;
  border: none;
  padding: 0;
}
.modal-show {
  background-color: rgba(0, 0, 0, 0.6);
  display: block;
  opacity: 1 !important;
}
.modal-show .modal-dialog {
  transform: none !important;
}
.modal-435px {
  max-width: 27.1875rem;
}
.badge-code {
  padding: 0.375em 0.625em;
}
:root {
  --box-padding-x: 1.875rem;
  --box-padding-y: 1.875rem;
}
@media (max-width: 767.98px) {
  :root {
    --box-padding-x: 1rem;
  }
}
.box {
  display: flex;
  flex-direction: column;
}
.box-title {
  color: #566171;
  font-size: 1rem;
  margin-bottom: 1.25rem;
  text-transform: uppercase;
}
.box-title-flush {
  margin-left: calc(var(--box-padding-x) * -1);
  margin-right: calc(var(--box-padding-x) * -1);
}
.box-header {
  align-items: center;
  display: flex;
  margin-bottom: 1.25rem;
}
.box-header .box-title {
  margin-bottom: 0;
}
.box-more-link {
  align-items: center;
  display: inline-flex;
}
.box-body {
  background-color: var(--bs-white);
  border-radius: 1rem;
  box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.05), 0 1px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: var(--box-padding-y) var(--box-padding-x);
  position: relative;
}
@media (max-width: 767.98px) {
  .box-body {
    border-radius: 1.25rem;
  }
}
@media (min-width: 768px) {
  .box-body.p-md-30px {
    --box-padding-x: 1.875rem;
  }
  .box-btn {
    min-width: 12.5rem;
  }
}
.box-title-break {
  background-color: #f6f6f6;
  border-bottom: 1px solid #dee4f0;
  color: #566171;
  font-size: 0.8125rem;
  font-weight: 800;
  line-height: 1.23;
  margin-bottom: 0;
  padding: 0.75rem 1rem;
  text-align: center;
  text-transform: uppercase;
}
.box-table {
  --box-padding-x: 0;
}
.box .table-responsive-flush.table-responsive,
.box .table-responsive-flush.table-responsive-xs {
  --table-flush: var(--box-padding-x);
}
@media (max-width: 575.98px) {
  .box .table-responsive-flush.table-responsive-sm {
    --table-flush: var(--box-padding-x);
  }
}
@media (max-width: 767.98px) {
  .box .table-responsive-flush.table-responsive-md {
    --table-flush: var(--box-padding-x);
  }
}
@media (max-width: 991.98px) {
  .box .table-responsive-flush.table-responsive-lg {
    --table-flush: var(--box-padding-x);
  }
}
@media (max-width: 1199.98px) {
  .box .table-responsive-flush.table-responsive-xl {
    --table-flush: var(--box-padding-x);
  }
}
@media (max-width: 1439.98px) {
  .box .table-responsive-flush.table-responsive-xxl {
    --table-flush: var(--box-padding-x);
  }
}
.box-shadow {
  background-color: var(--bs-white);
  border-radius: 1rem;
  box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.05), 0 1px 6px rgba(0, 0, 0, 0.08);
  padding: var(--box-padding-y) var(--box-padding-x);
}
.box-shadow .box-body {
  box-shadow: none;
  padding: 0;
}
.box-border {
  background-color: var(--bs-white);
  border: 1px solid #dee4f0;
  border-radius: 1rem;
  padding: var(--box-padding-y) var(--box-padding-x);
}
.box-border .box-body {
  box-shadow: none;
  padding: 0;
}
.box {
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 767.98px) {
  .box .nav-tabs .nav-item {
    flex-basis: 0;
    flex-grow: 1;
  }
}
@media (min-width: 768px) {
  .box .nav-tabs .nav-link {
    min-width: 12rem;
  }
}
@media (max-width: 767.98px) {
  .box .tab-content .box-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
@media (min-width: 992px) {
  .box-lg {
    max-width: 100%;
    width: 50rem;
  }
}
.box-md {
  --form-horizontal-label-width: 8.125rem;
  --form-horizontal-body-width: 22.375rem;
  max-width: 40rem;
}
@media (min-width: 768px) {
  .box-md .col-form-body {
    max-width: var(--form-horizontal-body-width);
  }
}
.box-item-flush {
  margin-left: calc(var(--box-padding-x) * -1);
  margin-right: calc(var(--box-padding-x) * -1);
}
.box-item-flush .table,
.box-item-flush .table-responsive {
  --box-padding-x: 0;
}
.box-item-flush .table-bordered td:first-child,
.box-item-flush .table-bordered th:first-child {
  border-left: none;
}
.box-item-flush .table-bordered td:last-child,
.box-item-flush .table-bordered th:last-child {
  border-right: none;
}
.box-item-flush .table-bordered tr:first-child td,
.box-item-flush .table-bordered tr:first-child th {
  border-top: none;
}
.box-item-flush .table-bordered tr:last-child td,
.box-item-flush .table-bordered tr:last-child th {
  border-bottom: none;
}
.step {
  --step-bg: #e5eaf1;
  --step-flush: 0px;
  --step-item-height: 3.75rem;
  --step-item-bg: var(--step-bg);
  --step-item-color: #566171;
  --step-item-success-bg: #6fcf97;
  --step-item-success-color: var(--bs-white);
  --step-item-active-bg: #213b94;
  --step-item-active-color: var(--bs-white);
  background-color: var(--step-bg);
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  margin-top: -2.25rem;
  position: relative;
}
@media (min-width: 768px) {
  .step {
    padding: 0 0.75rem;
  }
}
.step-inner {
  display: flex;
  padding-right: 1.25rem;
}
@media (min-width: 768px) {
  .step-inner {
    position: relative;
  }
  .step-inner:after,
  .step-inner:before {
    background-color: var(--step-bg);
    border-right: 2px solid #fff;
    content: "";
    height: 30px;
    left: 0;
    pointer-events: none;
    position: absolute;
    width: 1.25rem;
    z-index: 1;
  }
  .step-inner:before {
    top: 0;
    transform: skew(30deg);
    transform-origin: right bottom;
  }
  .step-inner:after {
    bottom: 0;
    transform: skew(-30deg);
    transform-origin: right top;
  }
}
.step-item {
  align-items: center;
  background-color: var(--step-item-success-bg);
  color: var(--step-item-success-color);
  display: flex;
  font-weight: 700;
  height: var(--step-item-height);
  justify-content: center;
  padding: 0.5rem 2rem 0.5rem 3.25rem;
  position: relative;
  text-align: center;
}
.step-item:after,
.step-item:before {
  background-color: var(--step-item-success-bg);
  border-right: 2px solid #fff;
  content: "";
  height: 30px;
  left: 100%;
  pointer-events: none;
  position: absolute;
  width: 1.25rem;
  z-index: 1;
}
.step-item:before {
  top: 0;
  transform: skew(30deg);
  transform-origin: right bottom;
}
.step-item:after {
  bottom: 0;
  transform: skew(-30deg);
  transform-origin: right top;
}
@media (max-width: 767.98px) {
  .step-item {
    flex: 1 1 auto;
  }
  .step-item:first-child {
    padding-left: 2rem;
  }
}
@media (min-width: 1200px) {
  .step-item {
    min-width: 11.875rem;
  }
}
.step-item.active,
.step-item.active:after,
.step-item.active:before {
  background-color: var(--step-item-active-bg);
  color: var(--step-item-active-color);
}
.step-item.active ~ .step-item,
.step-item.active ~ .step-item:after,
.step-item.active ~ .step-item:before {
  background-color: var(--step-item-bg);
  color: var(--step-item-color);
}
@media (max-width: 767.98px) {
  .step {
    justify-content: flex-start;
    margin-left: calc(var(--step-flush) * -1);
    margin-right: calc(var(--step-flush) * -1);
  }
  .step .step-scroll {
    overflow-scrolling: touch;
    -webkit-overflow-scrolling: touch;
    flex-grow: 1;
    overflow-x: auto;
    overflow-y: hidden;
    padding-left: var(--step-flush);
    padding-right: var(--step-flush);
  }
  .step .step-scroll::-webkit-scrollbar {
    display: none;
  }
  .step .step-inner {
    flex-wrap: nowrap;
  }
  .step .step-item {
    white-space: nowrap;
  }
}
.header-filter-show .step {
  z-index: auto;
}
.step-timeline {
  bottom: 0;
  display: flex;
  position: absolute;
  right: 1.5rem;
  top: 0;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .step-timeline {
    bottom: auto;
    left: 50%;
    margin-top: 0.75rem;
    position: absolute;
    right: auto;
    top: 100%;
    transform: translateX(-50%);
  }
}
.step-timeline .dropdown-menu {
  min-width: 20rem;
}
@media (max-width: 575.98px) {
  .step-timeline .dropdown-menu {
    width: 100vw;
  }
}
.timeline {
  --timeline-dot-size: 0.5625rem;
  --timeline-dot-color: #d9d9d9;
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
  max-height: 29.375rem;
  overflow-x: hidden;
  overflow-y: auto;
}
@media (max-width: 991.98px) {
  .timeline {
    max-height: 19.5rem;
  }
}
.timeline-inner {
  display: grid;
  gap: 0.25rem;
  position: relative;
}
.timeline-inner:before {
  background-color: #d9d9d9;
  bottom: 0;
  content: "";
  left: calc((var(--timeline-dot-size) - 1px) * 0.5);
  position: absolute;
  top: 1.625rem;
  width: 1px;
}
.timeline-item {
  border-radius: var(--bs-dropdown-border-radius);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-left: 1rem;
  padding: 0.5rem;
  position: relative;
}
.timeline-item:before {
  background-color: var(--timeline-dot-color);
  border-radius: 50%;
  content: "";
  height: var(--timeline-dot-size);
  left: -1rem;
  position: absolute;
  top: 1.4375rem;
  width: var(--timeline-dot-size);
}
.timeline-item:focus-visible,
.timeline-item:hover {
  background-color: var(--bs-dropdown-link-hover-bg);
  color: var(--bs-dropdown-link-hover-color);
}
.timeline-item.active {
  background-color: rgba(226, 236, 249, 0.5);
}
.timeline-title {
  font-size: 1rem;
  line-height: 1.375;
  text-decoration: underline;
}
.loading {
  align-items: center;
  background-color: #e5eaf1;
  display: flex;
  height: calc(100vh - 512px);
  justify-content: center;
  min-height: 18.75rem;
  padding: 1.5rem;
}
.loading-body {
  flex-grow: 1;
  max-width: 22.5rem;
}
.loading-current {
  font-size: 1.25rem;
}
.loading-note {
  color: #566171;
}
.loading-logo {
  display: block;
  margin-bottom: 3.75rem;
  width: 12.375rem;
}
.loading .progress {
  --bs-progress-height: 0.5rem;
  --bs-progress-bg: #a4b4cb;
  --bs-progress-bar-bg: #213b94;
}
.page-loading {
  align-items: center;
  background-color: #24346a;
  color: var(--bs-white);
  display: flex;
  justify-content: center;
}
.page-loading .loading {
  background-color: transparent;
  min-height: 100vh;
  width: 100%;
}
@media (max-width: 991.98px) {
  .page-loading .loading {
    min-height: -webkit-fill-available;
  }
}
.page-loading .loading-note {
  color: #a4b4cb;
}
.page-loading .progress {
  --bs-progress-bg: #566171;
  --bs-progress-bar-bg: var(--bs-white);
}
.page-loading .loading-body {
  max-width: 28.75rem;
}
.error404 {
  align-items: center;
  background-color: #fcfaff;
  display: flex;
  justify-content: center;
  min-height: 100vh;
}
@media (max-width: 991.98px) {
  .error404 {
    min-height: -webkit-fill-available;
  }
}
.error404-body {
  flex-grow: 1;
  max-width: 22.5rem;
}
.tag {
  border: 1px solid #dee4f0;
  border-radius: 0.75rem;
  gap: 0.5rem;
  justify-content: space-between;
  padding: 0.5rem;
  position: relative;
}
.tag,
.tag-body {
  align-items: flex-start;
  display: flex;
}
.tag-body {
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-right: -0.5rem;
  max-height: 5.75rem;
  overflow-x: hidden;
  overflow-y: auto;
  padding-right: 1.875rem;
}
.tag-clear {
  --bs-btn-bg: #8f969f;
  --bs-btn-color: var(--bs-white);
  --bs-btn-hover-bg: #81878f;
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-active-bg: #72787f;
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-border-radius: 50%;
  --bs-btn-font-size: 1rem;
  color: var(--bs-white);
  height: 1em;
  margin-top: 0.375rem;
  min-height: auto;
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
  width: 1em;
}
.tag-clear svg {
  font-size: 0.75em;
}
.tag-item {
  background-color: #f5f5f5;
  border: 1px solid #edf5ff;
  border-radius: 0.375rem;
  color: #566171;
  font-size: 0.8125rem;
  padding: 0.25rem 0.5rem;
}
.tag-item-close {
  --bs-btn-font-size: 1.2307692308em;
  min-height: auto;
  opacity: 0.7;
  transition: all 0.2s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .tag-item-close {
    transition: none;
  }
}
.tag-item-close:focus-visible,
.tag-item-close:hover {
  opacity: 1;
}
.tag-item-close svg {
  font-size: 0.8125em;
}
.tag .badge {
  border-radius: 0.375rem;
}
.tag-input {
  margin-top: 0.1875rem;
  min-width: 10px;
}
.tag-input:focus {
  outline: none;
}
.swal2-popup.swal2-toast .swal2-icon {
  align-self: flex-start;
  border: none;
  height: auto;
  margin-right: 1rem;
  margin-top: 0.125rem;
  min-width: auto;
  width: auto;
}
.swal2-popup.swal2-toast .swal2-icon [class*="iconsvg"] {
  font-size: inherit;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-success {
  color: #0c8809;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-alert {
  color: #dca048;
}
.swal2-popup.swal2-toast .swal2-icon.swal2-error {
  color: #fb3836;
}
.swal2-popup.swal2-toast .swal2-close {
  align-self: flex-start;
  box-shadow: none;
  margin-left: 0.5rem;
}
.swal2-popup.swal2-toast .swal2-close [class*="iconsvg"] {
  font-size: inherit;
}
.swal2-popup.swal2-toast .swal2-html-container {
  color: #a4b4cb;
}
@keyframes swal2-toast-show {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes swal2-toast-hide {
  to {
    opacity: 0;
  }
}
html {
  --scrollbar-width: 0;
  --scrollbar-inner-width: 0;
}
@media (min-width: 992px) {
  html {
    --scrollbar-width: 10px;
    --scrollbar-inner-width: 6px;
    --scrollbar-track-color: #eee;
    --scrollbar-thumb-color: #a2a2a2;
    --scrollbar-thumb-hover-color: #888;
    --scrollbar-thumb-border-radius: 0;
    --scrollbar-thumb-border-width: 0;
  }
}
@media (min-width: 992px) and (hover: none) {
  html {
    --scrollbar-width: 0px;
  }
}
@media (min-width: 992px) {
  html body > * {
    --scrollbar-track-color: transparent;
    --scrollbar-thumb-color: #bdbdbd;
    --scrollbar-thumb-hover-color: #a4a4a4;
    --scrollbar-thumb-border-radius: var(--bs-border-radius-pill);
  }
}
@media (min-width: 992px) {
  html body::-webkit-scrollbar {
    height: var(--scrollbar-width);
    width: var(--scrollbar-width);
  }
  html body ::-webkit-scrollbar {
    height: var(--scrollbar-inner-width);
    width: var(--scrollbar-inner-width);
  }
  html ::-webkit-scrollbar {
    background-color: transparent;
    height: var(--scrollbar-width);
    width: var(--scrollbar-width);
  }
  html ::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border: var(--scrollbar-thumb-border-width) solid
      var(--scrollbar-track-color);
    border-radius: var(--scrollbar-thumb-border-radius);
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
  }
}
@media (min-width: 992px) and (prefers-reduced-motion: reduce) {
  html ::-webkit-scrollbar-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
@media (min-width: 992px) {
  html ::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover-color);
  }
  html ::-webkit-scrollbar-track {
    background-color: var(--scrollbar-track-color);
  }
}
.page {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: 1920px;
  min-height: 100vh;
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .page {
    min-height: -webkit-fill-available;
  }
}
@media (min-width: 992px) {
  .page {
    padding-left: var(--sidebar-width);
  }
  .page.sidebar-narrow {
    --sidebar-width: 3rem;
  }
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .page {
    --sidebar-width: 3rem;
  }
}
.page-reset {
  padding: 0;
}
.page-reset .header {
  left: 0;
}
.page-reset .page-main {
  padding-bottom: 0;
  padding-top: 0;
}
.page-main {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding-bottom: 3rem;
  padding-top: 2.25rem;
  width: 100%;
}
.page-nosidebar {
  padding: 0;
}
.page-nosidebar .header,
.page-nosidebar .header.headroom--not-top {
  left: 0;
}
@media (min-width: 992px) {
  .container,
  .container-fluid {
    --bs-gutter-x: 3.75rem;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1420px;
  }
}
@media (min-width: 992px) {
  .container-lg-fluid {
    max-width: none;
  }
}
.row-main {
  --bs-gutter-x: 1.875rem;
}
:root {
  --header-height: 4.5rem;
}
@media (max-width: 991.98px) {
  :root {
    --header-height: 3.125rem;
  }
}
.header {
  background-color: var(--bs-white);
  box-shadow: 0 10px 60px rgba(226, 236, 249, 0.5);
  display: flex;
  height: var(--header-height);
  max-width: 1920px;
  position: relative;
}
@media (min-width: 992px) {
  .header {
    padding: 1rem 0;
  }
}
@media screen and (min-width: 1920.02px) {
  .header {
    margin: 0 auto;
    width: calc(1920px - var(--sidebar-width));
  }
}
.header > .container {
  align-items: center;
  display: flex;
  gap: 1rem;
  justify-content: space-between;
}
@media (min-width: 992px) {
  .header > .container {
    max-width: none;
  }
}
.header-logo {
  display: block;
  flex-shrink: 0;
  width: 8.75rem;
}
@media (max-width: 991.98px) {
  .header-logo {
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 6.875rem;
  }
}
.header.headroom--not-top {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1), 0 10px 60px rgba(226, 236, 249, 0.5);
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1030;
}
@media (min-width: 992px) {
  .header.headroom--not-top {
    left: var(--sidebar-width);
  }
}
.header.headroom--not-top ~ .page-main {
  margin-top: var(--header-height);
}
.header.headroom--not-top ~ .page-main .step {
  z-index: auto;
}
.modal-open .headroom--not-top {
  right: var(--scrollbar-width);
}
@media (min-width: 992px) {
  .header-filter {
    flex-grow: 1;
    margin-left: auto;
    margin-right: auto;
    max-width: 58.4375rem;
  }
}
@media (max-width: 991.98px) {
  .header-filter {
    --bs-offcanvas-width: 18.75rem;
    max-width: 90%;
  }
}
.header-filter .offcanvas-body .row {
  --bs-gutter-x: 0.625rem;
  --bs-gutter-y: 0.625rem;
}
.header-filter .dropdown-toggle.btn,
.header-filter .form-control,
.header-filter .search-input {
  border-radius: 0.75rem;
}
.header-filter .select2 {
  --select2-border-radius: 0.75rem;
  min-width: 8.4375rem;
}
.header-filter .flatpickr {
  position: relative;
}
.header-filter .flatpickr .flatpickr-calendar {
  left: 0 !important;
  top: 2.5rem !important;
}
@media (max-width: 991.98px) {
  .header-filter .flatpickr .flatpickr-calendar {
    left: auto !important;
    right: 0 !important;
  }
}
.header-filter .flatpickr-range > .flatpickr-input {
  min-width: auto;
}
@media (min-width: 992px) {
  .header-filter .flatpickr-range > .flatpickr-input {
    min-width: 14.0625rem;
  }
}
.header-filter .flatpickr-input {
  border-radius: 0.75rem;
}
@media (max-width: 991.98px) {
  .header-filter .flatpickr-calendar {
    max-width: 100%;
  }
}
.header-filter .vscomp-toggle-button {
  border-radius: 0.75rem;
}
@media (min-width: 992px) {
  .header-filter .vscomp-ele-wrapper.vscomp-status {
    width: 11.625rem;
  }
}
.select2-sort + .select2 {
  --select2-icon: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='m3.435 9.43-.232.24V3.6a.8.8 0 0 0-1.6 0v6.07l-.232-.24a.803.803 0 0 0-1.136 1.136l1.6 1.6a.8.8 0 0 0 .264.167.752.752 0 0 0 .608 0 .8.8 0 0 0 .264-.168l1.6-1.6A.803.803 0 1 0 3.434 9.43Zm3.767-5.031H15.2a.8.8 0 0 0 0-1.6H7.202a.8.8 0 1 0 0 1.6Zm7.998 2.4H7.202a.8.8 0 0 0 0 1.6H15.2a.8.8 0 0 0 0-1.6Zm0 3.999H7.202a.8.8 0 1 0 0 1.6H15.2a.8.8 0 0 0 0-1.6Z' fill='%23A4B4CB'/%3E%3C/svg%3E");
  --select2-icon-size: 1rem;
  --select2-icon-position-right: 1rem;
}
.badge-gui-duyet {
  --bs-badge-color: #0d99ff;
}
.badge-gui-duyet,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="gui-duyet"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="gui-duyet"] {
  background-color: #dcf1ff;
}
.badge-cho-duyet {
  --bs-badge-color: #f48620;
}
.badge-cho-duyet,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="cho-duyet"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="cho-duyet"] {
  background-color: #fff6ee;
}
.badge-da-duyet {
  --bs-badge-color: #0c8809;
}
.badge-da-duyet,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="da-duyet"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="da-duyet"] {
  background-color: #e3f0d9;
}
.badge-gui-dang {
  --bs-badge-color: #458ab9;
}
.badge-gui-dang,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="gui-dang"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="gui-dang"] {
  background-color: #ddeefa;
}
.badge-cho-dang {
  --bs-badge-color: #ff6d19;
}
.badge-cho-dang,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="cho-dang"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="cho-dang"] {
  background-color: #fcf2ec;
}
.badge-xuat-ban {
  --bs-badge-color: #096d34;
}
.badge-xuat-ban,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="xuat-ban"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="xuat-ban"] {
  background-color: #e7f6ee;
}
.badge-bai-nhap {
  --bs-badge-color: #8f969f;
}
.badge-bai-nhap,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="bai-nhap"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="bai-nhap"] {
  background-color: #ecf0f6;
}
.badge-da-huy {
  --bs-badge-color: #6d1b1c;
}
.badge-da-huy,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="da-huy"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="da-huy"] {
  background-color: #f9e2e2;
}
.badge-tra-lai {
  --bs-badge-color: #bf472d;
}
.badge-tra-lai,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="tra-lai"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="tra-lai"] {
  background-color: #f8f1ea;
}
.badge-giu-cho {
  --bs-badge-color: #f2586e;
}
.badge-giu-cho,
.select2-container--default
  .select2-dropdown
  .select2-results__option--highlighted[data-select2-id*="giu-cho"],
.select2-container--default
  .select2-dropdown
  .select2-results__option--selected[data-select2-id*="giu-cho"] {
  background-color: #fff0f1;
}
.vscomp-status .vscomp-toggle-all-label {
  font-size: 0.8125rem;
  font-weight: 600;
}
.vscomp-status .checkbox-icon {
  margin-right: 0.625rem;
  position: relative;
}
.vscomp-status .vscomp-option.focused {
  background-color: transparent;
}
@media (min-width: 992px) {
  .vscomp-status .vscomp-dropbox-container {
    min-width: 24.375rem;
  }
  .vscomp-status .vscomp-options {
    display: flex;
    flex-wrap: wrap;
  }
  .vscomp-status .vscomp-options-list {
    height: auto !important;
  }
  .vscomp-status .vscomp-option {
    width: 33.3333333333%;
  }
}
.dropdown-status .dropdown-toggle {
  justify-content: space-between;
  width: 100%;
}
.dropdown-status .dropdown-menu {
  --bs-dropdown-padding-x: 1.25rem;
  --bs-dropdown-padding-y: 1.25rem;
  min-width: 23.75rem;
}
@media (max-width: 991.98px) {
  .dropdown-status .dropdown-menu {
    overflow-scrolling: touch;
    -webkit-overflow-scrolling: touch;
    max-height: 18.75rem;
    max-width: 100%;
    min-width: auto;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
.dropdown-status .dropdown-menu.show {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}
.dropdown-status .dropdown-menu .row {
  --bs-gutter-x: 1.25rem;
  --bs-gutter-y: 1.25rem;
}
@media (max-width: 991.98px) {
  .dropdown-status .dropdown-menu .row {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }
}
.header-action {
  align-items: center;
  display: flex;
  gap: 0.625rem;
}
@media (min-width: 768px) {
  .header-action {
    gap: 1rem;
  }
}
@media (min-width: 1200px) {
  .header-action {
    gap: 1.25rem;
  }
}
.header-action-item {
  align-items: center;
  display: flex;
  min-height: 2.375rem;
}
@media (max-width: 991.98px) {
  .header-action-item {
    min-height: var(--header-height);
  }
}
.header-action-link {
  --bs-link-color: #a4b4cb;
}
.header-action-icon {
  font-size: 1.5rem;
}
@media (max-width: 991.98px) {
  .header-action-icon {
    font-size: 1.25rem;
  }
}
@media (min-width: 992px) {
  .header-action .dropdown-menu[data-popper-placement*="bottom"] {
    margin-top: 0.5rem !important;
  }
}
.header-notification .dropdown-toggle:after {
  display: none;
}
.header-notification .dropdown-toggle.show {
  color: #415ec0;
}
.header-notification .dropdown-menu {
  --bs-dropdown-link-color: #141735;
  max-width: 21.25rem;
}
@media (max-width: 575.98px) {
  .header-notification .dropdown-menu {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    max-width: none;
    width: 100vw;
  }
}
.header-notification .dropdown-toggle {
  position: relative;
}
.header-notification .dropdown-toggle .badge {
  --bs-badge-padding-x: 0;
  --bs-badge-padding-y: 0;
  --bs-badge-font-size: 0.625rem;
  align-items: center;
  bottom: 100%;
  display: flex;
  height: 1rem;
  justify-content: center;
  left: 100%;
  margin-bottom: -0.4375rem;
  margin-left: -0.75rem;
  position: absolute;
  width: 1rem;
}
.header-notification .dropdown-item {
  display: flex;
  white-space: normal;
  width: auto;
}
.header-notification .dropdown-item.new {
  background-color: #f5f7fa;
}
.header-notification .dropdown-item.new:focus-visible,
.header-notification .dropdown-item.new:hover {
  background-color: var(--bs-dropdown-link-hover-bg);
}
.header-notification .dropdown-item.new.active,
.header-notification .dropdown-item.new:active {
  background-color: var(--bs-dropdown-link-active-bg);
}
.header-notification .dropdown-item.new:after {
  align-self: center;
  background-color: var(--bs-success);
  border-radius: 50%;
  content: "";
  flex-shrink: 0;
  height: 0.5rem;
  margin-left: 1rem;
  width: 0.5rem;
}
.header-notification .dropdown-item:not(:last-child) {
  margin-bottom: 0.25rem;
}
@media (max-width: 1199.98px) {
  .header-acc .dropdown-toggle:after {
    display: none;
  }
}
.header-acc-avatar {
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
}
@media (max-width: 991.98px) {
  .header-acc-avatar {
    height: 1.25rem;
    width: 1.25rem;
  }
}
.header-acc-name {
  font-size: 0.8125rem;
  font-weight: 700;
}
.header-acc-info {
  color: #ad9d85;
  font-size: 0.75rem;
}
.header-acc .dropdown-toggle {
  align-items: center;
  display: flex;
}
.header-acc .dropdown-menu {
  --bs-dropdown-min-width: 14.375rem;
}
.footer {
  --footer-bg: var(--bs-white);
  --footer-color: #566171;
  --footer-link-color: var(--footer-color);
  --footer-link-hover-color: var(--bs-link-hover-color);
  background-color: var(--footer-bg);
  color: var(--footer-color);
  font-size: 0.75rem;
  padding: 0.75rem 0;
}
@media (max-width: 991.98px) {
  .footer {
    padding: 1.5rem 0;
  }
}
.footer a {
  --bs-link-color: var(--footer-link-color);
  --bs-link-hover-color: var(--footer-link-hover-color);
}
.footer-item {
  display: flex;
}
.footer-item-icon {
  color: #a4b4cb;
  flex-shrink: 0;
  margin-right: 0.625rem;
  margin-top: 0.125rem;
}
.footer-hotline {
  border: 1px solid #a4b4cb;
  border-radius: 50rem;
  color: #566171;
  padding: 0.375rem;
}
.footer-hotline-icon {
  align-items: center;
  display: flex;
  height: 1.25rem;
  justify-content: center;
  width: 1.25rem;
}
.btn-hotline,
.footer .btn-hotline {
  min-height: auto;
}
.btn-hotline {
  --bs-btn-padding-x: 0.375rem;
  --bs-btn-padding-y: 0.375rem;
  --bs-btn-color: #566171;
  --bs-btn-bg: #fff;
  --bs-btn-border-color: #a4b4cb;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #fff;
  --bs-btn-hover-border-color: #adbcd0;
  --bs-btn-focus-shadow-rgb: 152, 168, 190;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #fff;
  --bs-btn-active-border-color: #adbcd0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fff;
  --bs-btn-disabled-border-color: #a4b4cb;
  padding-right: 1.25rem;
}
.btn-hotline .btn-icon {
  align-items: center;
  background-color: #f2c144;
  border-radius: 50%;
  color: #141735;
  display: flex;
  height: 1.25rem;
  justify-content: center;
  width: 1.25rem;
}
.footer-dark,
.page-dark .footer {
  --footer-bg: #0b163a;
  --footer-color: #bdbdbd;
}
.footer-dark .btn-hotline,
.page-dark .footer .btn-hotline {
  --bs-btn-color: #fff;
  --bs-btn-bg: #24346a;
  --bs-btn-border-color: #24346a;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #1f2c5a;
  --bs-btn-hover-border-color: #1d2a55;
  --bs-btn-focus-shadow-rgb: 69, 82, 128;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #1d2a55;
  --bs-btn-active-border-color: #1b2750;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #24346a;
  --bs-btn-disabled-border-color: #24346a;
}
:root {
  --sidebar-width: 13.75rem;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  :root {
    --sidebar-width: 3rem;
  }
}
.sidebar {
  --sidebar-padding-x: 0.625rem;
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
  background-color: var(--bs-white) !important;
  bottom: 0;
  box-shadow: 1px 0 0 #dee4f0;
  left: 0;
  max-width: var(--sidebar-width);
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 var(--sidebar-padding-x);
  position: fixed;
  top: 0;
  transition: all 0.2s ease-in-out;
  width: var(--sidebar-width);
  z-index: 1050;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }
}
@media screen and (min-width: 1921px) {
  .sidebar {
    left: calc(50% - 960px);
  }
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) {
    --sidebar-padding-x: 0;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar {
    --sidebar-padding-x: 0;
  }
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:focus-visible,
  .sidebar:hover {
    --sidebar-width: 13.75rem;
  }
}
@media (max-width: 991.98px) {
  .sidebar {
    box-shadow: none;
    display: flex;
    flex-direction: column;
    transform: translateX(-100%);
    transition: all 0.3s ease-in-out;
    width: 90%;
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }
}
.sidebar-logo {
  display: flex;
  margin: 1.875rem auto;
  padding: 0 0.625rem;
  width: 8.75rem;
}
.sidebar-logo-compact {
  display: none;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-logo {
    width: var(--sidebar-width);
  }
  .sidebar:not(:hover) .sidebar-logo-full {
    display: none;
  }
  .sidebar:not(:hover) .sidebar-logo-compact {
    display: block;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-logo {
    width: var(--sidebar-width);
  }
  .sidebar-narrow .sidebar-logo-full {
    display: none;
  }
  .sidebar-narrow .sidebar-logo-compact {
    display: block;
  }
}
.sidebar-logo img {
  height: 2.375rem;
  width: 100%;
}
.sidebar-close {
  left: 0.5rem;
  position: absolute;
  top: 0.5rem;
}
.overlay-sidebar {
  display: block;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
  visibility: hidden;
  z-index: 1049;
}
@media (prefers-reduced-motion: reduce) {
  .overlay-sidebar {
    transition: none;
  }
}
.sidebar-nav {
  --bs-nav-link-padding-y: 0.75rem;
  --bs-nav-link-font-weight: 700;
  flex-direction: column;
  gap: 0.5rem;
  line-height: 1.2;
  margin-left: calc(var(--sidebar-padding-x) * -1);
  margin-right: calc(var(--sidebar-padding-x) * -1);
}
.sidebar-nav .btn {
  --bs-btn-border-radius: 0.625rem;
  justify-content: flex-start;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-nav .btn {
    --bs-btn-padding-x: 0.5625rem;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-nav .btn {
    --bs-btn-padding-x: 0.5625rem;
  }
}
.sidebar-nav .btn > svg {
  color: inherit;
}
.sidebar-nav .btn.active {
  --bs-btn-border-radius: 0;
  margin-left: calc(var(--sidebar-padding-x) * -1);
  margin-right: calc(var(--sidebar-padding-x) * -1);
  position: relative;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-nav .btn.active {
    justify-content: center;
    margin-left: -0.25rem;
    margin-right: -0.25rem;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-nav .btn.active {
    justify-content: center;
    margin-left: -0.25rem;
    margin-right: -0.25rem;
  }
}
.sidebar-nav .btn.active:after {
  background-color: var(--bs-btn-active-color);
  bottom: -1px;
  content: "";
  position: absolute;
  right: -1px;
  top: -1px;
  width: 0.1875rem;
}
.sidebar-nav .btn-outline-danger {
  --bs-btn-border-color: rgba(var(--bs-danger-rgb), 0.3);
}
.sidebar-nav .btn-outline-primary {
  --bs-btn-border-color: rgba(var(--bs-primary-rgb), 0.3);
}
.sidebar-nav .btn-outline-success {
  --bs-btn-border-color: rgba(var(--bs-success-rgb), 0.3);
}
.sidebar-nav .nav-item-header {
  --bs-nav-link-padding-y: 0.5rem;
  background-color: #f5f5f5;
  color: #a4b4cb;
  font-size: 0.8125rem;
  font-weight: 700;
  line-height: 1.2;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  text-transform: uppercase;
  white-space: nowrap;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-nav .nav-item-header {
    display: none;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-nav .nav-item-header {
    display: none;
  }
}
.sidebar-nav .nav-item-btn {
  display: flex;
  padding: 0 var(--sidebar-padding-x);
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-nav .nav-item-btn {
    padding: 0 0.25rem;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-nav .nav-item-btn {
    padding: 0 0.25rem;
  }
}
.sidebar-nav .nav-item-btn .btn {
  flex-grow: 1;
}
.sidebar-nav .nav-link {
  display: flex;
}
.sidebar-nav .nav-link.active {
  background-color: #edf5ff;
  color: #213b94;
  position: relative;
}
.sidebar-nav .nav-link.active:after {
  background-color: #213b94;
  bottom: 0;
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 0.1875rem;
}
.sidebar-nav .nav-link.active .nav-icon {
  color: #213b94;
}
.sidebar-nav .nav-icon {
  color: #a4b4cb;
  flex-shrink: 0;
  font-size: 1rem;
  height: 1em;
  width: 1em;
}
.sidebar-nav .nav-text {
  margin-left: 0.5rem;
  white-space: nowrap;
}
@media screen and (min-width: 992px) and (max-width: 1439.98px) {
  .sidebar:not(:hover) .sidebar-nav .nav-text {
    display: none;
  }
}
@media (min-width: 1200px) {
  .sidebar-narrow .sidebar-nav .nav-text {
    display: none;
  }
}
.sidebar-nav .btn-outline-primary.active {
  --bs-btn-active-bg: #edf0fb;
  --bs-btn-active-color: #4a69d2;
  --bs-btn-active-border-color: #edf0fb;
}
.sidebar-nav .btn-outline-danger.active {
  --bs-btn-active-bg: #feeeed;
  --bs-btn-active-color: #f2564c;
  --bs-btn-active-border-color: #feeeed;
}
.sidebar-nav .btn-outline-success.active {
  --bs-btn-active-bg: #e7f3e6;
  --bs-btn-active-color: #0c8809;
  --bs-btn-active-border-color: #e7f3e6;
}
.box-stat .row {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 0.625rem;
}
.stat {
  background-color: var(--bs-white);
  border-radius: 1rem;
  box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.05), 0 1px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  font-size: 1rem;
  padding: 1.875rem;
}
.stat-body {
  align-items: flex-end;
  display: flex;
  gap: 0.625rem;
}
.stat-title {
  color: #566171;
  font-weight: 600;
}
.stat-number {
  font-size: calc(1.35rem + 1.2vw);
  font-weight: 300;
  line-height: 1;
}
@media (min-width: 1200px) {
  .stat-number {
    font-size: 2.25rem;
  }
}
.stat-icon {
  flex-shrink: 0;
  font-size: 1.5rem;
  height: 1em;
  margin-bottom: 0.3125rem;
  width: 1em;
}
.tab-pane-dung-bai {
  margin-left: auto;
  margin-right: auto;
  max-width: 50rem;
}
.filter-btn {
  --bs-btn-font-weight: normal;
  --bs-btn-border-radius: 0.75rem;
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1.25rem;
  --bs-btn-border-color: #dee4f0;
  --bs-btn-bg: var(--bs-white);
  --bs-btn-hover-border-color: #a4b4cb;
  --bs-btn-hover-bg: var(--bs-white);
  --bs-btn-active-border-color: #a4b4cb;
  --bs-btn-active-bg: #edf5ff;
  align-items: stretch;
  flex-direction: column;
  text-align: left;
}
.filter .dropdown-toggle {
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.filter .dropdown-toggle:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='11' height='7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10.254 1.726 9.074.546l-3.82 3.822L1.434.547.253 1.726l5 5 5-5Z' fill='%23566171'/%3E%3C/svg%3E");
  margin-left: 0.25rem;
  transform: translateY(-1px);
}
.filter .dropdown-menu {
  font-weight: 400;
  max-width: calc(100vw - 24px);
  white-space: normal;
  width: 22.5rem;
}
.filter .flatpickr {
  display: flex;
  height: 100%;
}
.filter .flatpickr-label {
  left: 1.25rem;
  pointer-events: none;
  position: absolute;
  top: 0.5rem;
}
.filter .flatpickr-input {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.6 1.6H12V.8a.8.8 0 0 0-1.6 0v.8H5.6V.8A.8.8 0 0 0 4 .8v.8H2.4A2.4 2.4 0 0 0 0 4v9.6A2.4 2.4 0 0 0 2.4 16h11.2a2.4 2.4 0 0 0 2.4-2.4V4a2.4 2.4 0 0 0-2.4-2.4Zm.8 12a.8.8 0 0 1-.8.8H2.4a.8.8 0 0 1-.8-.8V8h12.8v5.6Zm0-7.2H1.6V4a.8.8 0 0 1 .8-.8H4V4a.8.8 0 0 0 1.6 0v-.8h4.8V4A.8.8 0 0 0 12 4v-.8h1.6a.8.8 0 0 1 .8.8v2.4Z' fill='%23566171'/%3E%3C/svg%3E");
  background-position: bottom 0.75rem right 1rem;
  border-radius: 0.75rem;
  padding-top: 1.5625rem;
}
.filter .flatpickr-input:focus-visible,
.filter .flatpickr-input:hover {
  border-color: #a4b4cb;
}
.filter .flatpickr-input.active {
  background-color: #edf5ff;
}
.pageview {
  --pageview-title-width: 9.375rem;
}
@media (min-width: 768px) {
  .pageview {
    --pageview-title-width: 11.25rem;
  }
  .pageview-title {
    margin-left: var(--pageview-title-width);
  }
}
.pageview-title-2 {
  background-color: #a4b4cb;
  color: #566171;
  padding: 0.25rem 0.5rem;
}
@media (min-width: 768px) {
  .pageview-title-2 {
    margin-left: var(--pageview-title-width);
  }
}
.pageview .table-responsive {
  max-height: 13.5625rem;
}
.pageview .table {
  --bs-table-border-color: var(--bs-white);
}
.pageview .table thead td,
.pageview .table thead th,
.pageview .table thead tr {
  border-color: transparent;
}
.pageview .table thead td,
.pageview .table thead th {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 3;
}
.pageview .table thead td:first-child,
.pageview .table thead th:first-child {
  z-index: 4;
}
.pageview .table thead td:before,
.pageview .table thead th:before {
  background-color: #fff;
  bottom: -1px;
  content: "";
  left: -1px;
  pointer-events: none;
  position: absolute;
  right: -1px;
  top: -1px;
  z-index: -1;
}
@media (min-width: 768px) {
  .pageview .table td,
  .pageview .table th {
    min-width: 6.5625rem;
  }
}
.pageview .table td:first-child,
.pageview .table th:first-child {
  min-width: var(--pageview-title-width);
}
@media (min-width: 768px) {
  .pageview .table td:first-child,
  .pageview .table th:first-child {
    background-color: #fff;
    left: 0;
    position: sticky;
    text-align: left;
    z-index: 0;
  }
  .pageview .table td:first-child:before,
  .pageview .table th:first-child:before {
    background-color: #fff;
    bottom: -1px;
    content: "";
    left: -1px;
    pointer-events: none;
    position: absolute;
    right: -1px;
    top: -1px;
    z-index: -1;
  }
}
.pageview {
  --pageview-tr-hover-border-color: #0c8809;
  --pageview-tr-hover-box-shadow: 0px 4px 12px 0px rgba(12, 136, 9, 0.12);
}
.pageview .table tbody:before {
  content: "";
  display: block;
  height: 1px;
  pointer-events: none;
}
.pageview .table tbody tr:hover td,
.pageview .table tbody tr:hover th {
  position: relative;
  z-index: 1;
}
.pageview .table tbody tr:hover td:before,
.pageview .table tbody tr:hover th:before {
  border-bottom: 1px solid var(--pageview-tr-hover-border-color);
  border-top: 1px solid var(--pageview-tr-hover-border-color);
  bottom: -1px;
  box-shadow: var(--pageview-tr-hover-box-shadow);
  content: "";
  left: -1px;
  position: absolute;
  right: -1px;
  top: -1px;
  z-index: -1;
}
.pageview .table tbody tr:hover td:first-child,
.pageview .table tbody tr:hover th:first-child {
  position: sticky;
  z-index: 2;
}
.pageview .table tbody tr:hover td:first-child:before,
.pageview .table tbody tr:hover th:first-child:before {
  border-left: 1px solid var(--pageview-tr-hover-border-color);
}
.pageview .table tbody tr:hover td:last-child:before,
.pageview .table tbody tr:hover th:last-child:before {
  border-right: 1px solid var(--pageview-tr-hover-border-color);
}
.pageview-orange {
  --pageview-tr-hover-border-color: #ff6d19;
  --pageview-tr-hover-box-shadow: 0px 4px 12px 0px rgba(255, 109, 25, 0.12);
}
.table-green {
  --bs-table-bg: #54d6be;
  color: var(--bs-table-color);
}
.table-green-50 {
  --bs-table-bg: rgba(84, 214, 190, 0.5);
  color: var(--bs-table-color);
}
.table-green-16 {
  --bs-table-bg: rgba(84, 214, 190, 0.16);
  color: var(--bs-table-color);
}
.table-pink {
  --bs-table-bg: #ea7ed2;
}
.table-pink,
.table-pink-50 {
  color: var(--bs-table-color);
}
.table-pink-50 {
  --bs-table-bg: rgba(234, 126, 210, 0.5);
}
.table-pink-16 {
  --bs-table-bg: rgba(234, 126, 210, 0.16);
  color: var(--bs-table-color);
}
.min-w-220px {
  min-width: 13.75rem !important;
}
.page-login {
  background-color: #24346a;
  padding: 0;
}
.page-login .page-main {
  align-items: center;
  display: flex;
  justify-content: center;
  margin-top: 0;
  padding: 3rem 0.75rem;
}
.login {
  color: var(--bs-white);
  margin-left: auto;
  margin-right: auto;
  max-width: 30rem;
  width: 100%;
}
.login a {
  --bs-link-color: var(--bs-white);
  --bs-link-hover-color: rgba(var(--bs-white-rgb), 0.7);
}
.login-logo {
  display: block;
  margin-bottom: 2.5rem;
  width: 12.375rem;
}
@media (max-width: 767.98px) {
  .login-logo {
    width: 12.5rem;
  }
}
.login-body {
  background-color: var(--bs-white);
  border-radius: 0.75rem;
  color: var(--bs-body-color);
  padding: 1.875rem;
}
@media (max-width: 575.98px) {
  .login-body {
    padding: 1.5rem;
  }
}
.login-tab .nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-font-size: calc(1.25625rem + 0.075vw);
  --bs-nav-link-color: #bdbdbd;
  --bs-nav-link-font-weight: bold;
  border-bottom: 1px solid #e0e0e0;
  justify-content: space-evenly;
}
@media (min-width: 1200px) {
  .login-tab .nav {
    --bs-nav-link-font-size: 1.3125rem;
  }
}
@media (max-width: 767.98px) {
  .login-tab .nav {
    --bs-nav-link-font-size: 1.125rem;
  }
}
.login-tab .nav-link.active {
  border-bottom: 3px solid var(--bs-primary);
  color: var(--bs-primary);
}
@media (max-width: 991.98px) {
  .header-2 .header-logo {
    position: static;
    transform: none;
  }
}
.header-link-baogia {
  line-height: 1;
  white-space: nowrap;
}
@media (min-width: 1200px) {
  .header-link-baogia {
    font-size: 0.9375rem;
  }
}
@media (max-width: 991.98px) {
  .header-link-baogia {
    font-size: 0.625rem;
  }
}
.box-table-baogia {
  --box-padding-x: 1.25rem;
  --box-padding-y: 1.25rem;
}
.table-td-node {
  width: 26.4705882353%;
}
.table > .table-head-hidden td,
.table > .table-head-hidden th {
  border: none;
  height: 0;
  padding: 0;
}
@media (min-width: 992px) {
  .header-filter-price {
    width: 6.25rem;
  }
}
@media (max-width: 991.98px) {
  .header-3 .header-logo {
    position: static;
    transform: none;
  }
}
@media (min-width: 1200px) {
  .container-xl-1280px {
    max-width: calc(1280px + var(--bs-gutter-x)) !important;
  }
}
.footer-2 {
  --footer-bg: transparent;
  --footer-color: #141735;
  font-size: 0.875rem;
}
.footer-2 .footer-logo {
  display: inline-block;
  width: 9.375rem;
}
.form-kpi {
  --form-horizontal-label-width: 6.75rem;
  --form-horizontal-body-width: none;
}
@media (min-width: 992px) {
  .form-kpi {
    --form-horizontal-label-width: auto;
  }
}

/* .box .nav-tabs .nav-link */
/* --form-horizontal-label-width: 5rem; */
/* col-form-body */
/* col-form-text */